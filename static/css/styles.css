/* Custom styles for the Segling application */

:root {
    --body-bg: #ffffff;
    --body-color: #212529;
    --card-bg: #ffffff;
    --card-border: rgba(0, 0, 0, 0.125);
    --input-bg: #ffffff;
    --input-color: #212529;
    --input-border: #ced4da;
    --table-bg: #ffffff;
    --table-border: #dee2e6;
    --table-striped-bg: rgba(0, 0, 0, 0.15);
    --dropdown-bg: #ffffff;
    --dropdown-border: rgba(0, 0, 0, 0.15);
    --dropdown-hover-bg: #f8f9fa;
    --footer-bg: #f8f9fa;
}

/* Apply background color to html element to prevent white flash */
html {
    background-color: var(--body-bg);
}

[data-bs-theme="dark"] {
    --body-bg: #212529;
    --body-color: #f8f9fa;
    --card-bg: #343a40;
    --card-border: rgba(255, 255, 255, 0.125);
    --input-bg: #343a40;
    --input-color: #f8f9fa;
    --input-border: #495057;
    --table-bg: #343a40;
    --table-border: #495057;
    --table-striped-bg: rgba(255, 255, 255, 0.15);
    --dropdown-bg: #343a40;
    --dropdown-border: rgba(255, 255, 255, 0.15);
    --dropdown-hover-bg: #495057;
    --footer-bg: #343a40;
}

body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--body-bg);
    color: var(--body-color);
    /* No transition by default to prevent flash on page load */
    transition: none;
}

/* Only apply transitions after the page has fully loaded */
body.theme-transition-ready {
    transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
    flex: 1;
}

/* Dark mode overrides */
[data-bs-theme="dark"] .card {
    background-color: var(--card-bg);
    border-color: var(--card-border);
}

[data-bs-theme="dark"] .form-control,
[data-bs-theme="dark"] .form-select {
    background-color: var(--input-bg);
    color: var(--input-color);
    border-color: var(--input-border);
}

[data-bs-theme="dark"] .table {
    color: var(--body-color);
}

[data-bs-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) {
    background-color: var(--table-striped-bg);
}

[data-bs-theme="dark"] .table-bordered {
    border-color: var(--table-border);
}

[data-bs-theme="dark"] .bg-light {
    background-color: var(--footer-bg) !important;
}

/* Footer theme that adapts to light/dark mode */
.footer-theme {
    background-color: #f8f9fa; /* Light gray in light mode */
    color: #212529;
}

[data-bs-theme="dark"] .footer-theme {
    background-color: #343a40; /* Dark gray in dark mode */
    color: #f8f9fa;
}

/* Fix for year filter in events page */
[data-bs-theme="dark"] .year-filter .bg-light {
    background-color: var(--card-bg) !important;
    color: var(--body-color) !important;
}

/* Fix for published results section in settings page */
[data-bs-theme="dark"] .p-3.bg-light.rounded {
    background-color: var(--card-bg) !important;
    color: var(--body-color) !important;
}

/* Custom table header theme that adapts to light/dark mode */
.table-header-theme {
    background-color: #f8f9fa; /* Light gray in light mode */
    color: #212529;
}

.table-header-theme th {
    background-color: #f8f9fa; /* Light gray in light mode */
    color: #212529;
}

/* Dark mode version */
[data-bs-theme="dark"] .table-header-theme,
[data-bs-theme="dark"] .table-header-theme th {
    background-color: #2c3136 !important; /* Darker than card-bg for better contrast */
    color: #f8f9fa !important;
    border-bottom-color: #495057 !important;
}

/* Theme toggle button */
.theme-toggle {
    cursor: pointer;
    padding: 0.5rem;
    display: flex;
    align-items: center;
}

.theme-toggle i {
    font-size: 1.25rem;
}

/* HTMX indicator styles */
.htmx-indicator {
    display: none;
}

.htmx-request .htmx-indicator {
    display: inline;
}

.htmx-request.htmx-indicator {
    display: inline;
}

/* Table styles */
.table-responsive {
    max-height: 70vh;
    overflow-y: auto;
}

.sticky-top {
    position: sticky;
    top: 0;
    z-index: 1;
}

/* Fade out deleted rows */
tr.htmx-swapping {
    opacity: 0;
    transition: opacity 1s ease-out;
}

/* Scrollable events list */
.events-list-container {
    scrollbar-width: thin;
    scrollbar-color: var(--input-border) transparent;
}

.events-list-container::-webkit-scrollbar {
    width: 6px;
}

.events-list-container::-webkit-scrollbar-track {
    background: transparent;
}

.events-list-container::-webkit-scrollbar-thumb {
    background-color: var(--input-border);
    border-radius: 6px;
}

.events-list-container .list-group-item {
    border-left: none;
    border-right: none;
}

.events-list-container .list-group-item:first-child {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.events-list-container .list-group-item:last-child {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

/* Styles for the mätbrevs nummer search dropdown */
#matbrevs-search-results {
    display: none;
    position: absolute;
    z-index: 1000;
    background-color: var(--dropdown-bg);
    border: 1px solid var(--dropdown-border);
    border-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
    width: 100%;
    margin-top: 0.25rem;
}

#matbrevs-search-results.show {
    display: block;
}

#matbrevs-search-results .dropdown-item {
    padding: 0.5rem 1rem;
    clear: both;
    font-weight: 400;
    color: var(--body-color);
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
}

#matbrevs-search-results .dropdown-item:hover,
#matbrevs-search-results .dropdown-item:focus {
    color: var(--body-color);
    text-decoration: none;
    background-color: var(--dropdown-hover-bg);
}

/* Styles for the action buttons in the events table */
.action-buttons {
    width: 120px;
    min-width: 120px;
    max-width: 120px;
}

.action-buttons .btn {
    margin-bottom: 4px;
}

/* Participant count functionality has been removed */

/* Custom time input styles for Safari */
.time-input-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
}

.time-input-wrapper input[type="text"] {
    background-color: var(--input-bg);
    color: var(--input-color);
    border: 1px solid var(--input-border);
    border-radius: 0.25rem;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    width: 100%;
}

.time-input-wrapper input[type="text"]:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

[data-bs-theme="dark"] .time-input-wrapper input[type="text"] {
    background-color: var(--input-bg);
    color: var(--input-color);
    border-color: var(--input-border);
}
