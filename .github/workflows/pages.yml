name: Deploy Documentation

on:
  workflow_dispatch:

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: write
  pages: write
  id-token: write

jobs:
  # First job to check if GitHub Pages is enabled
  check_pages:
    name: Check GitHub Pages Status
    runs-on: ubuntu-latest
    outputs:
      pages_enabled: ${{ steps.check.outputs.pages_enabled }}
    steps:
      - name: Check if GitHub Pages is enabled
        id: check
        run: |
          # Try to get the GitHub Pages status
          STATUS=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            "https://api.github.com/repos/${{ github.repository }}/pages")

          if [ "$STATUS" == "200" ]; then
            echo "GitHub Pages is enabled"
            echo "pages_enabled=true" >> $GITHUB_OUTPUT
          else
            echo "GitHub Pages is not enabled. Please enable it in repository settings."
            echo "pages_enabled=false" >> $GITHUB_OUTPUT
          fi

  # Main deployment job
  deploy:
    name: Deploy Documentation
    needs: check_pages
    if: needs.check_pages.outputs.pages_enabled == 'true'
    runs-on: ubuntu-latest
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.21'
          cache: true

      - name: Install dependencies
        run: go mod download

      - name: Generate documentation
        run: |
          # Create necessary directories
          mkdir -p docs
          mkdir -p docs/_layouts

          # Copy documentation files
          cp README.md docs/index.md
          cp RELEASE_NOTES.md docs/release-notes.md

          # Copy additional documentation files if they exist
          if [ -f docs/ENTYPSEGLING.md ]; then
            cp docs/ENTYPSEGLING.md docs/entypsegling.md
          fi

          # Check if Segling_PRD.md exists before copying
          if [ -f Segling_PRD.md ]; then
            cp Segling_PRD.md docs/prd.md
          else
            echo "# Product Requirements Document" > docs/prd.md
            echo "" >> docs/prd.md
            echo "This document is not available in the repository." >> docs/prd.md
          fi

          # Check if PRD exists
          HAS_PRD=$([ -f Segling_PRD.md ] && echo "true" || echo "false")

          # Create a simple navigation file
          cat > docs/_config.yml << EOF
          title: Segling
          description: A sailing competition management application
          theme: jekyll-theme-cayman
          has_prd: ${HAS_PRD}
          EOF

          # Create a navigation file
          cat > docs/_layouts/default.html << EOF
          <!DOCTYPE html>
          <html lang="{{ site.lang | default: "en-US" }}">
            <head>
              <meta charset="UTF-8">
              <meta http-equiv="X-UA-Compatible" content="IE=edge">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              {% seo %}
              <link rel="stylesheet" href="{{ "/assets/css/style.css?v=" | append: site.github.build_revision | relative_url }}">
            </head>
            <body>
              <header class="page-header" role="banner">
                <h1 class="project-name">{{ site.title | default: site.github.repository_name }}</h1>
                <h2 class="project-tagline">{{ site.description | default: site.github.project_tagline }}</h2>
                <a href="{{ site.baseurl }}/" class="btn">Home</a>
                {% if site.has_prd == "true" %}
                <a href="{{ site.baseurl }}/prd" class="btn">PRD</a>
                {% endif %}
                <a href="{{ site.baseurl }}/release-notes" class="btn">Release Notes</a>
                <a href="{{ site.github.repository_url }}" class="btn">View on GitHub</a>
              </header>
              <main id="content" class="main-content" role="main">
                {{ content }}
                <footer class="site-footer">
                  <span class="site-footer-owner"><a href="{{ site.github.repository_url }}">{{ site.github.repository_name }}</a> is maintained by <a href="{{ site.github.owner_url }}">{{ site.github.owner_name }}</a>.</span>
                  <span class="site-footer-credits">This page was generated by <a href="https://pages.github.com">GitHub Pages</a>.</span>
                </footer>
              </main>
            </body>
          </html>
          EOF

      - name: Setup Pages
        uses: actions/configure-pages@v4

      - name: Upload Pages Artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: 'docs'

      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4

  # Try to enable GitHub Pages
  enable_pages:
    name: Try to Enable GitHub Pages
    needs: check_pages
    if: needs.check_pages.outputs.pages_enabled == 'false'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Try to enable GitHub Pages
        run: |
          # Try to enable GitHub Pages using the API
          RESPONSE=$(curl -s -X POST \
            -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            -d '{"source":{"branch":"gh-pages","path":"/"}}' \
            "https://api.github.com/repos/${{ github.repository }}/pages")

          if [[ "$RESPONSE" == *"GitHub Pages is already enabled for this repository"* ]]; then
            echo "GitHub Pages is already enabled but not configured correctly."
          elif [[ "$RESPONSE" == *"created"* ]]; then
            echo "Successfully enabled GitHub Pages!"
          else
            echo "Could not enable GitHub Pages automatically. Manual setup required."
            echo "Response: $RESPONSE"
          fi

          # Always exit with success to continue the workflow
          exit 0

  # Fallback job when GitHub Pages is not enabled
  fallback:
    name: Generate Documentation (GitHub Pages Not Enabled)
    needs: [check_pages, enable_pages]
    if: needs.check_pages.outputs.pages_enabled == 'false'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.21'
          cache: true

      - name: Install dependencies
        run: go mod download

      - name: Generate documentation
        run: |
          # Create necessary directories
          mkdir -p docs
          mkdir -p docs/_layouts

          # Copy documentation files
          cp README.md docs/index.md
          cp RELEASE_NOTES.md docs/release-notes.md

          # Copy additional documentation files if they exist
          if [ -f docs/ENTYPSEGLING.md ]; then
            cp docs/ENTYPSEGLING.md docs/entypsegling.md
          fi

          # Check if Segling_PRD.md exists before copying
          if [ -f Segling_PRD.md ]; then
            cp Segling_PRD.md docs/prd.md
          else
            echo "# Product Requirements Document" > docs/prd.md
            echo "" >> docs/prd.md
            echo "This document is not available in the repository." >> docs/prd.md
          fi

          # Check if PRD exists
          HAS_PRD=$([ -f Segling_PRD.md ] && echo "true" || echo "false")

          # Create a simple navigation file
          cat > docs/_config.yml << EOF
          title: Segling
          description: A sailing competition management application
          theme: jekyll-theme-cayman
          has_prd: ${HAS_PRD}
          EOF

          # Create a navigation file
          cat > docs/_layouts/default.html << EOF
          <!DOCTYPE html>
          <html lang="{{ site.lang | default: "en-US" }}">
            <head>
              <meta charset="UTF-8">
              <meta http-equiv="X-UA-Compatible" content="IE=edge">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              {% seo %}
              <link rel="stylesheet" href="{{ "/assets/css/style.css?v=" | append: site.github.build_revision | relative_url }}">
            </head>
            <body>
              <header class="page-header" role="banner">
                <h1 class="project-name">{{ site.title | default: site.github.repository_name }}</h1>
                <h2 class="project-tagline">{{ site.description | default: site.github.project_tagline }}</h2>
                <a href="{{ site.baseurl }}/" class="btn">Home</a>
                {% if site.has_prd == "true" %}
                <a href="{{ site.baseurl }}/prd" class="btn">PRD</a>
                {% endif %}
                <a href="{{ site.baseurl }}/release-notes" class="btn">Release Notes</a>
                <a href="{{ site.github.repository_url }}" class="btn">View on GitHub</a>
              </header>
              <main id="content" class="main-content" role="main">
                {{ content }}
                <footer class="site-footer">
                  <span class="site-footer-owner"><a href="{{ site.github.repository_url }}">{{ site.github.repository_name }}</a> is maintained by <a href="{{ site.github.owner_url }}">{{ site.github.owner_name }}</a>.</span>
                  <span class="site-footer-credits">This page was generated by <a href="https://pages.github.com">GitHub Pages</a>.</span>
                </footer>
              </main>
            </body>
          </html>
          EOF

      - name: Upload documentation as artifact
        uses: actions/upload-artifact@v4
        with:
          name: documentation
          path: docs/
          retention-days: 7

      - name: Display GitHub Pages setup instructions
        run: |
          echo "::notice::GitHub Pages is not enabled for this repository. To enable it:"
          echo "::notice::1. Go to repository Settings > Pages"
          echo "::notice::2. Under 'Source', select 'GitHub Actions'"
          echo "::notice::3. Click 'Save'"
          echo "::notice::Once enabled, this workflow will automatically deploy the documentation."
