name: Build and Test

on:
  workflow_dispatch:
  push:
    tags:
      - 'v*'

# Sets permissions for all jobs
permissions:
  contents: write  # This is required for creating releases and uploading assets

jobs:
  build:
    name: Build and Test
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        go-version: ['1.21']

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: ${{ matrix.go-version }}
        cache: true

    - name: Install dependencies
      run: go mod download

    - name: Build
      run: go build -v ./cmd/...

    - name: Test
      run: go test -v ./pkg/... ./cmd/...

  release:
    name: Create Release
    needs: build
    if: startsWith(github.ref, 'refs/tags/')
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.21'
          cache: true

      - name: Install dependencies
        run: go mod download

      - name: Set version from tag
        id: get_version
        run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_ENV

      - name: Build for multiple platforms
        run: |
          # Clean up any existing dist directory
          echo "Cleaning up existing dist directory..."
          rm -rf dist
          mkdir -p dist

          # Build executables for each platform
          echo "Building executables for multiple platforms..."
          GOOS=linux GOARCH=amd64 go build -ldflags="-X 'main.Version=${{ env.VERSION }}'" -o dist/segling-linux-amd64 ./cmd/server
          GOOS=darwin GOARCH=amd64 go build -ldflags="-X 'main.Version=${{ env.VERSION }}'" -o dist/segling-darwin-amd64 ./cmd/server
          GOOS=darwin GOARCH=arm64 go build -ldflags="-X 'main.Version=${{ env.VERSION }}'" -o dist/segling-darwin-arm64 ./cmd/server
          GOOS=windows GOARCH=amd64 go build -ldflags="-X 'main.Version=${{ env.VERSION }}'" -o dist/segling-windows-amd64.exe ./cmd/server

          # Create package directories for each platform
          echo "Creating package directories..."
          mkdir -p dist/package/segling-linux-amd64
          mkdir -p dist/package/segling-darwin-amd64
          mkdir -p dist/package/segling-darwin-arm64
          mkdir -p dist/package/segling-windows-amd64

          # Create required subdirectories for each platform
          for platform in linux-amd64 darwin-amd64 darwin-arm64 windows-amd64; do
            echo "Creating subdirectories for $platform..."
            mkdir -p dist/package/segling-$platform/static
            mkdir -p dist/package/segling-$platform/pkg/templates
            mkdir -p dist/package/segling-$platform/backups
            mkdir -p dist/package/segling-$platform/certs
            mkdir -p dist/package/segling-$platform/certs/export
            mkdir -p dist/package/segling-$platform/certs/client
            mkdir -p dist/package/segling-$platform/credentials
            mkdir -p dist/package/segling-$platform/scripts
            mkdir -p dist/package/segling-$platform/docs
          done

          # Copy static files to each platform
          echo "Copying static files..."
          for platform in linux-amd64 darwin-amd64 darwin-arm64 windows-amd64; do
            cp -r static/* dist/package/segling-$platform/static/
          done

          # Copy templates to each platform
          echo "Copying templates..."
          for platform in linux-amd64 darwin-amd64 darwin-arm64 windows-amd64; do
            cp -r pkg/templates/* dist/package/segling-$platform/pkg/templates/
          done

          # Copy documentation to each platform
          echo "Copying documentation..."
          # Copy README to both root and docs directory for backward compatibility
          cp README.md dist/package/segling-linux-amd64/
          cp README.md dist/package/segling-darwin-amd64/
          cp README.md dist/package/segling-darwin-arm64/
          cp README.md dist/package/segling-windows-amd64/

          cp README.md dist/package/segling-linux-amd64/docs/
          cp README.md dist/package/segling-darwin-amd64/docs/
          cp README.md dist/package/segling-darwin-arm64/docs/
          cp README.md dist/package/segling-windows-amd64/docs/

          # Copy RELEASE_NOTES to both root and docs directory for backward compatibility
          cp RELEASE_NOTES.md dist/package/segling-linux-amd64/
          cp RELEASE_NOTES.md dist/package/segling-darwin-amd64/
          cp RELEASE_NOTES.md dist/package/segling-darwin-arm64/
          cp RELEASE_NOTES.md dist/package/segling-windows-amd64/

          cp RELEASE_NOTES.md dist/package/segling-linux-amd64/docs/
          cp RELEASE_NOTES.md dist/package/segling-darwin-amd64/docs/
          cp RELEASE_NOTES.md dist/package/segling-darwin-arm64/docs/
          cp RELEASE_NOTES.md dist/package/segling-windows-amd64/docs/

          if [ -f "docs/CREDENTIALS.md" ]; then
            cp docs/CREDENTIALS.md dist/package/segling-linux-amd64/docs/
            cp docs/CREDENTIALS.md dist/package/segling-darwin-amd64/docs/
            cp docs/CREDENTIALS.md dist/package/segling-darwin-arm64/docs/
            cp docs/CREDENTIALS.md dist/package/segling-windows-amd64/docs/
          fi

          if [ -f "docs/ENTYPSEGLING.md" ]; then
            cp docs/ENTYPSEGLING.md dist/package/segling-linux-amd64/docs/
            cp docs/ENTYPSEGLING.md dist/package/segling-darwin-amd64/docs/
            cp docs/ENTYPSEGLING.md dist/package/segling-darwin-arm64/docs/
            cp docs/ENTYPSEGLING.md dist/package/segling-windows-amd64/docs/
          fi

          # Copy scripts if they exist
          if [ -d "scripts" ]; then
            echo "Copying scripts..."
            for platform in linux-amd64 darwin-amd64 darwin-arm64; do
              cp scripts/*.sh dist/package/segling-$platform/scripts/ 2>/dev/null || true
              cp scripts/*.go dist/package/segling-$platform/scripts/ 2>/dev/null || true
              chmod +x dist/package/segling-$platform/scripts/*.sh 2>/dev/null || true
            done

            # For Windows, copy with .bat extension
            cp scripts/*.bat dist/package/segling-windows-amd64/scripts/ 2>/dev/null || true
            cp scripts/*.go dist/package/segling-windows-amd64/scripts/ 2>/dev/null || true
          fi

          # Create empty database file for each platform
          echo "Creating empty database files..."
          touch dist/package/segling-linux-amd64/segling.db
          touch dist/package/segling-darwin-amd64/segling.db
          touch dist/package/segling-darwin-arm64/segling.db
          touch dist/package/segling-windows-amd64/segling.db

          # Create README.txt for each platform
          echo "Creating README files..."
          for platform in linux-amd64 darwin-amd64 darwin-arm64 windows-amd64; do
            cat > dist/package/segling-$platform/README.txt << EOF
          Segling Application v${{ env.VERSION }}
          ==============================

          This is a sailing competition management application.

          Quick Start:
          1. Ensure you have the necessary permissions to run the executable
             chmod +x segling (for Linux/macOS)
          2. Run the application
             ./segling (Linux/macOS) or segling-windows-amd64.exe (Windows)
          3. Open your browser and navigate to:
             http://localhost:8080

          Enabling HTTPS (Secure Connection):
          1. Generate self-signed certificates (for development)
             ./scripts/generate_certs.sh (Linux/macOS)
          2. Run the application with TLS enabled
             ./segling -tls -cert certs/cert.pem -key certs/key.pem
          3. Open your browser and navigate to:
             https://localhost:8080
             (Note: Browsers will show a warning about self-signed certificates)

          Directory Structure:
          - segling/segling.exe: The main executable
          - pkg/templates/: HTML templates
          - static/: CSS, JavaScript, and other static assets
          - backups/: Directory for database backups
          - certs/: Directory for TLS certificates
          - credentials/: Directory for sensitive credentials (not included in backups)
          - scripts/: Utility scripts
          - docs/: Documentation
          - segling.db: SQLite database file

          Notes:
          - The application will create and initialize the database on first run
          - Backups will be stored in the backups/ directory
          - The application runs on port 8080 by default
          - Default club for new sailors can be configured in Settings
          - Dark/light mode toggle is available in the top navigation bar

          For more information, visit: https://github.com/rogge66/sailapp
          EOF
          done

          # Create start scripts for Linux and macOS
          echo "Creating start scripts for Linux and macOS..."
          for platform in linux-amd64 darwin-amd64 darwin-arm64; do
            cat > dist/package/segling-$platform/start.sh << EOF
          #!/bin/bash
          # Start script for Segling application

          # Make sure the executable has the right permissions
          chmod +x segling

          # Run the application
          ./segling

          # If the application exits, wait for user input before closing the terminal
          echo "Application has stopped. Press Enter to exit."
          read
          EOF

            chmod +x dist/package/segling-$platform/start.sh

            cat > dist/package/segling-$platform/start-secure.sh << EOF
          #!/bin/bash
          # Start script for Segling application with HTTPS enabled

          # Check if certificates exist
          if [ ! -f "certs/cert.pem" ] || [ ! -f "certs/key.pem" ]; then
              echo "TLS certificates not found. Generating self-signed certificates..."
              ./scripts/generate_certs.sh
          fi

          # Make sure the executable has the right permissions
          chmod +x segling

          # Run the application with TLS enabled
          ./segling -tls -cert certs/cert.pem -key certs/key.pem

          # If the application exits, wait for user input before closing the terminal
          echo "Application has stopped. Press Enter to exit."
          read
          EOF

            chmod +x dist/package/segling-$platform/start-secure.sh
          done

          # Create batch files for Windows
          echo "Creating batch files for Windows..."
          cat > dist/package/segling-windows-amd64/start.bat << EOF
          @echo off
          REM Start script for Segling application

          REM Run the application
          segling-windows-amd64.exe

          REM If the application exits, wait for user input before closing the terminal
          echo Application has stopped. Press Enter to exit.
          pause > nul
          EOF

          # Move executables to their respective package directories
          echo "Moving executables to package directories..."
          cp dist/segling-linux-amd64 dist/package/segling-linux-amd64/segling
          cp dist/segling-darwin-amd64 dist/package/segling-darwin-amd64/segling
          cp dist/segling-darwin-arm64 dist/package/segling-darwin-arm64/segling
          cp dist/segling-windows-amd64.exe dist/package/segling-windows-amd64/segling-windows-amd64.exe

          # Create zip archives for each platform
          echo "Creating zip archives..."
          cd dist
          zip -r segling-linux-amd64.zip package/segling-linux-amd64
          zip -r segling-darwin-amd64.zip package/segling-darwin-amd64
          zip -r segling-darwin-arm64.zip package/segling-darwin-arm64
          zip -r segling-windows-amd64.zip package/segling-windows-amd64

          # Copy executables to dist root for individual download
          cp package/segling-linux-amd64/segling segling-linux-amd64
          cp package/segling-darwin-amd64/segling segling-darwin-amd64
          cp package/segling-darwin-arm64/segling segling-darwin-arm64
          cp package/segling-windows-amd64/segling-windows-amd64.exe segling-windows-amd64.exe
          cd ..

      - name: Create release notes
        id: release_notes
        run: |
          VERSION=${{ env.VERSION }}
          VERSION_NO_V=${VERSION#v}
          RELEASE_NOTES=$(awk -v ver="$VERSION_NO_V" '/^## Version/ {if (p) { exit }; if ($3 == "("ver")" || $3 == "("ver")") p=1; next} p' RELEASE_NOTES.md)
          echo "RELEASE_NOTES<<EOF" >> $GITHUB_ENV
          echo "$RELEASE_NOTES" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      - name: Create Release
        id: create_release
        uses: softprops/action-gh-release@v2
        with:
          token: ${{ github.token }}  # Explicitly set the token
          tag_name: ${{ env.VERSION }}
          name: Release ${{ env.VERSION }}
          body: ${{ env.RELEASE_NOTES }}
          draft: false
          prerelease: false
          files: |
            dist/segling-linux-amd64.zip
            dist/segling-darwin-amd64.zip
            dist/segling-darwin-arm64.zip
            dist/segling-windows-amd64.zip
            dist/segling-linux-amd64/segling
            dist/segling-darwin-amd64/segling
            dist/segling-darwin-arm64/segling
            dist/segling-windows-amd64/segling-windows-amd64.exe
