-- Sailing Competition Scoring System Test Data
-- This script creates comprehensive test scenarios for the scoring system

-- Test Scenario 1: Basic Tied Finishes
-- Create event for basic tied finishes scenario
INSERT INTO events (namn, datum, starttid, competition_type, entypsegling, boat_type) 
VALUES ('Test Scenario 1: Basic Tied Finishes', '2024-06-15', '14:00', 'Test', 1, 'Laser');

-- Get the event ID (assuming it's the last inserted)
-- Note: In practice, you'd use RETURNING id or similar

-- Create test sailors for Scenario 1
INSERT INTO sailors (namn, klubb) VALUES 
('<PERSON>', 'Test Sailing Club'),
('<PERSON><PERSON><PERSON><PERSON>', 'Test Sailing Club'),
('<PERSON>', 'Test Sailing Club'),
('<PERSON>', 'Test Sailing Club'),
('<PERSON>', 'Test Sailing Club'),
('<PERSON><PERSON>', 'Test Sailing Club');

-- Create participants for Scenario 1 (assuming event_id = 1)
INSERT INTO event_participants (event_id, sailor_id, boat_id, srs_value, personal_number, crew_count) VALUES
(1, 1, NULL, 0, 'SWE-101', 0),
(1, 2, NULL, 0, 'SWE-102', 0),
(1, 3, NULL, 0, 'SWE-103', 0),
(1, 4, NULL, 0, 'SWE-104', 0),
(1, 5, NULL, 0, 'SWE-105', 0),
(1, 6, NULL, 0, 'SWE-106', 0);

-- Create heats for Scenario 1
INSERT INTO heats (event_id, namn, start_time) VALUES
(1, 'Heat 1', '14:00'),
(1, 'Heat 2', '15:00'),
(1, 'Heat 3', '16:00');

-- Heat 1 placements (with tied finishes)
-- Anna: 1st place (1.0p)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (1, 1, 1, 0, 0);
-- Björn & Carl: Tied for 2nd (2.5p each)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (1, 2, 2, 0, 0);
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (1, 3, 2, 0, 0);
-- Diana: 4th place (4.0p)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (1, 4, 4, 0, 0);
-- Erik & Frida: Tied for 5th (5.5p each)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (1, 5, 5, 0, 0);
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (1, 6, 5, 0, 0);

-- Heat 2 placements (3-way tie scenario)
-- Diana: 1st place (1.0p)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (2, 4, 1, 0, 0);
-- Anna, Björn, Erik: 3-way tie for 2nd (3.0p each)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (2, 1, 2, 0, 0);
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (2, 2, 2, 0, 0);
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (2, 5, 2, 0, 0);
-- Carl: 5th place (5.0p)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (2, 3, 5, 0, 0);
-- Frida: 6th place (6.0p)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (2, 6, 6, 0, 0);

-- Heat 3 placements (complex ties)
-- Carl & Frida: Tied for 1st (1.5p each)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (3, 3, 1, 0, 0);
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (3, 6, 1, 0, 0);
-- Erik: 3rd place (3.0p)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (3, 5, 3, 0, 0);
-- Anna & Björn: Tied for 4th (4.5p each)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (3, 1, 4, 0, 0);
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (3, 2, 4, 0, 0);
-- Diana: 6th place (6.0p)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (3, 4, 6, 0, 0);

-- Test Scenario 2: Complete Ties (Shared Positions)
INSERT INTO events (namn, datum, starttid, competition_type, entypsegling, boat_type) 
VALUES ('Test Scenario 2: Shared Positions', '2024-06-16', '14:00', 'Test', 1, 'Optimist');

-- Create sailors for Scenario 2
INSERT INTO sailors (namn, klubb) VALUES 
('Lars Andersson', 'Test Sailing Club'),
('Maria Johansson', 'Test Sailing Club'),
('Nils Petersson', 'Test Sailing Club'),
('Olga Lindström', 'Test Sailing Club'),
('Per Gustafsson', 'Test Sailing Club');

-- Create participants for Scenario 2 (assuming event_id = 2)
INSERT INTO event_participants (event_id, sailor_id, boat_id, srs_value, personal_number, crew_count) VALUES
(2, 7, NULL, 0, 'SWE-201', 0),
(2, 8, NULL, 0, 'SWE-202', 0),
(2, 9, NULL, 0, 'SWE-203', 0),
(2, 10, NULL, 0, 'SWE-204', 0),
(2, 11, NULL, 0, 'SWE-205', 0);

-- Create heats for Scenario 2
INSERT INTO heats (event_id, namn, start_time) VALUES
(2, 'Heat 1', '14:00'),
(2, 'Heat 2', '15:00');

-- Heat 1 placements (Lars & Maria tied for 1st)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (4, 7, 1, 0, 0);  -- Lars: 1.5p
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (4, 8, 1, 0, 0);  -- Maria: 1.5p
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (4, 9, 3, 0, 0);  -- Nils: 3.0p
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (4, 10, 4, 0, 0); -- Olga: 4.0p
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (4, 11, 5, 0, 0); -- Per: 5.0p

-- Heat 2 placements (Lars & Maria tied for 2nd again)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (5, 9, 1, 0, 0);  -- Nils: 1.0p
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (5, 7, 2, 0, 0);  -- Lars: 2.5p
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (5, 8, 2, 0, 0);  -- Maria: 2.5p
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (5, 10, 4, 0, 0); -- Olga: 4.0p
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (5, 11, 5, 0, 0); -- Per: 5.0p

-- Test Scenario 3: DNS/DNF Scenarios
INSERT INTO events (namn, datum, starttid, competition_type, entypsegling, boat_type) 
VALUES ('Test Scenario 3: DNS/DNF Handling', '2024-06-17', '14:00', 'Test', 1, '420');

-- Create sailors for Scenario 3
INSERT INTO sailors (namn, klubb) VALUES 
('Gustav Nord', 'Test Sailing Club'),
('Hanna Väst', 'Test Sailing Club'),
('Ivan Öst', 'Test Sailing Club'),
('Julia Syd', 'Test Sailing Club'),
('Karl Mitt', 'Test Sailing Club'),
('Lisa Hem', 'Test Sailing Club');

-- Create participants for Scenario 3 (assuming event_id = 3)
INSERT INTO event_participants (event_id, sailor_id, boat_id, srs_value, personal_number, crew_count) VALUES
(3, 12, NULL, 0, 'SWE-401', 0),
(3, 13, NULL, 0, 'SWE-402', 0),
(3, 14, NULL, 0, 'SWE-403', 0),
(3, 15, NULL, 0, 'SWE-404', 0),
(3, 16, NULL, 0, 'SWE-405', 0),
(3, 17, NULL, 0, 'SWE-406', 0);

-- Create heats for Scenario 3
INSERT INTO heats (event_id, namn, start_time) VALUES
(3, 'Heat 1', '14:00'),
(3, 'Heat 2', '15:00');

-- Heat 1 placements (with DNS/DNF)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (6, 12, 1, 0, 0);  -- Gustav: 1.0p
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (6, 13, 2, 0, 0);  -- Hanna: 2.0p
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (6, 14, 3, 0, 0);  -- Ivan: 3.0p
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (6, 15, 4, 0, 0);  -- Julia: 4.0p
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (6, 16, 0, 0, 1);  -- Karl: DNF (5.0p)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (6, 17, 0, 1, 0);  -- Lisa: DNS (7.0p)

-- Heat 2 placements (different DNS/DNF pattern)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (7, 16, 1, 0, 0);  -- Karl: 1.0p
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (7, 14, 2, 0, 0);  -- Ivan: 2.0p
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (7, 12, 3, 0, 0);  -- Gustav: 3.0p
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (7, 13, 0, 0, 1);  -- Hanna: DNF (4.0p)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (7, 15, 0, 1, 0);  -- Julia: DNS (6.0p)
INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf) VALUES (7, 17, 0, 1, 0);  -- Lisa: DNS (6.0p)

-- Expected Results Summary:
-- Scenario 1 Final Results:
-- 1st: Anna (8.5p) - 1.0 + 3.0 + 4.5
-- 2nd: Carl (9.0p) - 2.5 + 5.0 + 1.5  
-- 3rd: Björn (10.0p) - 2.5 + 3.0 + 4.5
-- 4th: Diana (11.0p) - 4.0 + 1.0 + 6.0
-- 5th: Erik (11.5p) - 5.5 + 3.0 + 3.0
-- 6th: Frida (13.0p) - 5.5 + 6.0 + 1.5

-- Scenario 2 Final Results (with shared positions):
-- 1st: Nils (4.0p) - 3.0 + 1.0
-- 2nd: Lars (4.0p) - 1.5 + 2.5 (SHARED with Maria)
-- 2nd: Maria (4.0p) - 1.5 + 2.5 (SHARED with Lars)
-- 4th: Olga (8.0p) - 4.0 + 4.0
-- 5th: Per (10.0p) - 5.0 + 5.0

-- Scenario 3 Final Results (DNS/DNF):
-- 1st: Gustav (4.0p) - 1.0 + 3.0
-- 2nd: Ivan (5.0p) - 3.0 + 2.0
-- 3rd: Karl (6.0p) - 5.0(DNF) + 1.0
-- 4th: Hanna (6.0p) - 2.0 + 4.0(DNF)
-- 5th: Julia (10.0p) - 4.0 + 6.0(DNS)
-- 6th: Lisa (13.0p) - 7.0(DNS) + 6.0(DNS)
