# Test Suite for Segling Application

This directory contains documentation for the test suite of the Segling application.

## Test Structure

The tests are organized by package, following Go's standard testing approach:

1. **Database Tests** (`pkg/database/database_test.go`):
   - Tests for database connection and initialization
   - CRUD operations for sailors, boats, and events
   - SRS data operations

2. **Handler Tests** (`pkg/handlers/handlers_test.go`):
   - Tests for API endpoints for sailors, boats, and events
   - Tests for SRS data handling

3. **Utility Tests** (`pkg/utils/srs_fetcher_test.go`):
   - Tests for SRS data fetching and parsing
   - Uses mock HTTP server to simulate SRS website responses

## Running Tests

You can run all tests using the provided script:

```bash
./run_tests.sh
```

Or run specific test packages:

```bash
go test -v ./pkg/database
go test -v ./pkg/handlers
go test -v ./pkg/utils
```

## Test Database

The tests use a separate test database file (`test_segling.db`) to avoid affecting the production database. This file is created and removed during the test process.

## Mock SRS Data

The utility tests use a mock HTTP server to simulate responses from the SRS website. This allows testing the SRS data fetching functions without making actual HTTP requests to the external service.

## Adding New Tests

When adding new features to the application, follow these guidelines for creating tests:

1. Create test functions in the appropriate test file
2. Use descriptive function names that indicate what is being tested
3. Set up any necessary test data
4. Make assertions to verify the expected behavior
5. Clean up any test data or resources

Example test function structure:

```go
func TestFeature(t *testing.T) {
    // Setup
    ...
    
    // Test
    result, err := SomeFunctionToTest()
    
    // Assertions
    if err != nil {
        t.Fatalf("Error: %v", err)
    }
    if result != expectedResult {
        t.Errorf("Expected %v, got %v", expectedResult, result)
    }
    
    // Cleanup
    ...
}
```
