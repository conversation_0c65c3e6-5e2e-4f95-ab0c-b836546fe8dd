#!/bin/bash

# Test script for the real Segling application
# This script tests the application with a test database
# It populates the database with test data and runs tests against all endpoints

# Default values
PORT=0  # 0 means find a free port
TEST_DB="test_segling.db"
VERBOSE=false
SERVER_PID=""
SERVER_URL=""
SERVER_BIN="./segling"
RUN_UNIT_TESTS=true
RUN_INTEGRATION_TESTS=true

# Function to print usage information
print_usage() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -h, --help                 Show this help message"
    echo "  -p, --port PORT            Server port (default: auto-detect free port)"
    echo "  -d, --db FILE              Test database file (default: $TEST_DB)"
    echo "  -b, --bin PATH             Path to server binary (default: $SERVER_BIN)"
    echo "  -v, --verbose              Verbose output"
    echo "  --unit-only                Run only unit tests"
    echo "  --integration-only         Run only integration tests"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            print_usage
            exit 0
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -d|--db)
            TEST_DB="$2"
            shift 2
            ;;
        -b|--bin)
            SERVER_BIN="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --unit-only)
            RUN_UNIT_TESTS=true
            RUN_INTEGRATION_TESTS=false
            shift
            ;;
        --integration-only)
            RUN_UNIT_TESTS=false
            RUN_INTEGRATION_TESTS=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            print_usage
            exit 1
            ;;
    esac
done

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to log verbose messages
log_verbose() {
    if [ "$VERBOSE" = true ]; then
        log "$1"
    fi
}

# Function to find a free port
find_free_port() {
    if [ "$PORT" -ne 0 ]; then
        # Use the specified port
        return
    fi

    # Check if nc command is available
    if command -v nc >/dev/null 2>&1; then
        # Try to find a free port using nc
        for p in $(seq 8000 9000); do
            if ! nc -z localhost $p 2>/dev/null; then
                PORT=$p
                log_verbose "Found free port using nc: $PORT"
                return
            fi
        done
    fi

    # If nc is not available or no free port found, try using /dev/tcp
    if [ "$PORT" -eq 0 ]; then
        for p in $(seq 8000 9000); do
            (echo > /dev/tcp/localhost/$p) >/dev/null 2>&1
            if [ $? -ne 0 ]; then
                PORT=$p
                log_verbose "Found free port using /dev/tcp: $PORT"
                return
            fi
        done
    fi

    # If all else fails, use a random port in the ephemeral range
    if [ "$PORT" -eq 0 ]; then
        PORT=$((RANDOM % 16384 + 49152))
        log_verbose "Using random port in ephemeral range: $PORT"
    fi
}

# Function to check if the server is running
check_server() {
    local max_attempts=10
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        log_verbose "Checking if server is running (attempt $attempt/$max_attempts)..."

        curl -s -o /dev/null -w "%{http_code}" "$SERVER_URL" > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            log "Server is running at $SERVER_URL"
            return 0
        fi

        sleep 1
        ((attempt++))
    done

    log "Error: Server is not running at $SERVER_URL after $max_attempts attempts"
    return 1
}

# Function to start the server
start_server() {
    # Find a free port
    find_free_port

    # Set the server URL
    SERVER_URL="http://localhost:$PORT"

    # Remove existing test database if it exists
    if [ -f "$TEST_DB" ]; then
        log "Removing existing test database: $TEST_DB"
        rm "$TEST_DB"
    fi

    # Start the server
    log "Starting server on port $PORT with database $TEST_DB"
    $SERVER_BIN -port $PORT -db $TEST_DB &
    SERVER_PID=$!

    # Wait for server to start
    sleep 2

    # Check if server process is still running
    if ! ps -p $SERVER_PID > /dev/null; then
        log "Error: Server process died immediately"
        exit 1
    fi

    # Check if server is responding
    if ! check_server; then
        log "Error: Server is not responding"
        stop_server
        exit 1
    fi

    log "Server started with PID: $SERVER_PID at $SERVER_URL"
}

# Function to build the server executable
build_server() {
    log "Building server executable..."

    # Check if Go is installed
    if ! command -v go &> /dev/null; then
        log "Error: Go is not installed. Please install Go to build this application."
        exit 1
    fi

    # Build the server executable
    if go build -o "$SERVER_BIN" ./cmd/server; then
        log "✅ Server executable built successfully: $SERVER_BIN"
    else
        log "❌ Failed to build server executable"
        exit 1
    fi
}

# Function to stop the server
stop_server() {
    if [ -n "$SERVER_PID" ]; then
        log "Stopping server with PID: $SERVER_PID"
        kill $SERVER_PID
        wait $SERVER_PID 2>/dev/null
        log "Server stopped"
    fi
}

# Function to test an endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local expected_status=$3
    local description=$4
    local data=$5

    log_verbose "Testing $description: $method $endpoint"

    local response
    local status

    if [ "$method" = "GET" ]; then
        response=$(curl -s -o /dev/null -w "%{http_code}" -X GET "$SERVER_URL$endpoint")
    elif [ "$method" = "POST" ]; then
        if [ -z "$data" ]; then
            response=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$SERVER_URL$endpoint")
        else
            response=$(curl -s -o /dev/null -w "%{http_code}" -X POST -d "$data" "$SERVER_URL$endpoint")
        fi
    elif [ "$method" = "DELETE" ]; then
        response=$(curl -s -o /dev/null -w "%{http_code}" -X DELETE "$SERVER_URL$endpoint")
    fi

    status=$response

    if [ "$status" -eq "$expected_status" ]; then
        log "✅ $description: $method $endpoint - Status: $status"
        log_verbose "Test passed: $description"
        return 0
    else
        log "❌ $description: $method $endpoint - Expected: $expected_status, Got: $status"
        log_verbose "Test failed: $description"
        return 1
    fi
}

# Function to create test data
create_test_data() {
    log "Creating test data..."

    # Create a sailor
    log_verbose "Creating test sailor"
    sailor_data="namn=Test Sailor&telefon=1234567890&klubb=LSS"
    sailor_response=$(curl -s -X POST -d "$sailor_data" "$SERVER_URL/sailors")

    # Create a boat
    log_verbose "Creating test boat"
    boat_data="namn=Test Boat&battyp=Test Type&srs=1.0&srs_utan_undanvindsegel=0.95&srs_shorthanded=0.9&srs_shorthanded_utan_undanvindsegel=0.85&nationality=SWE&segelnummer=1234"
    boat_response=$(curl -s -X POST -d "$boat_data" "$SERVER_URL/boats")

    # Create an event
    log_verbose "Creating test event"
    event_data="namn=Test Event&datum=$(date +%Y-%m-%d)&starttid=12:00&vind=5&banlangd=10&jaktstart_type=regular&beskrivning=Test event for automated testing"
    event_response=$(curl -s -X POST -d "$event_data" "$SERVER_URL/events")

    # Get the event ID
    event_id=$(curl -s "$SERVER_URL/api/events" | grep -o '"ID":[0-9]*' | head -1 | cut -d':' -f2)

    if [ -n "$event_id" ]; then
        log "Created test event with ID: $event_id"

        # Add a participant to the event
        log_verbose "Adding test participant to event"
        sailor_id=$(curl -s "$SERVER_URL/api/sailors/search?q=Test" | grep -o '"ID":[0-9]*' | head -1 | cut -d':' -f2)
        boat_id=$(curl -s "$SERVER_URL/api/boats/search?q=Test" | grep -o '"ID":[0-9]*' | head -1 | cut -d':' -f2)

        if [ -n "$sailor_id" ] && [ -n "$boat_id" ]; then
            participant_data="event_id=$event_id&sailor_id=$sailor_id&boat_id=$boat_id&srs_type=srs&selected_srs_value=1.0&crew_count=1"
            participant_response=$(curl -s -X POST -d "$participant_data" "$SERVER_URL/events/$event_id/participants")
            log "Added test participant to event"

            # Add a finish time for the participant
            log_verbose "Adding finish time for test participant"
            finish_time="finish_time_1=$(date +%H:%M:%S)"
            finish_response=$(curl -s -X POST -d "$finish_time" "$SERVER_URL/events/$event_id/finish-times/1")
            log "Added finish time for test participant"
        fi
    fi

    log "Test data created successfully"
}

# Function to run Go unit tests
run_unit_tests() {
    local failed=0
    local total=0

    log "Running Go unit tests..."

    # Run database tests
    log_verbose "Running database tests..."
    if go test ./pkg/database -v; then
        log "✅ Database tests passed"
    else
        log "❌ Database tests failed"
        ((failed++))
    fi
    ((total++))

    # Run scoring tests
    log_verbose "Running scoring tests..."
    if go test ./pkg/database -v -run TestScoring; then
        log "✅ Scoring tests passed"
    else
        log "❌ Scoring tests failed"
        ((failed++))
    fi
    ((total++))

    # Run results display tests
    log_verbose "Running results display tests..."
    if go test ./pkg/handlers -v -run TestResultsDisplayContent; then
        log "✅ Results display tests passed"
    else
        log "❌ Results display tests failed"
        ((failed++))
    fi
    ((total++))

    # Run CSV export tests
    log_verbose "Running CSV export tests..."
    if go test ./pkg/handlers -v -run TestCSVExportContent; then
        log "✅ CSV export tests passed"
    else
        log "❌ CSV export tests failed"
        ((failed++))
    fi
    ((total++))

    # Run entypsegling-jaktstart interaction tests
    log_verbose "Running entypsegling-jaktstart interaction tests..."
    if go test ./pkg/handlers -v -run TestEntypseglingDisablesJaktstart; then
        log "✅ Entypsegling-jaktstart interaction tests passed"
    else
        log "❌ Entypsegling-jaktstart interaction tests failed"
        ((failed++))
    fi
    ((total++))

    # Run all other handler tests
    log_verbose "Running other handler tests..."
    if go test ./pkg/handlers -v -run "^Test" | grep -v "TestResultsDisplayContent\|TestCSVExportContent\|TestEntypseglingDisablesJaktstart"; then
        log "✅ Other handler tests passed"
    else
        log "❌ Other handler tests failed"
        ((failed++))
    fi
    ((total++))

    log "Unit tests completed: $((total-failed))/$total test suites passed"
    return $failed
}

# Function to run all tests
run_tests() {
    local failed=0
    local total=0

    log "Starting endpoint tests..."

    # Test home page
    test_endpoint "GET" "/" 200 "Home page"
    local test_result=$?
    ((total++))
    [ $test_result -ne 0 ] && ((failed++))
    log_verbose "Test #$total: Home page - Result: $test_result"

    # Test help page
    test_endpoint "GET" "/help" 200 "Help page"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    # Test settings endpoints
    test_endpoint "GET" "/settings" 200 "Settings page"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "POST" "/settings" 303 "Update settings" "auto_backup=on"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "POST" "/settings/club" 303 "Update club setting" "default_club=LSS"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "POST" "/settings/time-format" 303 "Update time format setting" "use_24h_time=on"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    # Test backup management endpoints
    test_endpoint "GET" "/backups" 200 "Backups page"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    # Test sailors endpoints
    test_endpoint "GET" "/sailors" 200 "Sailors page"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/sailors/1" 200 "Sailor details"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/api/sailors/search?q=Test" 200 "Search sailors API"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    # Test boats endpoints
    test_endpoint "GET" "/boats" 200 "Boats page"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/boats/1" 200 "Boat details"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/boats/1/json" 200 "Boat JSON"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/api/boats/search?q=Test" 200 "Search boats API"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    # Test events endpoints
    test_endpoint "GET" "/events" 200 "Events page"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/events/1" 200 "Event details"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/api/events" 200 "Events API"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/api/events/1" 200 "Event API details"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    # Test event participants endpoints
    test_endpoint "GET" "/events/1/participants" 200 "Event participants"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/events/1/participants/list" 200 "Event participants list"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    # Test jaktstart endpoints
    test_endpoint "GET" "/events/1/jaktstart" 200 "Jaktstart times"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/events/1/jaktstart/print" 200 "Jaktstart times print"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    # Test finish times endpoints
    test_endpoint "GET" "/events/1/finish-times" 200 "Finish times"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    # Test results endpoints
    test_endpoint "GET" "/events/1/results" 200 "Results"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/events/1/results/print" 200 "Results print"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/events/1/results/csv" 200 "Results CSV"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    # Test lock/unlock endpoints (temporarily disabled - not implemented)
    # test_endpoint "POST" "/events/1/lock" 303 "Lock event" ""
    # ((total++))
    # [ $? -ne 0 ] && ((failed++))

    # test_endpoint "POST" "/events/1/unlock" 303 "Unlock event" ""
    # ((total++))
    # [ $? -ne 0 ] && ((failed++))

    # Test starter boat endpoints
    test_endpoint "GET" "/events/1/starter-boat" 303 "Starter boat"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/events/1/starter-boat/print" 200 "Starter boat print"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    # Test SRS data endpoints
    test_endpoint "GET" "/srs" 200 "SRS management"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/srs/boat-types" 200 "SRS boat types"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/srs/matbrev" 200 "SRS matbrev"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/srs/boat-types/search?search=Test" 200 "SRS boat types search"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/srs/matbrev/search?search=Test" 200 "SRS matbrev search"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    # Test SRS data export endpoints
    test_endpoint "GET" "/srs/boat-types/export" 200 "SRS boat types export"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    test_endpoint "GET" "/srs/matbrev/export" 200 "SRS matbrev export"
    ((total++))
    [ $? -ne 0 ] && ((failed++))

    # Print test results
    local passed=$((total-failed))
    log "Tests completed: $passed/$total passed"

    # Debug output
    log_verbose "Total tests run: $total"
    log_verbose "Failed tests: $failed"
    log_verbose "Passed tests: $passed"

    # All tests must pass - no tolerance for failures
    if [ $failed -eq 0 ]; then
        log "All tests passed! 🎉"
        return 0
    else
        log "Some tests failed! 😢"
        log "Failed: $failed out of $total tests"
        return 1
    fi
}

# Main function
main() {
    # Trap Ctrl+C and exit gracefully
    trap 'log "Interrupted"; stop_server; exit 1' INT

    local unit_test_result=0
    local integration_test_result=0

    # Build the server executable first to ensure we have the latest code
    build_server

    # Run unit tests if requested
    if [ "$RUN_UNIT_TESTS" = true ]; then
        log "=== Running Unit Tests ==="
        run_unit_tests
        unit_test_result=$?
    fi

    # Run integration tests if requested
    if [ "$RUN_INTEGRATION_TESTS" = true ]; then
        # Start the server for integration tests
        start_server

        # Create test data
        create_test_data

        # Run the endpoint tests
        log "=== Running Integration Tests ==="
        run_tests
        integration_test_result=$?

        # Stop the server
        stop_server
    fi

    # Calculate overall result
    local overall_result=0
    if [ "$RUN_UNIT_TESTS" = true ] && [ $unit_test_result -ne 0 ]; then
        overall_result=1
    fi
    if [ "$RUN_INTEGRATION_TESTS" = true ] && [ $integration_test_result -ne 0 ]; then
        overall_result=1
    fi

    if [ $overall_result -eq 0 ]; then
        log "🎉 All tests passed!"
        exit 0
    else
        log "😢 Some tests failed!"
        if [ "$RUN_UNIT_TESTS" = true ]; then
            log "Unit tests: $([ $unit_test_result -eq 0 ] && echo "PASSED" || echo "FAILED")"
        fi
        if [ "$RUN_INTEGRATION_TESTS" = true ]; then
            log "Integration tests: $([ $integration_test_result -eq 0 ] && echo "PASSED" || echo "FAILED")"
        fi
        exit 1
    fi
}

# Run the main function
main
