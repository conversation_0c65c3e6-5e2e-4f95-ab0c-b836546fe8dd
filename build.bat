@echo off
REM Build script for the Segling application on Windows
REM This script compiles the application and packages all necessary files
REM It uses the latest git tag for versioning (or falls back to a default version)

REM Define variables
set APP_NAME=segling
set DEFAULT_VERSION=1.9.1

REM Get the latest git tag for version, fallback to default if not available
REM First try to get tag+hash if we're not exactly on a tag
for /f "tokens=*" %%i in ('git describe --tags 2^>nul') do set VERSION=%%i
if "%VERSION%"=="" (
    REM If that fails, try to get just the latest tag
    for /f "tokens=*" %%i in ('git describe --tags --abbrev^=0 2^>nul') do set VERSION=%%i
    if "%VERSION%"=="" set VERSION=%DEFAULT_VERSION%
)

echo === Building Segling Application v%VERSION% ===

set BUILD_DIR=build
set DIST_DIR=dist
set PACKAGE_NAME=%APP_NAME%-%VERSION%
set PACKAGE_DIR=%DIST_DIR%\%PACKAGE_NAME%

REM Check if Go is installed
where go >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: Go is not installed. Please install Go to build this application.
    exit /b 1
)

REM Create build and dist directories
echo Creating build directories...
if not exist %BUILD_DIR% mkdir %BUILD_DIR%
if not exist %DIST_DIR% mkdir %DIST_DIR%
if not exist %PACKAGE_DIR% mkdir %PACKAGE_DIR%
if not exist %PACKAGE_DIR%\static mkdir %PACKAGE_DIR%\static
if not exist %PACKAGE_DIR%\pkg\templates mkdir %PACKAGE_DIR%\pkg\templates
if not exist %PACKAGE_DIR%\backups mkdir %PACKAGE_DIR%\backups
if not exist %PACKAGE_DIR%\certs mkdir %PACKAGE_DIR%\certs
if not exist %PACKAGE_DIR%\certs\export mkdir %PACKAGE_DIR%\certs\export
if not exist %PACKAGE_DIR%\certs\client mkdir %PACKAGE_DIR%\certs\client
if not exist %PACKAGE_DIR%\credentials mkdir %PACKAGE_DIR%\credentials
if not exist %PACKAGE_DIR%\scripts mkdir %PACKAGE_DIR%\scripts
if not exist %PACKAGE_DIR%\docs mkdir %PACKAGE_DIR%\docs

REM Build the application
echo Building application...
go build -ldflags="-X main.Version=%VERSION%" -o %BUILD_DIR%\%APP_NAME%.exe .\cmd\server

REM Copy the executable
echo Copying executable...
copy %BUILD_DIR%\%APP_NAME%.exe %PACKAGE_DIR%\

REM Copy static files
echo Copying static files...
xcopy /E /I /Y static %PACKAGE_DIR%\static

REM Copy templates
echo Copying templates...
xcopy /E /I /Y pkg\templates %PACKAGE_DIR%\pkg\templates

REM Create an empty database file
echo Creating initial database...
type nul > %PACKAGE_DIR%\segling.db
echo Created empty database file
echo Note: The database will be initialized on first run

REM Copy scripts (if they exist)
if exist scripts (
    echo Copying scripts...
    xcopy /E /I /Y scripts\* %PACKAGE_DIR%\scripts\
)

REM Copy documentation (if it exists)
if exist docs (
    echo Copying documentation...
    xcopy /E /I /Y docs\* %PACKAGE_DIR%\docs\
)

REM Create a README file
echo Creating README file...
(
echo Segling Application v%VERSION%
echo ==============================
echo.
echo This is a sailing competition management application.
echo.
echo Quick Start:
echo 1. Run the application by double-clicking on segling.exe or using start.bat
echo 2. Open your browser and navigate to:
echo    http://localhost:8080
echo.
echo Enabling HTTPS (Secure Connection^):
echo 1. Generate self-signed certificates (for development^)
echo    Run scripts\generate_certs.bat
echo 2. Run the application with TLS enabled
echo    segling.exe -tls -cert certs\cert.pem -key certs\key.pem
echo 3. Open your browser and navigate to:
echo    https://localhost:8080
echo    (Note: Browsers will show a warning about self-signed certificates^)
echo.
echo Importing Certificates to Browser (to avoid warnings^):
echo 1. Export the certificate in browser-friendly formats
echo    Run scripts\export_cert_for_browser.bat
echo 2. Follow the instructions provided by the script to import
echo    the certificate into your specific browser
echo.
echo Enabling Mutual TLS (Client Certificate Authentication^):
echo 1. Generate client certificates
echo    Run scripts\generate_client_certs.bat
echo 2. Run the application with client certificate verification
echo    segling.exe -tls -cert certs\cert.pem -key certs\key.pem -client-ca certs\client\ca.crt -require-client-cert
echo 3. Import the client certificate to your browser
echo    (Password for the client certificate: segling^)
echo.
echo Directory Structure:
echo - segling.exe: The main executable
echo - pkg/templates/: HTML templates
echo - static/: CSS, JavaScript, and other static assets
echo - backups/: Directory for database backups
echo - certs/: Directory for TLS certificates
echo   - certs/export/: Exported certificates for browser import
echo   - certs/client/: Client certificates for mutual TLS
echo - credentials/: Directory for sensitive credentials (not included in backups^)
echo - scripts/: Utility scripts
echo - docs/: Documentation
echo - segling.db: SQLite database file
echo.
echo Notes:
echo - The application will create and initialize the database on first run
echo - Backups will be stored in the backups/ directory
echo - The application runs on port 8080 by default
echo - Default club for new sailors can be configured in Settings
echo - Dark/light mode toggle is available in the top navigation bar
echo.
echo For more information, visit: https://github.com/rogge66/sailapp
) > %PACKAGE_DIR%\README.txt

REM Create a simple start script
echo Creating start scripts...
(
echo @echo off
echo REM Start script for Segling application
echo.
echo REM Run the application
echo start segling.exe
echo.
echo REM If you want to see console output, use the following line instead:
echo REM segling.exe
echo.
echo echo Application started. You can close this window.
) > %PACKAGE_DIR%\start.bat

REM Create a secure start script
(
echo @echo off
echo REM Start script for Segling application with HTTPS enabled
echo.
echo REM Check if certificates exist
echo if not exist certs\cert.pem (
echo     echo TLS certificates not found. Generating self-signed certificates...
echo     call scripts\generate_certs.bat
echo )
echo.
echo REM Run the application with TLS enabled
echo start segling.exe -tls -cert certs\cert.pem -key certs\key.pem
echo.
echo REM If you want to see console output, use the following line instead:
echo REM segling.exe -tls -cert certs\cert.pem -key certs\key.pem
echo.
echo echo Application started with HTTPS. You can close this window.
) > %PACKAGE_DIR%\start-secure.bat

REM Create a zip archive
echo Creating zip archive...
REM Check if PowerShell is available for zipping
where powershell >nul 2>nul
if %ERRORLEVEL% equ 0 (
    powershell -Command "Compress-Archive -Path '%PACKAGE_DIR%\*' -DestinationPath '%DIST_DIR%\%PACKAGE_NAME%.zip' -Force"
) else (
    echo Warning: PowerShell not found. Zip archive not created.
    echo You can manually zip the contents of %PACKAGE_DIR%
)

REM Clean up
echo Cleaning up...
rmdir /S /Q %BUILD_DIR%

REM Create latest directory
echo Creating latest distribution directory...
if not exist %DIST_DIR%\latest mkdir %DIST_DIR%\latest

REM Windows - copy files to latest directory
echo Copying files to latest directory...
REM Clear the latest directory first
if exist %DIST_DIR%\latest\* del /Q /S %DIST_DIR%\latest\*
REM Copy all files from the package directory to the latest directory
xcopy /E /I /Y %PACKAGE_DIR%\* %DIST_DIR%\latest\
REM Copy the zip file if it exists
if exist %DIST_DIR%\%PACKAGE_NAME%.zip copy /Y %DIST_DIR%\%PACKAGE_NAME%.zip %DIST_DIR%\latest\%APP_NAME%.zip
echo Files copied to %DIST_DIR%\latest\

REM Print success message
echo Build completed successfully!
echo Package created at: %PACKAGE_DIR%
echo Latest version available at: %DIST_DIR%\latest\
echo You can also run the application directly from: %PACKAGE_DIR%\segling.exe
