# Entypsegling (One-Design) Competition Format

This document explains the Entypsegling (One-Design) competition format in Segling, which allows for placement-based results without time calculations or handicaps.

## Overview

Entypsegling is designed for one-design fleet racing where all competitors use the same boat type. Instead of using time-based calculations with SRS handicaps, results are based purely on finish order (placements).

## Creating an Entypsegling Event

1. Navigate to the "Tävlingar" (Competitions) section
2. Click "Skapa ny tävling" (Create new competition)
3. <PERSON>ll in the event details (name, date, etc.)
4. Check the "Entypsegling" checkbox
5. Enter the boat type in the "Boat Type" field (e.g., "Laser", "Optimist", "420")
6. Save the event

**Note**: When Entypsegling is enabled, <PERSON><PERSON><PERSON><PERSON><PERSON> (pursuit start) is automatically disabled as they are incompatible formats.

## Managing Participants

For Entypsegling events, participants are managed differently:

1. Participants are not connected to boats from the boat database
2. Each participant has a personal sail number (e.g., "SWE 123", "FIN 456")
3. Participants are still connected to sailors from the sailor database

To add participants:
1. Navigate to the event's "Hantera deltagare" (Manage participants) page
2. Select a sailor from the database
3. Enter the personal sail number in the "Personal Number" field
4. Save the participant

## Recording Results

For Entypsegling events, you record placements instead of finish times:

1. Navigate to the event's "Måltider" (Finish times) page
2. For each participant, enter their placement (1st, 2nd, 3rd, etc.)
3. For participants who did not finish or start, check the DNF or DNS checkbox
4. Save the results

## Viewing Results

Results for Entypsegling events show:
- Position (final placement)
- Sailor information
- Personal sail number
- Crew count
- Status (DNF/DNS if applicable)

For multi-heat events, results also show:
- Overall position
- Total points
- Individual heat placements

## Tied Finishes

Entypsegling supports tied finishes:
- When multiple boats are assigned the same placement, they receive the average points for those positions
- Points are displayed with one decimal precision (e.g., 1.5p for a 2-way tie for 1st place)
- Tied finishes are clearly indicated in results with identical placement numbers

## Exporting Results

All export formats (CSV, Google Drive, GitHub Pages) support Entypsegling events with appropriate formatting for placement-based results.

### CSV Export
- Excludes time and SRS columns
- Includes personal numbers instead of boat information
- Shows placements and points

### Google Drive Export
- Creates Google Sheets with placement-based formatting
- Includes event boat type information
- Optimized layout for one-design competitions

### GitHub Pages Publishing
- Automatically detects entypsegling format
- Renders appropriate layout without time information
- Includes boat type and personal sail numbers

## Key Differences from Standard Racing

| Feature | Standard Racing | Entypsegling |
|---------|----------------|--------------|
| Results basis | Time + SRS handicap | Placement only |
| Participant setup | Sailor + Boat from database | Sailor + Personal sail number |
| Finish recording | Time (HH:MM:SS) | Placement (1, 2, 3, etc.) |
| Jaktstart compatibility | Yes | No (automatically disabled) |
| SRS calculations | Yes | No |
| Tied finishes | By corrected time | By average points |

## Best Practices

1. **Boat Type**: Use clear, standardized boat type names (e.g., "Laser Standard", "Optimist", "420")
2. **Personal Numbers**: Use consistent formatting for sail numbers (e.g., "SWE 123", "FIN 456")
3. **Tied Finishes**: Record tied finishes by assigning the same placement number to multiple participants
4. **DNS/DNF**: Always mark participants who didn't start or finish appropriately
5. **Multi-Heat Events**: Ensure consistent participant lists across all heats

## Troubleshooting

### Common Issues

**Q: Why can't I enable both Entypsegling and Jaktstart?**
A: These formats are incompatible. Entypsegling uses placement-based results while Jaktstart requires time-based calculations with SRS handicaps.

**Q: Can I change an event from standard racing to Entypsegling after creating it?**
A: Yes, but be aware that existing participant data (boat connections) and finish times will need to be reconfigured for the new format.

**Q: How are tied finishes calculated?**
A: When multiple participants share the same placement, they receive the average points for those positions. For example, two participants tied for 1st place would each receive 1.5 points.

**Q: Can I export Entypsegling results to the same formats as standard racing?**
A: Yes, all export formats (CSV, Google Drive, GitHub Pages) support Entypsegling with appropriate formatting adjustments.

## Technical Notes

- Entypsegling events store placement data instead of time data
- The system automatically adjusts export templates based on the event format
- Personal sail numbers are stored as free-text fields without validation
- Points calculations use decimal precision for accurate tied finish handling
