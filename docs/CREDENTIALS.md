# Credentials Management in Segling

This document explains how sensitive credentials are managed in the Segling application.

## Overview

Segling stores sensitive credentials (like API tokens) in separate files rather than in the database. This provides several benefits:

1. **Security**: Credentials are stored in files with restricted permissions (readable only by the owner)
2. **Backup Safety**: Credentials are not included in database backups, reducing the risk of accidental exposure
3. **Version Control**: Credential files are excluded from version control via `.gitignore`
4. **Separation of Concerns**: Application data and sensitive credentials are kept separate

## Credential Storage

Credentials are stored in the `credentials/` directory in the application root. This directory is:

- Created automatically when the application starts
- Set with restricted permissions (700, readable only by the owner)
- Excluded from Git via `.gitignore`

## GitHub Pages Token

The GitHub Personal Access Token used for GitHub Pages integration is stored in:

```
credentials/github_token.txt
```

### Migration from Database

If you're upgrading from a previous version of Segling that stored the GitHub token in the database, the token will be automatically migrated to the file system when the application starts. The migration process:

1. Checks if the token already exists in the file
2. If not, reads the token from the database
3. Saves the token to the file
4. Replaces the token in the database with a placeholder (`********`)

## Managing Credentials

### Viewing Credentials

Credentials are not directly viewable through the Segling UI for security reasons. To view a credential:

1. Navigate to the `credentials/` directory
2. Open the relevant credential file with a text editor

### Updating Credentials

Credentials can be updated through the Segling UI (Settings page). When you update a credential:

1. The new value is saved to the appropriate file
2. A placeholder is stored in the database to indicate the credential exists

### Manually Updating Credentials

You can also manually update credentials by editing the files directly:

1. Navigate to the `credentials/` directory
2. Edit the relevant credential file with a text editor
3. Save the file with the new credential value

### Backing Up Credentials

Since credentials are not included in the database backups, you should separately back up the `credentials/` directory if you need to preserve your credentials.

**Important**: Store these backups securely, as they contain sensitive information.

## Troubleshooting

### Missing Credentials

If credentials appear to be missing:

1. Check if the `credentials/` directory exists
2. Verify the credential files exist and have the correct permissions
3. Ensure the files contain valid credential values

### Permission Issues

If you encounter permission issues:

1. Check the permissions on the `credentials/` directory (should be 700)
2. Check the permissions on the credential files (should be 600)
3. Ensure the user running the application has read/write access to these files

### Migration Issues

If the automatic migration from database to file fails:

1. Check the application logs for error messages
2. Manually copy the credential from the database to the appropriate file
3. Update the database value to a placeholder (`********`)

## Security Considerations

- The `credentials/` directory should be backed up separately and securely
- When transferring the application to a new server, ensure credentials are transferred securely
- Regularly rotate credentials (especially API tokens) according to security best practices
- Consider using environment variables instead of files for even greater security in production environments
