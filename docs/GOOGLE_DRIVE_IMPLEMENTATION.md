# Google Drive Export Implementation

## Overview

This document describes the implementation of Google Drive export functionality for the Segling application, allowing users to export competition results directly to Google Sheets.

## Features Implemented

### 1. Core Google Drive Service (`pkg/services/googledrive.go`)
- **OAuth2 Authentication**: Complete OAuth2 flow with token management
- **Spreadsheet Creation**: Create new Google Sheets with custom titles
- **Data Writing**: Write formatted competition data to spreadsheets
- **Folder Management**: Support for organizing exports in specific Google Drive folders
- **Token Persistence**: Automatic token saving and refresh handling

### 2. Export Handlers (`pkg/handlers/googledrive_export.go`)
- **Authentication Management**: Check auth status and handle OAuth flow
- **Result Export**: Convert competition results to spreadsheet format
- **File Naming**: Configurable naming conventions with placeholders
- **Error Handling**: Comprehensive error handling and user feedback

### 3. Database Integration
- **Settings Storage**: New database settings for Google Drive configuration:
  - `google_drive_enabled`: Enable/disable integration
  - `google_drive_folder_id`: Target folder for exports
  - `google_drive_naming_convention`: File naming template

### 4. User Interface Updates

#### Settings Page (`pkg/templates/settings.html`)
- **Configuration Section**: Complete Google Drive settings interface
- **File Upload**: Upload `client_secrets.json` via web interface
- **Authentication Status**: Check and manage authentication state
- **Interactive OAuth Flow**: In-browser authentication process

#### Results Pages (`pkg/templates/results.html`, `pkg/templates/results_print.html`)
- **Export Buttons**: Google Drive export buttons on results pages
- **Progress Feedback**: Loading states and success/error messages
- **Automatic Opening**: Opens exported spreadsheet in new tab

### 5. Route Registration
- **Settings Routes**:
  - `POST /settings/google-drive`: Save Google Drive settings
  - `POST /settings/upload-client-secrets`: Upload credentials file
- **Export Routes**:
  - `POST /events/:id/export-google-drive`: Export competition results
- **Authentication Routes**:
  - `GET /google-drive/auth`: Check authentication status
  - `POST /google-drive/auth`: Complete OAuth flow

## Technical Implementation Details

### Authentication Flow
1. User uploads `client_secrets.json` via settings page
2. Application generates OAuth2 authorization URL
3. User visits URL and grants permissions
4. User copies authorization code back to application
5. Application exchanges code for access/refresh tokens
6. Tokens are stored securely for future use

### Data Export Process
1. User clicks "Export to Google Drive" button
2. Application checks authentication status
3. Retrieves competition results (regular or saved/locked)
4. Formats data for spreadsheet (headers, event info, results)
5. Creates new Google Sheets document
6. Writes formatted data to spreadsheet
7. Optionally moves file to specified folder
8. Returns spreadsheet URL to user

### Data Format
Exported spreadsheets include:
- **Event Information**: Name, date, start time, wind, course length
- **Result Headers**: Position, sailor, club, boat details, times
- **Result Data**: All competition results with proper formatting
- **Special Handling**: DNS/DNF status, custom SRS values, nationality formatting

### File Naming
Supports configurable naming with placeholders:
- `{{event_name}}`: Competition name
- `{{date}}`: Date (YYYY-MM-DD)
- `{{competition_type}}`: Competition type
- `{{year}}`, `{{month}}`, `{{day}}`: Date components

## Security Considerations

### Credential Management
- `client_secrets.json` stored locally, not in database
- OAuth tokens stored securely with automatic refresh
- No sensitive data exposed in URLs or logs

### API Permissions
- Minimal required scopes: `drive.file` and `spreadsheets`
- Only creates new files, doesn't access existing user data
- Files created are owned by the authenticating user

### Error Handling
- Graceful handling of authentication failures
- Clear error messages for common issues
- Fallback behavior when Google APIs are unavailable

## Dependencies Added

```go
require (
    golang.org/x/oauth2 v0.30.0
    google.golang.org/api v0.234.0
)
```

### Specific API Packages
- `google.golang.org/api/drive/v3`: Google Drive API
- `google.golang.org/api/sheets/v4`: Google Sheets API
- `golang.org/x/oauth2/google`: Google OAuth2 implementation

## Configuration Requirements

### Google Cloud Setup
1. Create Google Cloud project
2. Enable Google Drive API and Google Sheets API
3. Create OAuth2 credentials (Desktop application type)
4. Download `client_secrets.json`

### Application Setup
1. Upload `client_secrets.json` via settings page
2. Enable Google Drive integration
3. Configure folder ID (optional)
4. Set file naming convention (optional)
5. Complete OAuth authentication

## Testing

The implementation includes:
- **Compilation Testing**: All code compiles without errors
- **Service Creation**: Google Drive service initializes correctly
- **Authentication Flow**: OAuth URLs generate properly
- **Error Handling**: Graceful handling of missing credentials

## Future Enhancements

Potential improvements for future versions:
- **Batch Export**: Export multiple competitions at once
- **Template Customization**: Custom spreadsheet templates
- **Folder Auto-Creation**: Automatically create year/type folders
- **Export Scheduling**: Automatic exports after competitions
- **Advanced Formatting**: Charts, conditional formatting, etc.

## Troubleshooting

Common issues and solutions:
- **Authentication Errors**: Re-upload credentials, re-authenticate
- **API Quota Limits**: Wait and retry, upgrade Google Cloud project
- **Permission Errors**: Check OAuth scopes and API enablement
- **Network Issues**: Verify internet connectivity and firewall settings

## Integration with Existing Features

The Google Drive export integrates seamlessly with:
- **Locked Events**: Exports saved results for finalized competitions
- **Multiple Result Types**: Handles regular and pursuit start results
- **Custom SRS Values**: Preserves custom handicap values
- **Heat Results**: Exports multi-heat competition data
- **Print Views**: Available from both regular and print result pages

This implementation provides a complete, production-ready Google Drive export feature that enhances the Segling application's data sharing capabilities while maintaining security and user experience standards.
