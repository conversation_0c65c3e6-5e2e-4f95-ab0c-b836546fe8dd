# Google Drive Integration Setup

This guide explains how to set up Google Drive integration for the Segling application to export competition results to Google Sheets.

## Prerequisites

1. A Google account
2. Access to Google Cloud Console
3. The Segling application running

## Step 1: Create a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Note down your project ID

## Step 2: Enable Required APIs

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. **Enable Google Drive API**:
   - Search for "Google Drive API"
   - Click on "Google Drive API" in the results
   - Click the "Enable" button
   - Wait for the API to be enabled (you'll see a green checkmark)
3. **Enable Google Sheets API**:
   - Go back to "APIs & Services" > "Library"
   - Search for "Google Sheets API"
   - Click on "Google Sheets API" in the results
   - Click the "Enable" button
   - Wait for the API to be enabled

**Important**: Both APIs are required! Google Drive API manages files and folders, while Google Sheets API handles spreadsheet creation and data writing.

### Verify APIs Are Enabled
- Go to "APIs & Services" > "Dashboard"
- You should see both APIs listed under "Enabled APIs & services":
  - ✅ Google Drive API
  - ✅ Google Sheets API

## Step 3: Create Credentials

### Configure OAuth Consent Screen (First Time Only)

1. Go to "APIs & Services" > "OAuth consent screen"
2. **Choose User Type**:
   - Select "External" (unless you have a Google Workspace account)
   - Click "Create"
3. **Fill in App Information**:
   - **App name**: "Segling Results" (or your preferred name)
   - **User support email**: Your email address
   - **Developer contact information**: Your email address
   - Leave other fields as default
   - Click "Save and Continue"
4. **Scopes** (Step 2):
   - Click "Add or Remove Scopes"
   - Search for and add these scopes:
     - `https://www.googleapis.com/auth/drive.file` (Create, read, and write files in Google Drive)
     - `https://www.googleapis.com/auth/spreadsheets` (Create and edit spreadsheets)
   - Click "Update" then "Save and Continue"
5. **Test Users** (Step 3):
   - Add your email address as a test user
   - Click "Save and Continue"
6. **Summary** (Step 4):
   - Review and click "Back to Dashboard"

### Create OAuth Client ID

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. **Configure the OAuth client**:
   - **Application type**: Choose "Desktop application"
   - **Name**: "Segling App" (or your preferred name)
   - Click "Create"
4. **Download Credentials**:
   - A dialog will appear with your client ID and secret
   - Click "Download JSON"
   - Save the file (it will be named like `client_secret_1234567890-abc123.apps.googleusercontent.com.json`)
   - **Important**: Keep this file secure and don't share it publicly

### Important Notes About Credentials

- **File Security**: The downloaded JSON file contains sensitive information but is safe to store locally
- **No Secrets in Database**: The Segling app stores this file locally, not in the database
- **One-Time Setup**: You only need to create credentials once per Google Cloud project

## Step 4: Configure Segling Application

1. Open the Segling application in your web browser
2. Go to "Inställningar" (Settings)
3. Scroll down to the "Google Drive Integration" section
4. Upload the `client_secrets.json` file you downloaded in Step 3
5. Configure the settings:
   - **Enable Google Drive export**: Check this box
   - **Google Drive Folder ID** (optional): Leave empty to use root folder, or enter a folder ID
   - **File naming convention**: Use default or customize with placeholders

## Step 5: Authenticate with Google

### Complete the OAuth Flow

1. **Start Authentication**:
   - In the Google Drive Integration section, click "Kontrollera autentisering" (Check Authentication)
   - Click the "Autentisera" (Authenticate) button
   - This will open Google's authorization page in a new tab

2. **Grant Permissions**:
   - **Sign in** with your Google account (the same one you used to create the project)
   - **Review permissions**: You'll see a screen asking for permission to:
     - "See and download all your Google Drive files"
     - "Create, edit, and delete your spreadsheets in Google Drive"
   - **Click "Allow"** to grant these permissions

3. **Automatic Redirect**:
   - After granting permissions, you'll be automatically redirected back to the Segling application
   - You should see a success page saying "Autentisering lyckades!" (Authentication successful!)
   - The page will automatically redirect you back to settings after 3 seconds

4. **Verify Authentication**:
   - Back in the settings page, click "Kontrollera autentisering" again
   - You should now see "Autentiserad med Google Drive" (Authenticated with Google Drive)

### Troubleshooting Authentication

**If you get a "This app isn't verified" warning**:
1. Click "Advanced" at the bottom left
2. Click "Go to Segling Results (unsafe)"
3. This is normal for development/personal apps

**If authentication fails**:
1. Make sure both APIs are enabled in Google Cloud Console
2. Check that your OAuth consent screen is properly configured
3. Verify that you're using the correct Google account
4. Try clearing your browser cache and cookies for Google

**If you get redirected to a 404 page**:
1. Make sure the Segling application is running on http://localhost:8080
2. Check that the OAuth client is configured as "Desktop application"

## Step 6: Test the Integration

1. Go to any competition results page
2. You should see a "Exportera till Google Drive" (Export to Google Drive) button
3. Click the button to test the export
4. Check your Google Drive for the exported spreadsheet

## Folder Configuration (Optional)

To export to a specific folder in Google Drive:

1. Create a folder in Google Drive
2. Open the folder and look at the URL
3. The folder ID is the last part of the URL after `/folders/`
4. Copy this ID and paste it in the "Google Drive Mapp-ID" field in settings

Example URL: `https://drive.google.com/drive/folders/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74mMngDnt`
Folder ID: `1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74mMngDnt`

## File Naming Convention

You can customize how exported files are named using placeholders:

- `{{event_name}}` - Name of the competition
- `{{date}}` - Date in YYYY-MM-DD format
- `{{competition_type}}` - Type of competition (e.g., Kvällssegling, Regatta)
- `{{year}}` - Year (YYYY)
- `{{month}}` - Month (MM)
- `{{day}}` - Day (DD)

Example: `{{competition_type}}_{{event_name}}_{{date}}` might produce `Kvällssegling_Midsommarsegling_2024-06-21`

## Troubleshooting

### "Client secrets file not found"
- Make sure you've uploaded the `client_secrets.json` file in the settings
- The file should be uploaded through the Segling settings page, not placed manually

### "Not authenticated with Google Drive"
- Complete the authentication process as described in Step 5
- The authentication token may have expired - re-authenticate
- Make sure you're using the same Google account that owns the Google Cloud project

### "Unable to create spreadsheet" or "Project not enabled"
**This is the most common issue!** The error message might look like:
```
Project is not enabled in console.developers.google.com/apis/api/sheets.googleapis.com
```

**Solution**:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project (e.g., "segelresultat")
3. Go to "APIs & Services" > "Library"
4. Search for "Google Sheets API"
5. Click on it and click "Enable"
6. Wait for it to be enabled (you'll see a green checkmark)
7. Try the export again

**Why this happens**: Google Drive API and Google Sheets API are separate services. Even though spreadsheets are stored in Google Drive, you need both APIs enabled.

### "Unable to move file to folder"
- Check that the folder ID is correct (see folder configuration section above)
- Make sure the folder exists and is accessible with your Google account
- Verify the folder wasn't deleted or moved

### "This app isn't verified" warning
- This is normal for personal/development apps
- Click "Advanced" → "Go to [App Name] (unsafe)"
- This happens because Google hasn't verified your personal app (which is expected)

### "Access blocked: This app's request is invalid"
- Check that both Google Drive API and Google Sheets API are enabled
- Verify your OAuth consent screen is properly configured
- Make sure you added the correct scopes in the consent screen

### "Quota exceeded" errors
- Google APIs have usage limits. If you're exporting many files, you may hit these limits
- Wait a while before trying again
- For heavy usage, consider upgrading your Google Cloud project

### Authentication keeps failing
1. **Clear browser data**: Clear cookies and cache for Google domains
2. **Check project**: Make sure you're using the correct Google Cloud project
3. **Verify APIs**: Confirm both APIs are enabled in the correct project
4. **Re-create credentials**: If all else fails, create new OAuth credentials

## Security Notes

- The `client_secrets.json` file contains sensitive information but is safe to store locally
- The actual authentication tokens are stored securely and are not exposed in the database
- Only grant the minimum required permissions when setting up OAuth scopes
- Regularly review and rotate your Google Cloud credentials if needed

## Support

If you encounter issues with Google Drive integration, check:
1. Google Cloud Console for API quotas and errors
2. The Segling application logs for detailed error messages
3. Ensure all required APIs are enabled and properly configured
