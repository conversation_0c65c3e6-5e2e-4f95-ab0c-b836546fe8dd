package database

import (
	"testing"

	"github.com/rbjoregren/segling/pkg/models"
	"github.com/stretchr/testify/assert"
)

// TestTiedFinishCalculations tests the decimal point calculations for tied finishes
func TestTiedFinishCalculations(t *testing.T) {
	tests := []struct {
		name           string
		tiedPositions  []int
		expectedPoints float64
	}{
		{
			name:           "2-way tie for 1st place",
			tiedPositions:  []int{1, 2},
			expectedPoints: 1.5, // (1+2)/2 = 1.5
		},
		{
			name:           "2-way tie for 2nd place",
			tiedPositions:  []int{2, 3},
			expectedPoints: 2.5, // (2+3)/2 = 2.5
		},
		{
			name:           "3-way tie for 2nd place",
			tiedPositions:  []int{2, 3, 4},
			expectedPoints: 3.0, // (2+3+4)/3 = 3.0
		},
		{
			name:           "2-way tie for 4th place",
			tiedPositions:  []int{4, 5},
			expectedPoints: 4.5, // (4+5)/2 = 4.5
		},
		{
			name:           "4-way tie for 1st place",
			tiedPositions:  []int{1, 2, 3, 4},
			expectedPoints: 2.5, // (1+2+3+4)/4 = 2.5
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Calculate sum of positions
			sum := 0
			for _, pos := range tt.tiedPositions {
				sum += pos
			}

			// Calculate average points
			actualPoints := float64(sum) / float64(len(tt.tiedPositions))

			assert.Equal(t, tt.expectedPoints, actualPoints,
				"Tied finish calculation for %v should be %.1f points",
				tt.tiedPositions, tt.expectedPoints)
		})
	}
}

// TestSharedPositionLogic tests the shared position assignment
func TestSharedPositionLogic(t *testing.T) {
	// Create mock total results with complete ties
	totalResults := []models.TotalResult{
		{
			EventParticipant: models.EventParticipant{ID: 1},
			TotalPoints:      4.0,
			HeatResults: []models.HeatResult{
				{Points: 1.5}, {Points: 2.5}, // Same as participant 2
			},
		},
		{
			EventParticipant: models.EventParticipant{ID: 2},
			TotalPoints:      4.0,
			HeatResults: []models.HeatResult{
				{Points: 1.5}, {Points: 2.5}, // Same as participant 1
			},
		},
		{
			EventParticipant: models.EventParticipant{ID: 3},
			TotalPoints:      6.0,
			HeatResults: []models.HeatResult{
				{Points: 3.0}, {Points: 3.0},
			},
		},
		{
			EventParticipant: models.EventParticipant{ID: 4},
			TotalPoints:      8.0,
			HeatResults: []models.HeatResult{
				{Points: 4.0}, {Points: 4.0},
			},
		},
	}

	// Create a mock DB instance
	db := &DB{}

	// Apply shared position logic
	db.assignSharedPositions(totalResults)

	// Verify positions
	assert.Equal(t, 1, totalResults[0].TotalPosition, "First participant should be 1st")
	assert.Equal(t, 1, totalResults[1].TotalPosition, "Second participant should share 1st (tied)")
	assert.Equal(t, 3, totalResults[2].TotalPosition, "Third participant should be 3rd (after shared 1st)")
	assert.Equal(t, 4, totalResults[3].TotalPosition, "Fourth participant should be 4th")
}

// TestCompletelyTiedFunction tests the areCompletelyTied function
func TestCompletelyTiedFunction(t *testing.T) {
	db := &DB{}

	tests := []struct {
		name     string
		result1  models.TotalResult
		result2  models.TotalResult
		expected bool
	}{
		{
			name: "Completely tied boats",
			result1: models.TotalResult{
				TotalPoints: 4.0,
				HeatResults: []models.HeatResult{
					{Points: 1.5}, {Points: 2.5},
				},
			},
			result2: models.TotalResult{
				TotalPoints: 4.0,
				HeatResults: []models.HeatResult{
					{Points: 1.5}, {Points: 2.5},
				},
			},
			expected: true,
		},
		{
			name: "Different total points",
			result1: models.TotalResult{
				TotalPoints: 4.0,
				HeatResults: []models.HeatResult{
					{Points: 1.5}, {Points: 2.5},
				},
			},
			result2: models.TotalResult{
				TotalPoints: 5.0,
				HeatResults: []models.HeatResult{
					{Points: 1.5}, {Points: 3.5},
				},
			},
			expected: false,
		},
		{
			name: "Same total, different individual results",
			result1: models.TotalResult{
				TotalPoints: 4.0,
				HeatResults: []models.HeatResult{
					{Points: 1.0}, {Points: 3.0},
				},
			},
			result2: models.TotalResult{
				TotalPoints: 4.0,
				HeatResults: []models.HeatResult{
					{Points: 2.0}, {Points: 2.0},
				},
			},
			expected: false,
		},
		{
			name: "Same total, same results in different order",
			result1: models.TotalResult{
				TotalPoints: 5.0,
				HeatResults: []models.HeatResult{
					{Points: 2.0}, {Points: 3.0},
				},
			},
			result2: models.TotalResult{
				TotalPoints: 5.0,
				HeatResults: []models.HeatResult{
					{Points: 3.0}, {Points: 2.0},
				},
			},
			expected: true, // Should be true because we sort the results
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := db.areCompletelyTied(tt.result1, tt.result2)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestDNSAndDNFPoints tests DNS and DNF point calculations
func TestDNSAndDNFPoints(t *testing.T) {
	tests := []struct {
		name              string
		totalParticipants int
		finishedBoats     int
		expectedDNFPoints float64
		expectedDNSPoints float64
	}{
		{
			name:              "6 participants, 4 finished",
			totalParticipants: 6,
			finishedBoats:     4,
			expectedDNFPoints: 5.0, // last finisher (4th) + 1
			expectedDNSPoints: 7.0, // total participants (6) + 1
		},
		{
			name:              "8 participants, 6 finished",
			totalParticipants: 8,
			finishedBoats:     6,
			expectedDNFPoints: 7.0, // last finisher (6th) + 1
			expectedDNSPoints: 9.0, // total participants (8) + 1
		},
		{
			name:              "5 participants, 3 finished",
			totalParticipants: 5,
			finishedBoats:     3,
			expectedDNFPoints: 4.0, // last finisher (3rd) + 1
			expectedDNSPoints: 6.0, // total participants (5) + 1
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// DNF points = last finisher + 1
			actualDNFPoints := float64(tt.finishedBoats + 1)
			assert.Equal(t, tt.expectedDNFPoints, actualDNFPoints,
				"DNF points should be last finisher + 1")

			// DNS points = total participants + 1
			actualDNSPoints := float64(tt.totalParticipants + 1)
			assert.Equal(t, tt.expectedDNSPoints, actualDNSPoints,
				"DNS points should be total participants + 1")
		})
	}
}

// TestTieBreakingHierarchy tests the complete tie-breaking hierarchy
func TestTieBreakingHierarchy(t *testing.T) {
	// This test demonstrates the tie-breaking process
	boats := []struct {
		name        string
		totalPoints float64
		raceResults []float64 // Heat results in order
	}{
		{"Alex", 6.0, []float64{2.0, 1.0, 3.0}},
		{"Bella", 6.0, []float64{1.0, 3.0, 2.0}},
		{"Calle", 6.0, []float64{3.0, 2.0, 1.0}},
		{"Dora", 12.0, []float64{4.0, 4.0, 4.0}},
	}

	t.Run("Step 1: Total Points", func(t *testing.T) {
		// Dora should be clearly last with 12.0 points
		assert.True(t, boats[3].totalPoints > boats[0].totalPoints)
		assert.True(t, boats[3].totalPoints > boats[1].totalPoints)
		assert.True(t, boats[3].totalPoints > boats[2].totalPoints)
	})

	t.Run("Step 2: Best Individual Finishes", func(t *testing.T) {
		// Count 1st places for tied boats (Alex, Bella, Calle)
		firstPlaces := make([]int, 3)
		for i := 0; i < 3; i++ {
			for _, result := range boats[i].raceResults {
				if result == 1.0 {
					firstPlaces[i]++
				}
			}
		}

		// All have exactly 1 first place - still tied
		assert.Equal(t, 1, firstPlaces[0], "Alex should have 1 first place")
		assert.Equal(t, 1, firstPlaces[1], "Bella should have 1 first place")
		assert.Equal(t, 1, firstPlaces[2], "Calle should have 1 first place")
	})

	t.Run("Step 3: Most Recent Race", func(t *testing.T) {
		// Check last race (Heat 3) results
		lastRaceResults := []float64{
			boats[0].raceResults[2], // Alex: 3.0
			boats[1].raceResults[2], // Bella: 2.0
			boats[2].raceResults[2], // Calle: 1.0
		}

		// Calle wins with 1.0 in last race
		assert.Equal(t, 1.0, lastRaceResults[2], "Calle should win last race")
		assert.Equal(t, 2.0, lastRaceResults[1], "Bella should be 2nd in last race")
		assert.Equal(t, 3.0, lastRaceResults[0], "Alex should be 3rd in last race")

		// Final order should be: Calle, Bella, Alex, Dora
	})
}

// BenchmarkSharedPositionAssignment benchmarks the shared position algorithm
func BenchmarkSharedPositionAssignment(t *testing.B) {
	// Create a large dataset with many tied boats
	totalResults := make([]models.TotalResult, 1000)
	for i := 0; i < 1000; i++ {
		totalResults[i] = models.TotalResult{
			EventParticipant: models.EventParticipant{ID: int64(i + 1)},
			TotalPoints:      float64(i%10 + 1), // Create groups of tied boats
			HeatResults: []models.HeatResult{
				{Points: float64(i%5 + 1)},
				{Points: float64((i+1)%5 + 1)},
			},
		}
	}

	db := &DB{}

	t.ResetTimer()
	for i := 0; i < t.N; i++ {
		// Make a copy for each benchmark run
		testResults := make([]models.TotalResult, len(totalResults))
		copy(testResults, totalResults)

		db.assignSharedPositions(testResults)
	}
}
