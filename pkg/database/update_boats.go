package database

import (
	"database/sql"
	"log"
	"time"

	"github.com/rbjoregren/segling/pkg/models"
)

// UpdateAllBoatsFromSRSData updates all boats with the latest SRS data
func (db *DB) UpdateAllBoatsFromSRSData() error {
	log.Println("Starting to update all boats with latest SRS data...")

	// Begin a transaction to avoid database locks
	tx, err := db.Begin()
	if err != nil {
		return err
	}

	// Track if the transaction has been committed
	committed := false
	defer func() {
		if !committed && tx != nil {
			if err := tx.Rollback(); err != nil && err != sql.ErrTxDone {
				log.Printf("Error rolling back transaction: %v", err)
			}
		}
	}()

	// First, load all mätbrev data into memory for faster lookups
	log.Println("Loading all mätbrev data into memory...")
	matbrevMap := make(map[string]models.SRSMatbrev)
	matbrevRows, err := db.Query(`
		SELECT id, matbrevs_nummer, battyp, bat_namn, agare, segelnummer, nationality,
		       srs, srs_utan_undanvindsegel, srs_shorthanded, srs_shorthanded_utan_undanvindsegel,
		       created_at, updated_at
		FROM srs_matbrev
	`)
	if err != nil {
		return err
	}
	defer matbrevRows.Close()

	for matbrevRows.Next() {
		var mb models.SRSMatbrev
		var batNamn, segelnummer, nationality sql.NullString
		err := matbrevRows.Scan(
			&mb.ID, &mb.MatbrevsNummer, &mb.Battyp, &batNamn, &mb.Agare, &segelnummer, &nationality,
			&mb.SRS, &mb.SRSUtanUndanvindsegel, &mb.SRSShorthanded, &mb.SRSShorthandedUtanUndanvindsegel,
			&mb.CreatedAt, &mb.UpdatedAt,
		)
		if err != nil {
			log.Printf("Error scanning mätbrev row: %v", err)
			continue
		}

		// Convert NullString to string
		if batNamn.Valid {
			mb.BatNamn = batNamn.String
		}
		if segelnummer.Valid {
			mb.Segelnummer = segelnummer.String
		}
		if nationality.Valid {
			mb.Nationality = nationality.String
		} else {
			mb.Nationality = "SWE" // Default to SWE
		}

		matbrevMap[mb.MatbrevsNummer] = mb
	}
	log.Printf("Loaded %d mätbrev records into memory", len(matbrevMap))

	// Load all boat types into memory
	log.Println("Loading all boat types into memory...")
	boatTypeMap := make(map[string]models.SRSBoatType)
	boatTypeRows, err := db.Query(`
		SELECT id, battyp, srs, srs_utan_undanvindsegel, srs_shorthanded, srs_shorthanded_utan_undanvindsegel,
		       created_at, updated_at
		FROM srs_boat_types
	`)
	if err != nil {
		return err
	}
	defer boatTypeRows.Close()

	for boatTypeRows.Next() {
		var bt models.SRSBoatType
		err := boatTypeRows.Scan(
			&bt.ID, &bt.Battyp, &bt.SRS, &bt.SRSUtanUndanvindsegel,
			&bt.SRSShorthanded, &bt.SRSShorthandedUtanUndanvindsegel,
			&bt.CreatedAt, &bt.UpdatedAt,
		)
		if err != nil {
			log.Printf("Error scanning boat type row: %v", err)
			continue
		}
		boatTypeMap[bt.Battyp] = bt
	}
	log.Printf("Loaded %d boat type records into memory", len(boatTypeMap))

	// Prepare statements for updates
	log.Println("Preparing SQL statements...")
	matbrevStmt, err := tx.Prepare(`
		UPDATE boats
		SET battyp = ?, segelnummer = ?, nationality = ?,
			srs = ?, srs_utan_undanvindsegel = ?,
			srs_shorthanded = ?, srs_shorthanded_utan_undanvindsegel = ?,
			updated_at = ?
		WHERE matbrevs_nummer = ?
	`)
	if err != nil {
		return err
	}
	defer matbrevStmt.Close()

	boatTypeStmt, err := tx.Prepare(`
		UPDATE boats
		SET srs = ?, srs_utan_undanvindsegel = ?,
			srs_shorthanded = ?, srs_shorthanded_utan_undanvindsegel = ?,
			updated_at = ?
		WHERE battyp = ? AND (matbrevs_nummer IS NULL OR matbrevs_nummer = '')
	`)
	if err != nil {
		return err
	}
	defer boatTypeStmt.Close()

	// First, update all boats with mätbrevsnummer
	log.Println("Updating boats with mätbrevsnummer...")
	rows, err := tx.Query(`
		SELECT DISTINCT matbrevs_nummer FROM boats
		WHERE matbrevs_nummer IS NOT NULL AND matbrevs_nummer != ''
	`)
	if err != nil {
		return err
	}
	defer rows.Close()

	var matbrevUpdated int
	now := time.Now()

	// Collect all mätbrevsnummer to update
	var matbrevNumbers []string
	for rows.Next() {
		var matbrevsNummer string
		err := rows.Scan(&matbrevsNummer)
		if err != nil {
			log.Printf("Error scanning mätbrevsnummer: %v", err)
			continue
		}
		matbrevNumbers = append(matbrevNumbers, matbrevsNummer)
	}

	// Process updates in batches
	batchSize := 100
	for i := 0; i < len(matbrevNumbers); i += batchSize {
		end := i + batchSize
		if end > len(matbrevNumbers) {
			end = len(matbrevNumbers)
		}

		log.Printf("Processing mätbrev batch %d to %d of %d", i+1, end, len(matbrevNumbers))

		for _, matbrevsNummer := range matbrevNumbers[i:end] {
			// Get the mätbrev data from our in-memory map
			matbrev, ok := matbrevMap[matbrevsNummer]
			if !ok {
				log.Printf("Mätbrev data not found for %s", matbrevsNummer)
				continue
			}

			// Update the boat with the mätbrev data
			_, err = matbrevStmt.Exec(
				matbrev.Battyp, matbrev.Segelnummer, matbrev.Nationality,
				matbrev.SRS, matbrev.SRSUtanUndanvindsegel,
				matbrev.SRSShorthanded, matbrev.SRSShorthandedUtanUndanvindsegel,
				now, matbrevsNummer,
			)

			if err != nil {
				log.Printf("Error updating boat with mätbrevsnummer %s: %v", matbrevsNummer, err)
				continue
			}

			matbrevUpdated++
		}
	}

	// Then, update all boats without mätbrevsnummer
	log.Println("Updating boats without mätbrevsnummer...")
	rows, err = tx.Query(`
		SELECT DISTINCT battyp FROM boats
		WHERE (matbrevs_nummer IS NULL OR matbrevs_nummer = '') AND battyp != ''
	`)
	if err != nil {
		return err
	}
	defer rows.Close()

	var battypUpdated int

	// Collect all boat types to update
	var boatTypes []string
	for rows.Next() {
		var battyp string
		err := rows.Scan(&battyp)
		if err != nil {
			log.Printf("Error scanning båttyp: %v", err)
			continue
		}
		boatTypes = append(boatTypes, battyp)
	}

	// Process updates in batches
	for i := 0; i < len(boatTypes); i += batchSize {
		end := i + batchSize
		if end > len(boatTypes) {
			end = len(boatTypes)
		}

		log.Printf("Processing boat type batch %d to %d of %d", i+1, end, len(boatTypes))

		for _, battyp := range boatTypes[i:end] {
			// Get the SRS boat type data from our in-memory map
			srsBoatType, ok := boatTypeMap[battyp]
			if !ok {
				log.Printf("SRS boat type data not found for %s", battyp)
				continue
			}

			// Update all boats with this båttyp that don't have a mätbrevsnummer
			result, err := boatTypeStmt.Exec(
				srsBoatType.SRS, srsBoatType.SRSUtanUndanvindsegel,
				srsBoatType.SRSShorthanded, srsBoatType.SRSShorthandedUtanUndanvindsegel,
				now, battyp,
			)

			if err != nil {
				log.Printf("Error updating boats with båttyp %s: %v", battyp, err)
				continue
			}

			rowsAffected, _ := result.RowsAffected()
			battypUpdated += int(rowsAffected)
		}
	}

	// Commit the transaction
	log.Println("Committing transaction...")
	err = tx.Commit()
	if err != nil {
		return err
	}
	committed = true

	log.Printf("Updated boats from SRS data: %d from mätbrev, %d from båttyp", matbrevUpdated, battypUpdated)
	return nil
}
