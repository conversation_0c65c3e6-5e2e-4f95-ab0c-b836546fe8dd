package database

import (
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	"github.com/rb<PERSON><PERSON>gren/segling/pkg/models"
)

// UpdateParticipantFinishTime updates the finish time for a participant
func (db *DB) UpdateParticipantFinishTime(id int64, finishTime string) error {
	// TEMPORARILY DISABLED: Get the participant to check the event
	// participant, err := db.GetParticipant(id)
	// if err != nil {
	// 	return fmt.Erro<PERSON>("failed to get participant: %w", err)
	// }

	// TEMPORARILY DISABLED: Check if the event is locked
	// var locked bool
	// err = db.QueryRow(`SELECT locked FROM events WHERE id = ?`, participant.EventID).Scan(&locked)
	// if err != nil {
	// 	return fmt.Errorf("failed to check if event is locked: %w", err)
	// }

	// If the event is locked, don't allow updating finish times
	// if locked {
	// 	return fmt.Errorf("tävlingen är låst och måltider kan inte uppdateras - lås upp tävlingen først")
	// }

	// Check if the finish time has seconds
	// If it's in HH:MM format (without seconds), append ":00" for seconds
	if len(finishTime) == 5 && strings.Count(finishTime, ":") == 1 {
		finishTime = finishTime + ":00"
	}

	now := time.Now()
	_, err := db.Exec(`
		UPDATE event_participants
		SET finish_time = ?, updated_at = ?, dns = 0, dnf = 0
		WHERE id = ?
	`, finishTime, now, id)

	return err
}

// UpdateParticipantDNS updates the DNS (Did Not Start) status for a participant
func (db *DB) UpdateParticipantDNS(id int64, dns bool) error {
	// TEMPORARILY DISABLED: Get the participant to check the event
	// participant, err := db.GetParticipant(id)
	// if err != nil {
	// 	return fmt.Errorf("failed to get participant: %w", err)
	// }

	// TEMPORARILY DISABLED: Check if the event is locked
	// var locked bool
	// err = db.QueryRow(`SELECT locked FROM events WHERE id = ?`, participant.EventID).Scan(&locked)
	// if err != nil {
	// 	return fmt.Errorf("failed to check if event is locked: %w", err)
	// }

	// If the event is locked, don't allow updating DNS status
	// if locked {
	// 	return fmt.Errorf("tävlingen är låst och DNS-status kan inte uppdateras - lås upp tävlingen först")
	// }

	now := time.Now()
	var err error

	// If DNS is being set to true, clear finish time and DNF status
	if dns {
		_, err = db.Exec(`
			UPDATE event_participants
			SET dns = ?, dnf = 0, finish_time = '', updated_at = ?
			WHERE id = ?
		`, dns, now, id)
	} else {
		// If DNS is being set to false, just update the DNS status
		_, err = db.Exec(`
			UPDATE event_participants
			SET dns = ?, updated_at = ?
			WHERE id = ?
		`, dns, now, id)
	}

	return err
}

// UpdateParticipantDNF updates the DNF (Did Not Finish) status for a participant
func (db *DB) UpdateParticipantDNF(id int64, dnf bool) error {
	// TEMPORARILY DISABLED: Get the participant to check the event
	// participant, err := db.GetParticipant(id)
	// if err != nil {
	// 	return fmt.Errorf("failed to get participant: %w", err)
	// }

	// TEMPORARILY DISABLED: Check if the event is locked
	// var locked bool
	// err = db.QueryRow(`SELECT locked FROM events WHERE id = ?`, participant.EventID).Scan(&locked)
	// if err != nil {
	// 	return fmt.Errorf("failed to check if event is locked: %w", err)
	// }

	// If the event is locked, don't allow updating DNF status
	// if locked {
	// 	return fmt.Errorf("tävlingen är låst och DNF-status kan inte uppdateras - lås upp tävlingen först")
	// }

	now := time.Now()
	var err error

	// If DNF is being set to true, clear finish time and DNS status
	if dnf {
		_, err = db.Exec(`
			UPDATE event_participants
			SET dnf = ?, dns = 0, finish_time = '', updated_at = ?
			WHERE id = ?
		`, dnf, now, id)
	} else {
		// If DNF is being set to false, just update the DNF status
		_, err = db.Exec(`
			UPDATE event_participants
			SET dnf = ?, updated_at = ?
			WHERE id = ?
		`, dnf, now, id)
	}

	return err
}

// GetEventResults calculates and returns the results for an event
func (db *DB) GetEventResults(eventID int64) ([]models.Result, error) {
	// Get the event
	event, err := db.GetEvent(eventID)
	if err != nil {
		return nil, err
	}

	// Get all participants for the event
	participants, err := db.GetEventParticipants(eventID)
	if err != nil {
		return nil, err
	}

	// Parse the event start time
	baseStartTime, err := time.Parse("15:04", event.Starttid)
	if err != nil {
		// If the start time is invalid, use midnight as the base
		baseStartTime = time.Date(event.Datum.Year(), event.Datum.Month(), event.Datum.Day(), 0, 0, 0, 0, event.Datum.Location())
	}

	var results []models.Result
	var dnsResults []models.Result
	var dnfResults []models.Result

	for _, p := range participants {
		// Get the sailor information
		sailor, err := db.GetSailor(p.SailorID)
		if err != nil {
			continue
		}

		// Get the boat information
		boat, err := db.GetBoat(p.BoatID)
		if err != nil {
			continue
		}

		// Handle DNS and DNF participants separately
		if p.DNS {
			// Create a DNS result
			dnsResult := models.Result{
				EventParticipant:      p,
				Sailor:                sailor,
				Boat:                  boat,
				StartTime:             "-",
				ElapsedTime:           "-",
				CorrectedTime:         "-",
				CorrectedSeconds:      999999, // High value to sort at the end
				CorrectedSecondsFloat: 999999, // High value to sort at the end
				TimeToPrevious:        "-",
				TimeToWinner:          "-",
				TotalPersons:          1 + p.CrewCount, // 1 skipper + crew members
				DNS:                   true,
				DNF:                   false,
			}
			dnsResults = append(dnsResults, dnsResult)
			continue
		} else if p.DNF {
			// Calculate start time for DNF boats
			var startTimeStr string
			if event.IsJaktstart() {
				// Get SRS value for the boat
				var srsValue float64
				if p.UseCustomSRSValue && p.CustomSRSValue > 0 {
					srsValue = p.CustomSRSValue
				} else {
					switch p.SRSType {
					case "srs_utan_undanvindsegel":
						srsValue = boat.SRSUtanUndanvindsegel
					case "srs_shorthanded":
						srsValue = boat.SRSShorthanded
					case "srs_shorthanded_utan_undanvindsegel":
						srsValue = boat.SRSShorthandedUtanUndanvindsegel
					default:
						srsValue = boat.SRS
					}
				}

				// Calculate the start time offset in seconds with multiplier support
				multiplier := event.GetJaktstartMultiplier()
				offsetSeconds := calculateStartTimeOffsetWithMultiplier(float64(event.Banlangd), event.Vind, srsValue, db.findLowestSRS(participants), multiplier)

				// Calculate the absolute start time
				startTime := baseStartTime.Add(time.Duration(offsetSeconds) * time.Second)
				startTimeStr = startTime.Format("15:04")
			} else {
				startTimeStr = event.Starttid
			}

			// Create a DNF result
			dnfResult := models.Result{
				EventParticipant:      p,
				Sailor:                sailor,
				Boat:                  boat,
				StartTime:             startTimeStr,
				ElapsedTime:           "-",
				CorrectedTime:         "-",
				CorrectedSeconds:      999998, // High value to sort at the end, but before DNS
				CorrectedSecondsFloat: 999998, // High value to sort at the end, but before DNS
				TimeToPrevious:        "-",
				TimeToWinner:          "-",
				TotalPersons:          1 + p.CrewCount, // 1 skipper + crew members
				DNS:                   false,
				DNF:                   true,
			}
			dnfResults = append(dnfResults, dnfResult)
			continue
		}

		// Skip participants without finish times who are not DNS or DNF
		if p.FinishTime == "" {
			continue
		}

		// Get the SRS value to use
		var srsValue float64
		if p.UseCustomSRSValue && p.CustomSRSValue > 0 {
			// If using a custom SRS value, use it
			srsValue = p.CustomSRSValue
		} else {
			// Always get the correct SRS value based on the SRS type from the boat
			var boatSRSValue float64
			switch p.SRSType {
			case "srs_utan_undanvindsegel":
				boatSRSValue = boat.SRSUtanUndanvindsegel
			case "srs_shorthanded":
				boatSRSValue = boat.SRSShorthanded
			case "srs_shorthanded_utan_undanvindsegel":
				boatSRSValue = boat.SRSShorthandedUtanUndanvindsegel
			default:
				boatSRSValue = boat.SRS
			}

			// Use the boat's SRS value based on the SRS type
			srsValue = boatSRSValue
		}

		// Calculate start time based on SRS value if jaktstart is enabled
		var startTime time.Time
		var startTimeStr string
		if event.IsJaktstart() {
			// Calculate the start time offset in seconds with multiplier support
			multiplier := event.GetJaktstartMultiplier()
			offsetSeconds := calculateStartTimeOffsetWithMultiplier(float64(event.Banlangd), event.Vind, srsValue, db.findLowestSRS(participants), multiplier)

			// Calculate the absolute start time
			startTime = baseStartTime.Add(time.Duration(offsetSeconds) * time.Second)
			startTimeStr = startTime.Format("15:04")
		} else {
			// For regular races, all boats start at the same time
			startTime = baseStartTime
			startTimeStr = event.Starttid
		}

		// Parse the finish time
		finishTime, err := time.Parse("15:04:05", p.FinishTime)
		if err != nil {
			// Try parsing without seconds
			finishTime, err = time.Parse("15:04", p.FinishTime)
			if err != nil {
				continue
			}
		}

		// Calculate elapsed time
		elapsedSeconds := int(finishTime.Sub(startTime).Seconds())
		if elapsedSeconds < 0 {
			// If finish time is on the next day, add 24 hours
			elapsedSeconds += 24 * 60 * 60
		}
		elapsedHours := elapsedSeconds / 3600
		elapsedMinutes := (elapsedSeconds % 3600) / 60
		elapsedSecondsRemainder := elapsedSeconds % 60
		elapsedTimeStr := fmt.Sprintf("%02d:%02d:%02d", elapsedHours, elapsedMinutes, elapsedSecondsRemainder)

		// Calculate corrected time with full precision for sorting
		correctedSecondsFloat := float64(elapsedSeconds) * srsValue

		// Calculate corrected time using proper rounding for display
		correctedSeconds := int(math.Round(correctedSecondsFloat))
		correctedHours := correctedSeconds / 3600
		correctedMinutes := (correctedSeconds % 3600) / 60
		correctedSecondsRemainder := correctedSeconds % 60
		correctedTimeStr := fmt.Sprintf("%02d:%02d:%02d", correctedHours, correctedMinutes, correctedSecondsRemainder)

		// Calculate total number of persons (skipper + crew)
		totalPersons := 1 + p.CrewCount // 1 skipper + crew members

		// Create the result
		result := models.Result{
			EventParticipant:      p,
			Sailor:                sailor,
			Boat:                  boat,
			StartTime:             startTimeStr,
			ElapsedTime:           elapsedTimeStr,
			CorrectedTime:         correctedTimeStr,
			ElapsedSeconds:        elapsedSeconds,
			CorrectedSeconds:      correctedSeconds,
			CorrectedSecondsFloat: correctedSecondsFloat,
			TimeToPrevious:        "", // Will be calculated after sorting
			TimeToWinner:          "", // Will be calculated after sorting
			TotalPersons:          totalPersons,
			DNS:                   false,
			DNF:                   false,
		}

		results = append(results, result)
	}

	// Sort results by corrected time (ascending), using full precision for accurate sorting
	sort.Slice(results, func(i, j int) bool {
		// Compare using full precision corrected time
		if results[i].CorrectedSecondsFloat == results[j].CorrectedSecondsFloat {
			// If corrected times are exactly equal (very rare), use elapsed time as a tiebreaker
			return results[i].ElapsedSeconds < results[j].ElapsedSeconds
		}
		// Otherwise, sort by full precision corrected time
		return results[i].CorrectedSecondsFloat < results[j].CorrectedSecondsFloat
	})

	// Calculate time differences
	if len(results) > 0 {
		// First place has no time to previous, and 0 time to winner
		results[0].TimeToWinner = "00:00:00"

		// For all other places, calculate time differences
		for i := 1; i < len(results); i++ {
			// Time to winner
			diffToWinner := results[i].CorrectedSeconds - results[0].CorrectedSeconds
			winnerHours := diffToWinner / 3600
			winnerMinutes := (diffToWinner % 3600) / 60
			winnerSeconds := diffToWinner % 60
			results[i].TimeToWinner = fmt.Sprintf("%02d:%02d:%02d", winnerHours, winnerMinutes, winnerSeconds)

			// Time to previous
			diffToPrevious := results[i].CorrectedSeconds - results[i-1].CorrectedSeconds
			previousHours := diffToPrevious / 3600
			previousMinutes := (diffToPrevious % 3600) / 60
			previousSeconds := diffToPrevious % 60
			results[i].TimeToPrevious = fmt.Sprintf("%02d:%02d:%02d", previousHours, previousMinutes, previousSeconds)
		}
	}

	// Append DNF and DNS results to the end
	results = append(results, dnfResults...)
	results = append(results, dnsResults...)

	return results, nil
}

// Helper function to find the lowest SRS value among all participants
func (db *DB) findLowestSRS(participants []models.EventParticipant) float64 {
	lowestSRS := 9999.0 // Start with a high value

	for _, p := range participants {
		var srsValue float64
		if p.UseCustomSRSValue && p.CustomSRSValue > 0 {
			// If using a custom SRS value, use it
			srsValue = p.CustomSRSValue
		} else {
			// Get the boat to determine the correct SRS value based on SRS type
			boat, err := db.GetBoat(p.BoatID)
			if err != nil {
				continue
			}

			// Always use the boat's SRS value based on the SRS type
			switch p.SRSType {
			case "srs_utan_undanvindsegel":
				srsValue = boat.SRSUtanUndanvindsegel
			case "srs_shorthanded":
				srsValue = boat.SRSShorthanded
			case "srs_shorthanded_utan_undanvindsegel":
				srsValue = boat.SRSShorthandedUtanUndanvindsegel
			default:
				srsValue = boat.SRS
			}
		}

		// Skip invalid SRS values
		if srsValue <= 0 {
			continue
		}

		if srsValue < lowestSRS {
			lowestSRS = srsValue
		}
	}

	return lowestSRS
}

// Helper function to calculate start time offset in seconds
func calculateStartTimeOffset(distance float64, wind int, handicap, lowestSRS float64) int {
	return calculateStartTimeOffsetWithMultiplier(distance, wind, handicap, lowestSRS, 1.0)
}

// Helper function to calculate start time offset in seconds with multiplier support
func calculateStartTimeOffsetWithMultiplier(distance float64, wind int, handicap, lowestSRS, multiplier float64) int {
	var secondsPerNM float64

	if wind <= 3 {
		secondsPerNM = 915
	} else if wind <= 6 {
		secondsPerNM = 639
	} else {
		secondsPerNM = 548
	}

	refTime := distance * secondsPerNM

	// Calculate time relative to the boat with lowest SRS with multiplier
	startTimeOffset := int((refTime*(1/lowestSRS-1/handicap)*multiplier)/60) * 60

	return startTimeOffset
}
