package database

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	"github.com/rbjoregren/segling/pkg/models"
)

// LockEvent locks an event and saves its results
// TEMPORARILY DISABLED: Lock functionality is disabled for testing
func (db *DB) LockEvent(eventID int64) error {
	// Check if the event exists
	event, err := db.GetEvent(eventID)
	if err != nil {
		return fmt.Errorf("failed to get event: %w", err)
	}

	// Check if the event is already locked
	if event.Locked {
		return fmt.Errorf("event is already locked")
	}

	// Get heats for this event to determine if it's single or multi-heat
	heats, err := db.GetEventHeats(eventID)
	if err != nil {
		return fmt.Errorf("failed to get event heats: %w", err)
	}

	// Ensure default heat exists
	err = db.EnsureDefaultHeat(eventID)
	if err != nil {
		return fmt.Errorf("failed to ensure default heat: %w", err)
	}

	// Get heats again after ensuring default
	heats, err = db.GetEventHeats(eventID)
	if err != nil {
		return fmt.Errorf("failed to get event heats: %w", err)
	}

	var isMultiHeat bool = len(heats) > 1
	var results []models.Result
	var totalResults []models.TotalResult

	// Get the appropriate results based on event type
	if isMultiHeat {
		// For multi-heat events, get total results
		totalResults, err = db.GetEventTotalResults(eventID)
		if err != nil {
			return fmt.Errorf("failed to get event total results: %w", err)
		}

		// Check if there are any results to save
		if len(totalResults) == 0 {
			return fmt.Errorf("no results to save for this multi-heat event")
		}
	} else {
		// For single-heat events, get heat results and convert to regular results
		if len(heats) > 0 {
			heatResults, err := db.GetHeatResultsComplete(heats[0].ID)
			if err != nil {
				return fmt.Errorf("failed to get heat results: %w", err)
			}

			// Convert heat results to regular results format for saving
			results = db.convertHeatResultsToResults(heatResults)
		}

		// Check if there are any results to save
		if len(results) == 0 {
			return fmt.Errorf("no results to save for this single-heat event")
		}
	}

	// Begin a transaction
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if err := tx.Rollback(); err != nil && err != sql.ErrTxDone {
			log.Printf("Error rolling back transaction: %v", err)
		}
	}()

	// Delete any existing saved results for this event
	_, err = tx.Exec("DELETE FROM saved_results WHERE event_id = ?", eventID)
	if err != nil {
		return fmt.Errorf("failed to delete existing saved results: %w", err)
	}

	// Save the results
	now := time.Now()
	if isMultiHeat {
		// Save total results for multi-heat events
		for i, totalResult := range totalResults {
			position := i + 1

			// Determine which SRS value to use
			var srsValueToSave float64
			var useCustomSRSValue bool

			if totalResult.EventParticipant.UseCustomSRSValue && totalResult.EventParticipant.CustomSRSValue > 0 {
				// If using a custom SRS value, use it
				srsValueToSave = totalResult.EventParticipant.CustomSRSValue
				useCustomSRSValue = true
				log.Printf("DEBUG: LockEvent - Using custom SRS value %f for participant %d",
					srsValueToSave, totalResult.EventParticipant.ID)
			} else {
				// Otherwise use the selected SRS value
				srsValueToSave = totalResult.EventParticipant.SelectedSRSValue
				useCustomSRSValue = false
				log.Printf("DEBUG: LockEvent - Using selected SRS value %f for participant %d",
					srsValueToSave, totalResult.EventParticipant.ID)
			}

			_, err = tx.Exec(`
				INSERT INTO saved_results (
					event_id, position, sailor_id, sailor_name, sailor_club,
					boat_id, boat_name, boat_type, matbrevs_nummer, segelnummer, nationality,
					srs_value, srs_type, use_custom_srs_value, crew_count, start_time, finish_time,
					elapsed_time, corrected_time, time_to_previous, time_to_winner,
					elapsed_seconds, corrected_seconds, corrected_seconds_float, dns, dnf, created_at
				) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
			`,
				eventID, position, totalResult.Sailor.ID, totalResult.Sailor.Namn, totalResult.Sailor.Klubb,
				totalResult.Boat.ID, totalResult.Boat.Namn, totalResult.Boat.Battyp, totalResult.Boat.MatbrevsNummer, totalResult.Boat.Segelnummer, totalResult.Boat.Nationality,
				srsValueToSave, totalResult.EventParticipant.SRSType, useCustomSRSValue, totalResult.EventParticipant.CrewCount, "-", "-",
				"-", "-", "-", "-",
				0, 0, 0.0, false, false, now,
			)
			if err != nil {
				return fmt.Errorf("failed to save total result: %w", err)
			}
		}
	} else {
		// Save regular results for single-heat events
		for i, result := range results {
			position := i + 1

			// Determine which SRS value to use
			var srsValueToSave float64
			var useCustomSRSValue bool

			if result.EventParticipant.UseCustomSRSValue && result.EventParticipant.CustomSRSValue > 0 {
				// If using a custom SRS value, use it
				srsValueToSave = result.EventParticipant.CustomSRSValue
				useCustomSRSValue = true
				log.Printf("DEBUG: LockEvent - Using custom SRS value %f for participant %d",
					srsValueToSave, result.EventParticipant.ID)
			} else {
				// Otherwise use the selected SRS value
				srsValueToSave = result.EventParticipant.SelectedSRSValue
				useCustomSRSValue = false
				log.Printf("DEBUG: LockEvent - Using selected SRS value %f for participant %d",
					srsValueToSave, result.EventParticipant.ID)
			}

			_, err = tx.Exec(`
				INSERT INTO saved_results (
					event_id, position, sailor_id, sailor_name, sailor_club,
					boat_id, boat_name, boat_type, matbrevs_nummer, segelnummer, nationality,
					srs_value, srs_type, use_custom_srs_value, crew_count, start_time, finish_time,
					elapsed_time, corrected_time, time_to_previous, time_to_winner,
					elapsed_seconds, corrected_seconds, corrected_seconds_float, dns, dnf, created_at
				) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
			`,
				eventID, position, result.Sailor.ID, result.Sailor.Namn, result.Sailor.Klubb,
				result.Boat.ID, result.Boat.Namn, result.Boat.Battyp, result.Boat.MatbrevsNummer, result.Boat.Segelnummer, result.Boat.Nationality,
				srsValueToSave, result.EventParticipant.SRSType, useCustomSRSValue, result.EventParticipant.CrewCount, result.StartTime, result.EventParticipant.FinishTime,
				result.ElapsedTime, result.CorrectedTime, result.TimeToPrevious, result.TimeToWinner,
				result.ElapsedSeconds, result.CorrectedSeconds, result.CorrectedSecondsFloat, result.DNS, result.DNF, now,
			)
			if err != nil {
				return fmt.Errorf("failed to save result: %w", err)
			}
		}
	}

	// Lock the event
	_, err = tx.Exec("UPDATE events SET locked = 1, updated_at = ? WHERE id = ?", now, eventID)
	if err != nil {
		return fmt.Errorf("failed to lock event: %w", err)
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	if isMultiHeat {
		log.Printf("Event %d locked and %d total results saved", eventID, len(totalResults))
	} else {
		log.Printf("Event %d locked and %d results saved", eventID, len(results))
	}
	return nil
}

// UnlockEvent unlocks an event and deletes its saved results
// TEMPORARILY DISABLED: Lock functionality is disabled for testing
func (db *DB) UnlockEvent(eventID int64) error {
	// Check if the event exists
	event, err := db.GetEvent(eventID)
	if err != nil {
		return fmt.Errorf("failed to get event: %w", err)
	}

	// Check if the event is locked
	if !event.Locked {
		return fmt.Errorf("event is not locked")
	}

	// Begin a transaction
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if err := tx.Rollback(); err != nil && err != sql.ErrTxDone {
			log.Printf("Error rolling back transaction: %v", err)
		}
	}()

	// Delete saved results for this event
	_, err = tx.Exec("DELETE FROM saved_results WHERE event_id = ?", eventID)
	if err != nil {
		return fmt.Errorf("failed to delete saved results: %w", err)
	}

	// Unlock the event
	now := time.Now()
	_, err = tx.Exec("UPDATE events SET locked = 0, updated_at = ? WHERE id = ?", now, eventID)
	if err != nil {
		return fmt.Errorf("failed to unlock event: %w", err)
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	log.Printf("Event %d unlocked and saved results deleted", eventID)
	return nil
}

// GetSavedResults returns the saved results for an event
func (db *DB) GetSavedResults(eventID int64) ([]models.SavedResult, error) {
	// Check if the event exists and is locked
	event, err := db.GetEvent(eventID)
	if err != nil {
		return nil, fmt.Errorf("failed to get event: %w", err)
	}

	if !event.Locked {
		return nil, fmt.Errorf("event is not locked, no saved results available")
	}

	// Check if the use_custom_srs_value column exists
	var hasUseCustomSRSValue int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('saved_results')
		WHERE name = 'use_custom_srs_value'
	`).Scan(&hasUseCustomSRSValue)

	if err != nil {
		return nil, fmt.Errorf("failed to check if use_custom_srs_value column exists: %w", err)
	}

	// Check if the dns and dnf columns exist
	var hasDNSAndDNF int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('saved_results')
		WHERE name = 'dns' OR name = 'dnf'
	`).Scan(&hasDNSAndDNF)

	if err != nil {
		return nil, fmt.Errorf("failed to check if dns and dnf columns exist: %w", err)
	}

	// Check if the corrected_seconds_float column exists
	var hasCorrectedSecondsFloat int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('saved_results')
		WHERE name = 'corrected_seconds_float'
	`).Scan(&hasCorrectedSecondsFloat)

	if err != nil {
		return nil, fmt.Errorf("failed to check if corrected_seconds_float column exists: %w", err)
	}

	// Query the saved results
	var rows *sql.Rows
	if hasUseCustomSRSValue > 0 && hasDNSAndDNF > 0 && hasCorrectedSecondsFloat > 0 {
		// If all new columns exist, include them in the query
		rows, err = db.Query(`
			SELECT id, event_id, position, sailor_id, sailor_name, sailor_club,
				   boat_id, boat_name, boat_type, matbrevs_nummer, segelnummer, nationality,
				   srs_value, srs_type, use_custom_srs_value, crew_count, start_time, finish_time,
				   elapsed_time, corrected_time, time_to_previous, time_to_winner,
				   elapsed_seconds, corrected_seconds, corrected_seconds_float, dns, dnf, created_at
			FROM saved_results
			WHERE event_id = ?
			ORDER BY position
		`, eventID)
	} else if hasUseCustomSRSValue > 0 && hasDNSAndDNF > 0 {
		// If use_custom_srs_value and DNS/DNF exist but not corrected_seconds_float
		rows, err = db.Query(`
			SELECT id, event_id, position, sailor_id, sailor_name, sailor_club,
				   boat_id, boat_name, boat_type, matbrevs_nummer, segelnummer, nationality,
				   srs_value, srs_type, use_custom_srs_value, crew_count, start_time, finish_time,
				   elapsed_time, corrected_time, time_to_previous, time_to_winner,
				   elapsed_seconds, corrected_seconds, CAST(corrected_seconds AS REAL) as corrected_seconds_float, dns, dnf, created_at
			FROM saved_results
			WHERE event_id = ?
			ORDER BY position
		`, eventID)
	} else if hasUseCustomSRSValue > 0 {
		// If only use_custom_srs_value exists, include it in the query
		rows, err = db.Query(`
			SELECT id, event_id, position, sailor_id, sailor_name, sailor_club,
				   boat_id, boat_name, boat_type, matbrevs_nummer, segelnummer, nationality,
				   srs_value, srs_type, use_custom_srs_value, crew_count, start_time, finish_time,
				   elapsed_time, corrected_time, time_to_previous, time_to_winner,
				   elapsed_seconds, corrected_seconds, CAST(corrected_seconds AS REAL) as corrected_seconds_float, created_at
			FROM saved_results
			WHERE event_id = ?
			ORDER BY position
		`, eventID)
	} else {
		// Otherwise, use the old schema
		rows, err = db.Query(`
			SELECT id, event_id, position, sailor_id, sailor_name, sailor_club,
				   boat_id, boat_name, boat_type, matbrevs_nummer, segelnummer, nationality,
				   srs_value, srs_type, crew_count, start_time, finish_time,
				   elapsed_time, corrected_time, time_to_previous, time_to_winner,
				   0 as elapsed_seconds, corrected_seconds, CAST(corrected_seconds AS REAL) as corrected_seconds_float, created_at
			FROM saved_results
			WHERE event_id = ?
			ORDER BY position
		`, eventID)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to query saved results: %w", err)
	}
	defer rows.Close()

	var results []models.SavedResult
	for rows.Next() {
		var r models.SavedResult

		if hasUseCustomSRSValue > 0 && hasDNSAndDNF > 0 && hasCorrectedSecondsFloat > 0 {
			// If all new columns exist, scan them
			err = rows.Scan(
				&r.ID, &r.EventID, &r.Position, &r.SailorID, &r.SailorName, &r.SailorClub,
				&r.BoatID, &r.BoatName, &r.BoatType, &r.MatbrevsNummer, &r.Segelnummer, &r.Nationality,
				&r.SRSValue, &r.SRSType, &r.UseCustomSRSValue, &r.CrewCount, &r.StartTime, &r.FinishTime,
				&r.ElapsedTime, &r.CorrectedTime, &r.TimeToPrevious, &r.TimeToWinner,
				&r.ElapsedSeconds, &r.CorrectedSeconds, &r.CorrectedSecondsFloat, &r.DNS, &r.DNF, &r.CreatedAt,
			)
		} else if hasUseCustomSRSValue > 0 && hasDNSAndDNF > 0 {
			// If use_custom_srs_value and DNS/DNF exist but not corrected_seconds_float
			err = rows.Scan(
				&r.ID, &r.EventID, &r.Position, &r.SailorID, &r.SailorName, &r.SailorClub,
				&r.BoatID, &r.BoatName, &r.BoatType, &r.MatbrevsNummer, &r.Segelnummer, &r.Nationality,
				&r.SRSValue, &r.SRSType, &r.UseCustomSRSValue, &r.CrewCount, &r.StartTime, &r.FinishTime,
				&r.ElapsedTime, &r.CorrectedTime, &r.TimeToPrevious, &r.TimeToWinner,
				&r.ElapsedSeconds, &r.CorrectedSeconds, &r.CorrectedSecondsFloat, &r.DNS, &r.DNF, &r.CreatedAt,
			)
		} else if hasUseCustomSRSValue > 0 {
			// If only use_custom_srs_value exists, scan it
			err = rows.Scan(
				&r.ID, &r.EventID, &r.Position, &r.SailorID, &r.SailorName, &r.SailorClub,
				&r.BoatID, &r.BoatName, &r.BoatType, &r.MatbrevsNummer, &r.Segelnummer, &r.Nationality,
				&r.SRSValue, &r.SRSType, &r.UseCustomSRSValue, &r.CrewCount, &r.StartTime, &r.FinishTime,
				&r.ElapsedTime, &r.CorrectedTime, &r.TimeToPrevious, &r.TimeToWinner,
				&r.ElapsedSeconds, &r.CorrectedSeconds, &r.CorrectedSecondsFloat, &r.CreatedAt,
			)
			// Set DNS and DNF to false for backward compatibility
			r.DNS = false
			r.DNF = false
		} else {
			// Otherwise, use the old schema
			err = rows.Scan(
				&r.ID, &r.EventID, &r.Position, &r.SailorID, &r.SailorName, &r.SailorClub,
				&r.BoatID, &r.BoatName, &r.BoatType, &r.MatbrevsNummer, &r.Segelnummer, &r.Nationality,
				&r.SRSValue, &r.SRSType, &r.CrewCount, &r.StartTime, &r.FinishTime,
				&r.ElapsedTime, &r.CorrectedTime, &r.TimeToPrevious, &r.TimeToWinner,
				&r.ElapsedSeconds, &r.CorrectedSeconds, &r.CorrectedSecondsFloat, &r.CreatedAt,
			)
			// Set UseCustomSRSValue, DNS, and DNF to false for backward compatibility
			r.UseCustomSRSValue = false
			r.DNS = false
			r.DNF = false
		}

		if err != nil {
			return nil, fmt.Errorf("failed to scan saved result: %w", err)
		}
		results = append(results, r)
	}

	return results, nil
}

// convertHeatResultsToResults converts heat results to regular results format for saving
func (db *DB) convertHeatResultsToResults(heatResults []models.HeatResult) []models.Result {
	var results []models.Result

	for _, hr := range heatResults {
		// Create a copy of the EventParticipant and set the finish time from the heat result
		eventParticipant := hr.EventParticipant
		eventParticipant.FinishTime = hr.FinishTime
		eventParticipant.DNS = hr.DNS
		eventParticipant.DNF = hr.DNF

		result := models.Result{
			EventParticipant:      eventParticipant,
			Sailor:                hr.Sailor,
			Boat:                  hr.Boat,
			StartTime:             hr.StartTime,
			ElapsedTime:           hr.ElapsedTime,
			CorrectedTime:         hr.CorrectedTime,
			ElapsedSeconds:        hr.ElapsedSeconds,
			CorrectedSeconds:      hr.CorrectedSeconds,
			CorrectedSecondsFloat: hr.CorrectedSecondsFloat,
			TimeToPrevious:        hr.TimeToPrevious,
			TimeToWinner:          hr.TimeToWinner,
			TotalPersons:          hr.TotalPersons,
			DNS:                   hr.DNS,
			DNF:                   hr.DNF,
		}
		results = append(results, result)
	}

	return results
}
