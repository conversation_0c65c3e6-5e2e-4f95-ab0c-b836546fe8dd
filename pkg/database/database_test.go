package database

import (
	"os"
	"testing"
	"time"
)

// TestDB is a test database file
const TestDB = "test_segling.db"

// setupTestDB creates a test database
func setupTestDB(t *testing.T) *DB {
	// Remove any existing test database
	os.Remove(TestDB)

	// Create a new test database
	db, err := New(TestDB)
	if err != nil {
		t.Fatalf("Error creating test database: %v", err)
	}

	// Initialize the database
	if err := db.Initialize(); err != nil {
		t.Fatalf("Error initializing test database: %v", err)
	}

	return db
}

// teardownTestDB closes and removes the test database
func teardownTestDB(db *DB) {
	if db != nil {
		db.Close()
	}
	os.Remove(TestDB)
}

// TestDatabaseConnection tests the database connection
func TestDatabaseConnection(t *testing.T) {
	db := setupTestDB(t)
	defer teardownTestDB(db)

	// Test the connection
	if err := db.Ping(); err != nil {
		t.Fatalf("Database connection failed: %v", err)
	}
}

// TestInitialize tests the database initialization
func TestInitialize(t *testing.T) {
	db := setupTestDB(t)
	defer teardownTestDB(db)

	// Check if tables exist
	tables := []string{"sailors", "boats", "events", "event_participants", "srs_boat_types", "srs_matbrev", "srs_sync_log"}
	for _, table := range tables {
		var count int
		err := db.QueryRow("SELECT count(*) FROM sqlite_master WHERE type='table' AND name=?", table).Scan(&count)
		if err != nil {
			t.Fatalf("Error checking if table %s exists: %v", table, err)
		}
		if count != 1 {
			t.Errorf("Table %s does not exist", table)
		}
	}
}

// TestSailorCRUD tests CRUD operations for sailors
func TestSailorCRUD(t *testing.T) {
	db := setupTestDB(t)
	defer teardownTestDB(db)

	// Create a sailor
	sailorName := "Test Sailor"
	sailorPhone := "123456789"
	sailorKlubb := "LSS"
	id, err := db.CreateSailor(sailorName, sailorPhone, sailorKlubb)
	if err != nil {
		t.Fatalf("Error creating sailor: %v", err)
	}
	if id <= 0 {
		t.Errorf("Expected positive ID, got %d", id)
	}

	// Read the sailor
	sailor, err := db.GetSailor(id)
	if err != nil {
		t.Fatalf("Error getting sailor: %v", err)
	}
	if sailor.Namn != sailorName {
		t.Errorf("Expected sailor name %s, got %s", sailorName, sailor.Namn)
	}
	if sailor.Telefon != sailorPhone {
		t.Errorf("Expected sailor phone %s, got %s", sailorPhone, sailor.Telefon)
	}
	if sailor.Klubb != sailorKlubb {
		t.Errorf("Expected sailor klubb %s, got %s", sailorKlubb, sailor.Klubb)
	}

	// Update the sailor
	updatedName := "Updated Sailor"
	updatedPhone := "987654321"
	updatedKlubb := "KSSS"
	err = db.UpdateSailor(id, updatedName, updatedPhone, updatedKlubb)
	if err != nil {
		t.Fatalf("Error updating sailor: %v", err)
	}

	// Read the updated sailor
	updatedSailor, err := db.GetSailor(id)
	if err != nil {
		t.Fatalf("Error getting updated sailor: %v", err)
	}
	if updatedSailor.Namn != updatedName {
		t.Errorf("Expected updated sailor name %s, got %s", updatedName, updatedSailor.Namn)
	}
	if updatedSailor.Telefon != updatedPhone {
		t.Errorf("Expected updated sailor phone %s, got %s", updatedPhone, updatedSailor.Telefon)
	}
	if updatedSailor.Klubb != updatedKlubb {
		t.Errorf("Expected updated sailor klubb %s, got %s", updatedKlubb, updatedSailor.Klubb)
	}

	// Delete the sailor
	err = db.DeleteSailor(id)
	if err != nil {
		t.Fatalf("Error deleting sailor: %v", err)
	}

	// Try to read the deleted sailor
	_, err = db.GetSailor(id)
	if err == nil {
		t.Errorf("Expected error when getting deleted sailor, got nil")
	}
}

// TestBoatCRUD tests CRUD operations for boats
func TestBoatCRUD(t *testing.T) {
	db := setupTestDB(t)
	defer teardownTestDB(db)

	// Create a boat
	boatName := "Test Boat"
	boatType := "Test Type"
	matbrevsNummer := "TEST123"
	segelnummer := "123"
	nationality := "SWE"
	srs := 1.05
	srsUU := 1.0
	srsSH := 0.95
	srsSHUU := 0.9

	id, err := db.CreateBoat(boatName, boatType, matbrevsNummer, segelnummer, nationality, srs, srsUU, srsSH, srsSHUU)
	if err != nil {
		t.Fatalf("Error creating boat: %v", err)
	}
	if id <= 0 {
		t.Errorf("Expected positive ID, got %d", id)
	}

	// Read the boat
	boat, err := db.GetBoat(id)
	if err != nil {
		t.Fatalf("Error getting boat: %v", err)
	}
	if boat.Namn != boatName {
		t.Errorf("Expected boat name %s, got %s", boatName, boat.Namn)
	}
	if boat.Battyp != boatType {
		t.Errorf("Expected boat type %s, got %s", boatType, boat.Battyp)
	}
	if boat.MatbrevsNummer != matbrevsNummer {
		t.Errorf("Expected matbrevs nummer %s, got %s", matbrevsNummer, boat.MatbrevsNummer)
	}
	if boat.SRS != srs {
		t.Errorf("Expected SRS %f, got %f", srs, boat.SRS)
	}

	// Update the boat
	updatedName := "Updated Boat"
	updatedType := "Updated Type"
	updatedMatbrevsNummer := "UPDATED123"
	updatedSegelnummer := "456"
	updatedNationality := "NOR"
	updatedSRS := 1.1
	updatedSRSUU := 1.05
	updatedSRSSH := 1.0
	updatedSRSSHUU := 0.95

	err = db.UpdateBoat(id, updatedName, updatedType, updatedMatbrevsNummer, updatedSegelnummer, updatedNationality, updatedSRS, updatedSRSUU, updatedSRSSH, updatedSRSSHUU)
	if err != nil {
		t.Fatalf("Error updating boat: %v", err)
	}

	// Read the updated boat
	updatedBoat, err := db.GetBoat(id)
	if err != nil {
		t.Fatalf("Error getting updated boat: %v", err)
	}
	if updatedBoat.Namn != updatedName {
		t.Errorf("Expected updated boat name %s, got %s", updatedName, updatedBoat.Namn)
	}
	if updatedBoat.Battyp != updatedType {
		t.Errorf("Expected updated boat type %s, got %s", updatedType, updatedBoat.Battyp)
	}

	// Delete the boat
	err = db.DeleteBoat(id)
	if err != nil {
		t.Fatalf("Error deleting boat: %v", err)
	}

	// Try to read the deleted boat
	_, err = db.GetBoat(id)
	if err == nil {
		t.Errorf("Expected error when getting deleted boat, got nil")
	}
}

// TestEventCRUD tests CRUD operations for events
func TestEventCRUD(t *testing.T) {
	db := setupTestDB(t)
	defer teardownTestDB(db)

	// Create an event
	eventName := "Test Event"
	eventDate := time.Now().AddDate(0, 0, 7) // One week from now
	eventStartTime := "12:00"
	eventWind := 5
	eventCourseLength := 10
	eventJaktstart := true
	eventTavlingstyp := "Kvällssegling"
	eventDescription := "Test Description"

	id, err := db.CreateEvent(eventName, eventDate, eventStartTime, eventWind, eventCourseLength, eventJaktstart, eventTavlingstyp, eventDescription)
	if err != nil {
		t.Fatalf("Error creating event: %v", err)
	}
	if id <= 0 {
		t.Errorf("Expected positive ID, got %d", id)
	}

	// Read the event
	event, err := db.GetEvent(id)
	if err != nil {
		t.Fatalf("Error getting event: %v", err)
	}
	if event.Namn != eventName {
		t.Errorf("Expected event name %s, got %s", eventName, event.Namn)
	}
	if event.Beskrivning != eventDescription {
		t.Errorf("Expected event description %s, got %s", eventDescription, event.Beskrivning)
	}

	// Update the event
	updatedName := "Updated Event"
	updatedDate := time.Now().AddDate(0, 0, 14) // Two weeks from now
	updatedStartTime := "13:00"
	updatedWind := 7
	updatedCourseLength := 12
	updatedJaktstart := false
	updatedTavlingstyp := "Regatta"
	updatedDescription := "Updated Description"

	err = db.UpdateEvent(id, updatedName, updatedDate, updatedStartTime, updatedWind, updatedCourseLength, updatedJaktstart, updatedTavlingstyp, updatedDescription)
	if err != nil {
		t.Fatalf("Error updating event: %v", err)
	}

	// Read the updated event
	updatedEvent, err := db.GetEvent(id)
	if err != nil {
		t.Fatalf("Error getting updated event: %v", err)
	}
	if updatedEvent.Namn != updatedName {
		t.Errorf("Expected updated event name %s, got %s", updatedName, updatedEvent.Namn)
	}
	if updatedEvent.Beskrivning != updatedDescription {
		t.Errorf("Expected updated event description %s, got %s", updatedDescription, updatedEvent.Beskrivning)
	}

	// Delete the event
	err = db.DeleteEvent(id)
	if err != nil {
		t.Fatalf("Error deleting event: %v", err)
	}

	// Try to read the deleted event
	_, err = db.GetEvent(id)
	if err == nil {
		t.Errorf("Expected error when getting deleted event, got nil")
	}
}

// TestHeatSortingTiebreaker tests the heat sorting tiebreaker logic
func TestHeatSortingTiebreaker(t *testing.T) {
	db := setupTestDB(t)
	defer teardownTestDB(db)

	// Create an event
	eventName := "Test Multi-Heat Event"
	eventDate := time.Now().AddDate(0, 0, 7)
	eventStartTime := "12:00"
	eventWind := 5
	eventCourseLength := 10
	eventJaktstart := false
	eventTavlingstyp := "Test"
	eventDescription := "Test event for heat sorting"

	eventID, err := db.CreateEvent(eventName, eventDate, eventStartTime, eventWind, eventCourseLength, eventJaktstart, eventTavlingstyp, eventDescription)
	if err != nil {
		t.Fatalf("Error creating event: %v", err)
	}

	// Create two sailors
	sailor1ID, err := db.CreateSailor("Sailor 1", "123", "LSS")
	if err != nil {
		t.Fatalf("Error creating sailor 1: %v", err)
	}

	sailor2ID, err := db.CreateSailor("Sailor 2", "456", "LSS")
	if err != nil {
		t.Fatalf("Error creating sailor 2: %v", err)
	}

	// Create two boats
	boat1ID, err := db.CreateBoat("Boat 1", "Type 1", "MAT1", "1", "SWE", 1.0, 1.0, 1.0, 1.0)
	if err != nil {
		t.Fatalf("Error creating boat 1: %v", err)
	}

	boat2ID, err := db.CreateBoat("Boat 2", "Type 2", "MAT2", "2", "SWE", 1.0, 1.0, 1.0, 1.0)
	if err != nil {
		t.Fatalf("Error creating boat 2: %v", err)
	}

	// Add participants to the event
	participant1ID, err := db.AddParticipantToEvent(eventID, sailor1ID, boat1ID, "srs", 1.0, 0, false)
	if err != nil {
		t.Fatalf("Error adding participant 1: %v", err)
	}

	participant2ID, err := db.AddParticipantToEvent(eventID, sailor2ID, boat2ID, "srs", 1.0, 0, false)
	if err != nil {
		t.Fatalf("Error adding participant 2: %v", err)
	}

	// Create two heats
	heat1ID, err := db.CreateHeat(eventID, 1, "Heat 1", "12:00")
	if err != nil {
		t.Fatalf("Error creating heat 1: %v", err)
	}

	heat2ID, err := db.CreateHeat(eventID, 2, "Heat 2", "12:00")
	if err != nil {
		t.Fatalf("Error creating heat 2: %v", err)
	}

	// Add finish times for Heat 1: Participant 1 = 1st place, Participant 2 = 2nd place
	err = db.UpdateHeatFinishTime(heat1ID, participant1ID, "12:04:00", false, false)
	if err != nil {
		t.Fatalf("Error updating heat 1 finish time for participant 1: %v", err)
	}

	err = db.UpdateHeatFinishTime(heat1ID, participant2ID, "12:05:00", false, false)
	if err != nil {
		t.Fatalf("Error updating heat 1 finish time for participant 2: %v", err)
	}

	// Add finish times for Heat 2: Participant 1 = 2nd place, Participant 2 = 1st place
	err = db.UpdateHeatFinishTime(heat2ID, participant1ID, "12:05:00", false, false)
	if err != nil {
		t.Fatalf("Error updating heat 2 finish time for participant 1: %v", err)
	}

	err = db.UpdateHeatFinishTime(heat2ID, participant2ID, "12:04:00", false, false)
	if err != nil {
		t.Fatalf("Error updating heat 2 finish time for participant 2: %v", err)
	}

	// Get total results
	totalResults, err := db.GetEventTotalResults(eventID)
	if err != nil {
		t.Fatalf("Error getting total results: %v", err)
	}

	if len(totalResults) != 2 {
		t.Fatalf("Expected 2 total results, got %d", len(totalResults))
	}

	// Debug: Print heat results for each participant
	for i, result := range totalResults {
		t.Logf("Participant %d (ID: %d): Total Points: %.1f", i+1, result.EventParticipant.ID, result.TotalPoints)
		for j, heatResult := range result.HeatResults {
			t.Logf("  Heat %d (HeatNumber: %d): Position %d, Points %.1f", j+1, heatResult.Heat.HeatNumber, heatResult.Position, heatResult.Points)
		}
	}

	// Both participants should have 3 total points (1+2 and 2+1)
	// Since they have identical sorted results [1,2], the tiebreaker goes to most recent race result
	if totalResults[0].TotalPoints != 3 || totalResults[1].TotalPoints != 3 {
		t.Errorf("Expected both participants to have 3 points, got %.1f and %.1f",
			totalResults[0].TotalPoints, totalResults[1].TotalPoints)
	}

	// Participant 2 should be ranked first due to winning the most recent race (Heat 2)
	// Participant 2 (ID: 2) got 1st place in Heat 2, Participant 1 (ID: 1) got 2nd place in Heat 2
	if totalResults[0].EventParticipant.ID != participant2ID {
		t.Errorf("Expected participant 2 (ID: %d) to be ranked first due to winning most recent race, but participant %d was ranked first",
			participant2ID, totalResults[0].EventParticipant.ID)
	}

	// Participant 1 should be ranked second
	if totalResults[1].EventParticipant.ID != participant1ID {
		t.Errorf("Expected participant 1 (ID: %d) to be ranked second, but participant %d was ranked second",
			participant1ID, totalResults[1].EventParticipant.ID)
	}

	t.Logf("Test passed: Both participants have identical total points [3] and sorted results [1,2], so participant 2 (ID: %d) ranked before participant 1 (ID: %d) due to winning most recent race (Heat 2: 1st vs 2nd)", participant2ID, participant1ID)
}

// TestHeatSortingRealScenario tests the exact scenario described in the issue: 2+2 vs 3+1 points
func TestHeatSortingRealScenario(t *testing.T) {
	db := setupTestDB(t)
	defer teardownTestDB(db)

	// Create an event
	eventName := "Test Real Scenario Event"
	eventDate := time.Now().AddDate(0, 0, 7)
	eventStartTime := "12:00"
	eventWind := 5
	eventCourseLength := 10
	eventJaktstart := false
	eventTavlingstyp := "Test"
	eventDescription := "Test event for real scenario"

	eventID, err := db.CreateEvent(eventName, eventDate, eventStartTime, eventWind, eventCourseLength, eventJaktstart, eventTavlingstyp, eventDescription)
	if err != nil {
		t.Fatalf("Error creating event: %v", err)
	}

	// Create three sailors
	sailor1ID, err := db.CreateSailor("Sailor 1", "123", "LSS")
	if err != nil {
		t.Fatalf("Error creating sailor 1: %v", err)
	}

	sailor2ID, err := db.CreateSailor("Sailor 2", "456", "LSS")
	if err != nil {
		t.Fatalf("Error creating sailor 2: %v", err)
	}

	sailor3ID, err := db.CreateSailor("Sailor 3", "789", "LSS")
	if err != nil {
		t.Fatalf("Error creating sailor 3: %v", err)
	}

	// Create three boats
	boat1ID, err := db.CreateBoat("Boat 1", "Type 1", "MAT1", "1", "SWE", 1.0, 1.0, 1.0, 1.0)
	if err != nil {
		t.Fatalf("Error creating boat 1: %v", err)
	}

	boat2ID, err := db.CreateBoat("Boat 2", "Type 2", "MAT2", "2", "SWE", 1.0, 1.0, 1.0, 1.0)
	if err != nil {
		t.Fatalf("Error creating boat 2: %v", err)
	}

	boat3ID, err := db.CreateBoat("Boat 3", "Type 3", "MAT3", "3", "SWE", 1.0, 1.0, 1.0, 1.0)
	if err != nil {
		t.Fatalf("Error creating boat 3: %v", err)
	}

	// Add participants to the event
	participant1ID, err := db.AddParticipantToEvent(eventID, sailor1ID, boat1ID, "srs", 1.0, 0, false)
	if err != nil {
		t.Fatalf("Error adding participant 1: %v", err)
	}

	participant2ID, err := db.AddParticipantToEvent(eventID, sailor2ID, boat2ID, "srs", 1.0, 0, false)
	if err != nil {
		t.Fatalf("Error adding participant 2: %v", err)
	}

	participant3ID, err := db.AddParticipantToEvent(eventID, sailor3ID, boat3ID, "srs", 1.0, 0, false)
	if err != nil {
		t.Fatalf("Error adding participant 3: %v", err)
	}

	// Create two heats
	heat1ID, err := db.CreateHeat(eventID, 1, "Heat 1", "12:00")
	if err != nil {
		t.Fatalf("Error creating heat 1: %v", err)
	}

	heat2ID, err := db.CreateHeat(eventID, 2, "Heat 2", "12:00")
	if err != nil {
		t.Fatalf("Error creating heat 2: %v", err)
	}

	// Heat 1 results: Participant 1 = 2nd, Participant 2 = 3rd, Participant 3 = 1st
	err = db.UpdateHeatFinishTime(heat1ID, participant1ID, "12:05:00", false, false)
	if err != nil {
		t.Fatalf("Error updating heat 1 finish time for participant 1: %v", err)
	}

	err = db.UpdateHeatFinishTime(heat1ID, participant2ID, "12:06:00", false, false)
	if err != nil {
		t.Fatalf("Error updating heat 1 finish time for participant 2: %v", err)
	}

	err = db.UpdateHeatFinishTime(heat1ID, participant3ID, "12:04:00", false, false)
	if err != nil {
		t.Fatalf("Error updating heat 1 finish time for participant 3: %v", err)
	}

	// Heat 2 results: Participant 1 = 2nd, Participant 2 = 1st, Participant 3 = 3rd
	err = db.UpdateHeatFinishTime(heat2ID, participant1ID, "12:05:00", false, false)
	if err != nil {
		t.Fatalf("Error updating heat 2 finish time for participant 1: %v", err)
	}

	err = db.UpdateHeatFinishTime(heat2ID, participant2ID, "12:04:00", false, false)
	if err != nil {
		t.Fatalf("Error updating heat 2 finish time for participant 2: %v", err)
	}

	err = db.UpdateHeatFinishTime(heat2ID, participant3ID, "12:06:00", false, false)
	if err != nil {
		t.Fatalf("Error updating heat 2 finish time for participant 3: %v", err)
	}

	// Get total results
	totalResults, err := db.GetEventTotalResults(eventID)
	if err != nil {
		t.Fatalf("Error getting total results: %v", err)
	}

	if len(totalResults) != 3 {
		t.Fatalf("Expected 3 total results, got %d", len(totalResults))
	}

	// Debug: Print heat results for each participant
	for i, result := range totalResults {
		t.Logf("Participant %d (ID: %d): Total Points: %.1f", i+1, result.EventParticipant.ID, result.TotalPoints)
		for j, heatResult := range result.HeatResults {
			t.Logf("  Heat %d: Position %d, Points %.1f", j+1, heatResult.Position, heatResult.Points)
		}
	}

	// Expected results:
	// Participant 1: 2nd + 2nd = 2 + 2 = 4 points, finish counts: [0, 2, 0] (0 firsts, 2 seconds, 0 thirds)
	// Participant 2: 3rd + 1st = 3 + 1 = 4 points, finish counts: [1, 0, 1] (1 first, 0 seconds, 1 third)
	// Participant 3: 1st + 3rd = 1 + 3 = 4 points, finish counts: [1, 0, 1] (1 first, 0 seconds, 1 third)

	// All should have 4 points
	for i, result := range totalResults {
		if result.TotalPoints != 4 {
			t.Errorf("Expected participant %d to have 4 points, got %.1f", i+1, result.TotalPoints)
		}
	}

	// Participant 2 should be ranked first because they have 1 first place finish
	// When tied with participant 3 on first places, participant 2 wins on most recent race (1st vs 3rd in Heat 2)
	if totalResults[0].EventParticipant.ID != participant2ID {
		t.Errorf("Expected participant 2 to be ranked first due to having 1 first place and better most recent race result, but participant %d was ranked first",
			totalResults[0].EventParticipant.ID)
	}

	// Participant 3 should be ranked second (also has 1 first place but worse most recent race result)
	if totalResults[1].EventParticipant.ID != participant3ID {
		t.Errorf("Expected participant 3 to be ranked second, but participant %d was ranked second",
			totalResults[1].EventParticipant.ID)
	}

	// Participant 1 should be ranked third (no first place finishes, only 2nd places)
	if totalResults[2].EventParticipant.ID != participant1ID {
		t.Errorf("Expected participant 1 to be ranked third due to no first place finishes, but participant %d was ranked third",
			totalResults[2].EventParticipant.ID)
	}

	t.Logf("Test passed: Correct sailing tiebreaker - participants with 1st place finishes ranked higher than participant with only 2nd place finishes")
}
