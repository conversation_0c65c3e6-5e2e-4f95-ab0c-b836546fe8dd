package database

import (
	"fmt"
	"testing"
	"time"

	"github.com/rbjoregren/segling/pkg/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestDNSDNFScoringRRS tests DNS and DNF scoring according to Racing Rules of Sailing (RRS) Appendix A
// This test verifies that both regular sailing and entypsegling events follow the correct scoring rules:
// - DNF points = antal startande + 1 (last finisher + 1)
// - DNS points = antal anmälda + 1 (total participants + 1)
func TestDNSDNFScoringRRS(t *testing.T) {
	// Setup test database
	db := setupTestDB(t)
	defer teardownTestDB(db)

	// Test scenarios for both regular sailing and entypsegling
	testCases := []struct {
		name              string
		isEntypsegling    bool
		totalParticipants int
		finishedBoats     int
		dnfBoats          int
		dnsBoats          int
		expectedDNFPoints float64
		expectedDNSPoints float64
	}{
		{
			name:              "Regular sailing: 6 participants, 4 finished, 1 DNF, 1 DNS",
			isEntypsegling:    false,
			totalParticipants: 6,
			finishedBoats:     4,
			dnfBoats:          1,
			dnsBoats:          1,
			expectedDNFPoints: 5.0, // last finisher (4th) + 1
			expectedDNSPoints: 7.0, // total participants (6) + 1
		},
		{
			name:              "Entypsegling: 6 participants, 4 finished, 1 DNF, 1 DNS",
			isEntypsegling:    true,
			totalParticipants: 6,
			finishedBoats:     4,
			dnfBoats:          1,
			dnsBoats:          1,
			expectedDNFPoints: 5.0, // last finisher (4th) + 1
			expectedDNSPoints: 7.0, // total participants (6) + 1
		},
		{
			name:              "Regular sailing: 8 participants, 5 finished, 2 DNF, 1 DNS",
			isEntypsegling:    false,
			totalParticipants: 8,
			finishedBoats:     5,
			dnfBoats:          2,
			dnsBoats:          1,
			expectedDNFPoints: 6.0, // last finisher (5th) + 1
			expectedDNSPoints: 9.0, // total participants (8) + 1
		},
		{
			name:              "Entypsegling: 8 participants, 5 finished, 2 DNF, 1 DNS",
			isEntypsegling:    true,
			totalParticipants: 8,
			finishedBoats:     5,
			dnfBoats:          2,
			dnsBoats:          1,
			expectedDNFPoints: 6.0, // last finisher (5th) + 1
			expectedDNSPoints: 9.0, // total participants (8) + 1
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create test event
			var eventID int64
			var err error
			if tc.isEntypsegling {
				eventID, err = db.CreateEventWithEntypsegling(
					tc.name, time.Now(), "12:00", 5, 10, false, "Test", "Test Description", true, "Test Boat Type")
			} else {
				eventID, err = db.CreateEvent(
					tc.name, time.Now(), "12:00", 5, 10, false, "Test", "Test Description")
			}
			require.NoError(t, err)

			// Create test sailors and boats
			sailorIDs := make([]int64, tc.totalParticipants)
			boatIDs := make([]int64, tc.totalParticipants)
			participantIDs := make([]int64, tc.totalParticipants)

			for i := 0; i < tc.totalParticipants; i++ {
				// Create sailor
				sailorID, err := db.CreateSailor(fmt.Sprintf("Sailor %d", i+1), "123456789", "Test Club")
				require.NoError(t, err)
				sailorIDs[i] = sailorID

				// Create boat (only for regular sailing)
				if !tc.isEntypsegling {
					boatID, err := db.CreateBoat(
						fmt.Sprintf("Boat %d", i+1), "Test Type", fmt.Sprintf("MAT%d", i+1),
						fmt.Sprintf("SWE-%d", i+1), "SWE", 1.0, 1.0, 1.0, 1.0)
					require.NoError(t, err)
					boatIDs[i] = boatID
				}

				// Create participant
				var participantID int64
				if !tc.isEntypsegling {
					participantID, err = db.AddParticipantToEvent(eventID, sailorIDs[i], boatIDs[i], "srs", 1.0, 0.0, false)
				} else {
					participantID, err = db.AddEntypSeglingParticipantToEvent(eventID, sailorIDs[i], fmt.Sprintf("SWE %d", i+1), 0)
				}
				require.NoError(t, err)
				participantIDs[i] = participantID
			}

			// Create heat
			heatID, err := db.CreateHeat(eventID, 1, "Heat 1", "12:00")
			require.NoError(t, err)

			// Add finish times/placements for finished boats
			for i := 0; i < tc.finishedBoats; i++ {
				if tc.isEntypsegling {
					// For entypsegling, use placements
					err = db.UpdateHeatPlacement(heatID, participantIDs[i], i+1, false, false)
					require.NoError(t, err)
				} else {
					// For regular sailing, use finish times
					finishTime := fmt.Sprintf("12:%02d:00", 10+i)
					err = db.UpdateHeatFinishTime(heatID, participantIDs[i], finishTime, false, false)
					require.NoError(t, err)
				}
			}

			// Add DNF boats
			for i := tc.finishedBoats; i < tc.finishedBoats+tc.dnfBoats; i++ {
				if tc.isEntypsegling {
					err = db.UpdateHeatPlacement(heatID, participantIDs[i], 0, false, true)
				} else {
					err = db.UpdateHeatFinishTime(heatID, participantIDs[i], "", false, true)
				}
				require.NoError(t, err)
			}

			// Add DNS boats
			for i := tc.finishedBoats + tc.dnfBoats; i < tc.totalParticipants; i++ {
				if tc.isEntypsegling {
					err = db.UpdateHeatPlacement(heatID, participantIDs[i], 0, true, false)
				} else {
					err = db.UpdateHeatFinishTime(heatID, participantIDs[i], "", true, false)
				}
				require.NoError(t, err)
			}

			// Get heat results
			var results []models.HeatResult
			if tc.isEntypsegling {
				results, err = db.GetHeatResultsCompleteEntypsegling(heatID)
			} else {
				results, err = db.GetHeatResultsComplete(heatID)
			}
			require.NoError(t, err)

			// Verify results
			var finishedCount, dnfCount, dnsCount int
			var dnfPoints, dnsPoints []float64

			for _, result := range results {
				if result.DNS {
					dnsCount++
					dnsPoints = append(dnsPoints, result.Points)
				} else if result.DNF {
					dnfCount++
					dnfPoints = append(dnfPoints, result.Points)
				} else {
					finishedCount++
				}
			}

			// Verify counts
			assert.Equal(t, tc.finishedBoats, finishedCount, "Finished boats count")
			assert.Equal(t, tc.dnfBoats, dnfCount, "DNF boats count")
			assert.Equal(t, tc.dnsBoats, dnsCount, "DNS boats count")

			// Verify DNF points
			for i, points := range dnfPoints {
				assert.Equal(t, tc.expectedDNFPoints, points,
					"DNF boat %d should have %.1f points", i+1, tc.expectedDNFPoints)
			}

			// Verify DNS points
			for i, points := range dnsPoints {
				assert.Equal(t, tc.expectedDNSPoints, points,
					"DNS boat %d should have %.1f points", i+1, tc.expectedDNSPoints)
			}

			// Verify that all DNF boats get the same points
			for i := 1; i < len(dnfPoints); i++ {
				assert.Equal(t, dnfPoints[0], dnfPoints[i],
					"All DNF boats should receive the same points")
			}

			// Verify that all DNS boats get the same points
			for i := 1; i < len(dnsPoints); i++ {
				assert.Equal(t, dnsPoints[0], dnsPoints[i],
					"All DNS boats should receive the same points")
			}

			// Verify that DNS boats get higher penalty than DNF boats
			if len(dnsPoints) > 0 && len(dnfPoints) > 0 {
				assert.Greater(t, dnsPoints[0], dnfPoints[0],
					"DNS boats should receive higher penalty points than DNF boats")
			}
		})
	}
}
