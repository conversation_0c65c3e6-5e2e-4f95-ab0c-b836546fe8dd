package utils

import (
	"fmt"
	"math"
	"time"
)

// CalculateStartTime calculates the start time offset in seconds for a boat in a jaktstart race
// distance: course length in nautical miles
// wind: wind speed in m/s
// handicap: SRS value of the boat
// lowestSRS: the lowest SRS value among all boats (reference boat)
// Returns the start time offset in seconds relative to the reference boat (lowest SRS)
func CalculateStartTime(distance float64, wind int, handicap float64, lowestSRS float64) int {
	return CalculateStartTimeWithMultiplier(distance, wind, handicap, lowestSRS, 1.0)
}

// CalculateStartTimeWithMultiplier calculates the start time offset in seconds for a boat in a jaktstart race
// with support for half jaktstart (multiplier 0.5) or full jaktstart (multiplier 1.0)
// distance: course length in nautical miles
// wind: wind speed in m/s
// handicap: SRS value of the boat
// lowestSRS: the lowest SRS value among all boats (reference boat)
// multiplier: 1.0 for full jaktstart, 0.5 for half jaktstart, 0.0 for no jaktstart
// Returns the start time offset in seconds relative to the reference boat (lowest SRS)
func CalculateStartTimeWithMultiplier(distance float64, wind int, handicap float64, lowestSRS float64, multiplier float64) int {
	var secondsPerNM float64

	if wind <= 3 {
		secondsPerNM = 915
	} else if wind <= 6 {
		secondsPerNM = 639
	} else {
		secondsPerNM = 548
	}

	refTime := distance * secondsPerNM

	// Calculate time relative to the boat with lowest SRS
	startTime := refTime * (1/lowestSRS - 1/handicap) * multiplier

	// Round to the nearest minute (60 seconds)
	return int(math.Round(startTime/60) * 60)
}

// FormatStartTime formats a start time offset in seconds to a human-readable string
// seconds: start time offset in seconds
// Returns a string like "5 minutes before" or "2 minutes after"
func FormatStartTime(seconds int) string {
	direction := "efter" // after
	if seconds < 0 {
		direction = "före" // before
		seconds = -seconds
	}

	minutes := seconds / 60

	if minutes == 1 {
		return fmt.Sprintf("%d minut %s", minutes, direction)
	} else if minutes > 0 {
		return fmt.Sprintf("%d minuter %s", minutes, direction)
	} else {
		return "0 minuter (starttid)"
	}
}

// CalculateAbsoluteStartTime calculates the absolute start time for a boat
// baseStartTime: the base start time for the reference boat (SRS 1.0)
// offsetSeconds: the start time offset in seconds
// Returns the absolute start time
func CalculateAbsoluteStartTime(baseStartTime time.Time, offsetSeconds int) time.Time {
	return baseStartTime.Add(time.Duration(offsetSeconds) * time.Second)
}

// FormatAbsoluteStartTime formats an absolute start time to a human-readable string
// startTime: the absolute start time
// Returns a string like "13:45"
func FormatAbsoluteStartTime(startTime time.Time) string {
	return startTime.Format("15:04")
}
