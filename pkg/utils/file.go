package utils

import (
    "fmt"
    "io"
    "os"
)

// CopyFile copies a file from src to dst
func CopyFile(src, dst string) error {
    sourceFile, err := os.Open(src)
    if err != nil {
        return fmt.Errorf("could not open source file: %w", err)
    }
    defer sourceFile.Close()

    destFile, err := os.Create(dst)
    if err != nil {
        return fmt.Errorf("could not create destination file: %w", err)
    }
    defer destFile.Close()

    _, err = io.Copy(destFile, sourceFile)
    if err != nil {
        return fmt.Errorf("could not copy file: %w", err)
    }

    err = destFile.Sync()
    if err != nil {
        return fmt.Errorf("could not sync destination file: %w", err)
    }

    return nil
}