package utils

import (
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"

	"github.com/PuerkitoBio/goquery"
)

// URL constants for SRS data
var (
	BoatListURL     = "https://matbrev.svensksegling.se/Home/BoatList"
	ApprovedListURL = "https://matbrev.svensksegling.se/Home/ApprovedList"
)

// SRSData represents SRS data for a boat type
type SRSData struct {
	Battyp                           string  `json:"battyp"`
	MatbrevsNummer                   string  `json:"matbrevs_nummer"`
	BatNamn                          string  `json:"bat_namn"`
	Agare                            string  `json:"agare"`
	Segelnummer                      string  `json:"segelnummer"`
	Nationality                      string  `json:"nationality"`
	SRS                              float64 `json:"srs"`
	SRSUtanUndanvindsegel            float64 `json:"srs_utan_undanvindsegel"`
	SRSShorthanded                   float64 `json:"srs_shorthanded"`
	SRSShorthandedUtanUndanvindsegel float64 `json:"srs_shorthanded_utan_undanvindsegel"`
}

// FetchSRSData fetches SRS data for a specific boat type
func FetchSRSData(battyp string) (SRSData, error) {
	// Create a default SRSData with the boat type
	data := SRSData{
		Battyp: battyp,
	}

	// Make a request to the SRS data website
	resp, err := http.Get(BoatListURL)
	if err != nil {
		return data, fmt.Errorf("error fetching SRS data: %w", err)
	}
	defer resp.Body.Close()

	// Parse the HTML document
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return data, fmt.Errorf("error parsing HTML: %w", err)
	}

	// Parse header row to get column indices
	var columnMap = make(map[string]int)
	doc.Find("tr").First().Find("th, td").Each(func(i int, s *goquery.Selection) {
		header := strings.TrimSpace(s.Text())
		columnMap[header] = i
	})

	// Log the column mapping for debugging
	log.Printf("BoatList column mapping: %+v", columnMap)

	// Find the boat type in the table
	found := false
	doc.Find("tr").Each(func(i int, s *goquery.Selection) {
		// Skip the header row
		if i == 0 {
			return
		}

		// Get the boat type from the first column
		boatType := strings.TrimSpace(s.Find("td").First().Text())

		// If this is the boat type we're looking for
		if strings.EqualFold(boatType, battyp) {
			found = true

			// Get the SRS value using header mapping
			if srsCol, exists := columnMap["SRS"]; exists {
				srsText := strings.TrimSpace(s.Find("td").Eq(srsCol).Text())
				srsText = strings.Replace(srsText, ",", ".", -1) // Replace comma with dot for decimal
				srs, err := strconv.ParseFloat(srsText, 64)
				if err == nil {
					data.SRS = srs
				}
			}

			// Get the SRS utan undanvindsegel value using header mapping
			if srsUUCol, exists := columnMap["SRS utan undanvindsegel"]; exists {
				srsUUText := strings.TrimSpace(s.Find("td").Eq(srsUUCol).Text())
				srsUUText = strings.Replace(srsUUText, ",", ".", -1) // Replace comma with dot for decimal
				srsUU, err := strconv.ParseFloat(srsUUText, 64)
				if err == nil {
					data.SRSUtanUndanvindsegel = srsUU
				}
			}

			// Get the SRS S/H value using header mapping
			if srsSHCol, exists := columnMap["SRS S/H"]; exists {
				srsSHText := strings.TrimSpace(s.Find("td").Eq(srsSHCol).Text())
				log.Printf("SRS S/H text for %s: '%s'", battyp, srsSHText)
				srsSHText = strings.Replace(srsSHText, ",", ".", -1) // Replace comma with dot for decimal
				srsSH, err := strconv.ParseFloat(srsSHText, 64)
				if err == nil {
					data.SRSShorthanded = srsSH
					log.Printf("SRS S/H value for %s: %f", battyp, srsSH)
				} else {
					log.Printf("Error parsing SRS S/H value for %s: %v", battyp, err)
				}
			}

			// Get the SRS S/H utan undanvindsegel value using header mapping
			if srsSHUUCol, exists := columnMap["SRS S/H u. flygande segel"]; exists {
				srsSHUUText := strings.TrimSpace(s.Find("td").Eq(srsSHUUCol).Text())
				log.Printf("SRS S/H utan undanvindsegel text for %s: '%s'", battyp, srsSHUUText)
				srsSHUUText = strings.Replace(srsSHUUText, ",", ".", -1) // Replace comma with dot for decimal
				srsSHUU, err := strconv.ParseFloat(srsSHUUText, 64)
				if err == nil {
					data.SRSShorthandedUtanUndanvindsegel = srsSHUU
					log.Printf("SRS S/H utan undanvindsegel value for %s: %f", battyp, srsSHUU)
				} else {
					log.Printf("Error parsing SRS S/H utan undanvindsegel value for %s: %v", battyp, err)
				}
			}

			// Log the final SRS data
			log.Printf("Final SRS data for %s: %+v", battyp, data)

			// We found what we were looking for, so we can stop
			return
		}
	})

	if !found {
		return data, fmt.Errorf("boat type '%s' not found in SRS data", battyp)
	}

	return data, nil
}

// FetchSRSDataByMatbrev fetches SRS data for a specific mätbrevs nummer
func FetchSRSDataByMatbrev(matbrevsNummer string) (SRSData, error) {
	// Create a default SRSData with the mätbrevs nummer
	data := SRSData{
		MatbrevsNummer: matbrevsNummer,
		Nationality:    "SWE", // Default nationality to SWE
	}

	// Make a request to the SRS data website
	resp, err := http.Get(ApprovedListURL)
	if err != nil {
		return data, fmt.Errorf("error fetching SRS data: %w", err)
	}
	defer resp.Body.Close()

	// Parse the HTML document
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return data, fmt.Errorf("error parsing HTML: %w", err)
	}

	// Parse header row to get column indices - ApprovedList has a special structure
	var columnMap = make(map[string]int)

	// Look for the header row that contains the column links
	headerFound := false
	doc.Find("tr").Each(func(i int, s *goquery.Selection) {
		if headerFound {
			return
		}
		// Check if this row contains header links
		links := s.Find("a")
		if links.Length() > 5 { // Header row should have multiple links
			links.Each(func(j int, link *goquery.Selection) {
				header := strings.TrimSpace(link.Text())
				if header != "" {
					columnMap[header] = j
				}
			})
			headerFound = true
		}
	})

	// If no header links found, try to parse from the table structure
	if len(columnMap) == 0 {
		// Fallback: use hardcoded column positions based on known structure
		columnMap = map[string]int{
			"Mätbrevs-pdf":              0,
			"Ägare":                     1,
			"Båttyp":                    2,
			"Båtnamn":                   3,
			"Nat.":                      4,
			"Segelnr.":                  5,
			"Ver.":                      6,
			"Klassning":                 7,
			"SRS":                       8,
			"SRS u. flygande segel":     9,
			"SRS S/H":                   10,
			"SRS S/H u. flygande segel": 11,
		}
	}

	// Log the column mapping for debugging
	log.Printf("ApprovedList column mapping: %+v", columnMap)

	// Find the mätbrevs nummer in the table
	found := false
	doc.Find("tr").Each(func(i int, s *goquery.Selection) {
		// Skip the header rows
		if i <= 1 {
			return
		}

		// Get the mätbrevs nummer from the first column
		mbNummer := strings.TrimSpace(s.Find("td").First().Text())

		// If this is the mätbrevs nummer we're looking for
		if strings.EqualFold(mbNummer, matbrevsNummer) {
			found = true

			// Get the boat type using header mapping
			if battypCol, exists := columnMap["Båttyp"]; exists {
				data.Battyp = strings.TrimSpace(s.Find("td").Eq(battypCol).Text())
			}

			// Get the boat name using header mapping
			if batnamnCol, exists := columnMap["Båtnamn"]; exists {
				data.BatNamn = strings.TrimSpace(s.Find("td").Eq(batnamnCol).Text())
			}

			// Get the owner using header mapping
			if agareCol, exists := columnMap["Ägare"]; exists {
				data.Agare = strings.TrimSpace(s.Find("td").Eq(agareCol).Text())
			}

			// Get the nationality using header mapping
			if natCol, exists := columnMap["Nat."]; exists {
				nationality := strings.TrimSpace(s.Find("td").Eq(natCol).Text())
				if nationality != "" {
					data.Nationality = nationality
				}
			}

			// Get the sail number using header mapping
			if segelnrCol, exists := columnMap["Segelnr."]; exists {
				data.Segelnummer = strings.TrimSpace(s.Find("td").Eq(segelnrCol).Text())
			}

			// Get the SRS value using header mapping
			if srsCol, exists := columnMap["SRS"]; exists {
				srsText := strings.TrimSpace(s.Find("td").Eq(srsCol).Text())
				srsText = strings.Replace(srsText, ",", ".", -1) // Replace comma with dot for decimal
				srs, err := strconv.ParseFloat(srsText, 64)
				if err == nil {
					data.SRS = srs
				}
			}

			// Get the SRS utan undanvindsegel value using header mapping
			var srsUUCol int
			var srsUUExists bool
			if col, exists := columnMap["SRS u. flygande segel"]; exists {
				srsUUCol, srsUUExists = col, true
			} else if col, exists := columnMap["SRS u.flygande segel"]; exists {
				srsUUCol, srsUUExists = col, true
			} else if col, exists := columnMap["SRS u.\nflygande segel"]; exists {
				srsUUCol, srsUUExists = col, true
			}
			if srsUUExists {
				srsUUText := strings.TrimSpace(s.Find("td").Eq(srsUUCol).Text())
				srsUUText = strings.Replace(srsUUText, ",", ".", -1) // Replace comma with dot for decimal
				srsUU, err := strconv.ParseFloat(srsUUText, 64)
				if err == nil {
					data.SRSUtanUndanvindsegel = srsUU
				}
			}

			// Get the SRS S/H value using header mapping
			if srsSHCol, exists := columnMap["SRS S/H"]; exists {
				srsSHText := strings.TrimSpace(s.Find("td").Eq(srsSHCol).Text())
				srsSHText = strings.Replace(srsSHText, ",", ".", -1) // Replace comma with dot for decimal
				srsSH, err := strconv.ParseFloat(srsSHText, 64)
				if err == nil {
					data.SRSShorthanded = srsSH
				}
			}

			// Get the SRS S/H utan undanvindsegel value using header mapping
			if srsSHUUCol, exists := columnMap["SRS S/H u. flygande segel"]; exists {
				srsSHUUText := strings.TrimSpace(s.Find("td").Eq(srsSHUUCol).Text())
				srsSHUUText = strings.Replace(srsSHUUText, ",", ".", -1) // Replace comma with dot for decimal
				srsSHUU, err := strconv.ParseFloat(srsSHUUText, 64)
				if err == nil {
					data.SRSShorthandedUtanUndanvindsegel = srsSHUU
				}
			}

			// We found what we were looking for, so we can stop
			return
		}
	})

	if !found {
		return data, fmt.Errorf("mätbrevs nummer '%s' not found in SRS data", matbrevsNummer)
	}

	return data, nil
}

// FetchAllBoatTypes fetches all available boat types from the SRS data website
func FetchAllBoatTypes() ([]string, error) {
	var boatTypes []string

	// Make a request to the SRS data website
	resp, err := http.Get(BoatListURL)
	if err != nil {
		return nil, fmt.Errorf("error fetching SRS data: %w", err)
	}
	defer resp.Body.Close()

	// Parse the HTML document
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error parsing HTML: %w", err)
	}

	// Parse header row to get column indices
	var columnMap = make(map[string]int)
	doc.Find("tr").First().Find("th, td").Each(func(i int, s *goquery.Selection) {
		header := strings.TrimSpace(s.Text())
		columnMap[header] = i
	})

	// Find all boat types in the table
	doc.Find("tr").Each(func(i int, s *goquery.Selection) {
		// Skip the header row
		if i == 0 {
			return
		}

		// Get the boat type from the Båttyp column
		var boatType string
		if battypCol, exists := columnMap["Båttyp"]; exists {
			boatType = strings.TrimSpace(s.Find("td").Eq(battypCol).Text())
		} else {
			// Fallback to first column if header mapping fails
			boatType = strings.TrimSpace(s.Find("td").First().Text())
		}

		if boatType != "" {
			boatTypes = append(boatTypes, boatType)
		}
	})

	if len(boatTypes) == 0 {
		return nil, fmt.Errorf("no boat types found in SRS data")
	}

	log.Printf("Found %d boat types in SRS data", len(boatTypes))
	return boatTypes, nil
}

// SearchBoatsByOwner searches for boats by owner in the SRS data
func SearchBoatsByOwner(owner string) ([]SRSData, error) {
	var results []SRSData

	// Make a request to the SRS data website
	resp, err := http.Get(ApprovedListURL)
	if err != nil {
		return nil, fmt.Errorf("error fetching SRS data: %w", err)
	}
	defer resp.Body.Close()

	// Parse the HTML document
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error parsing HTML: %w", err)
	}

	// Parse header row to get column indices - ApprovedList has a special structure
	var columnMap = make(map[string]int)

	// Look for the header row that contains the column links
	headerFound := false
	doc.Find("tr").Each(func(i int, s *goquery.Selection) {
		if headerFound {
			return
		}
		// Check if this row contains header links
		links := s.Find("a")
		if links.Length() > 5 { // Header row should have multiple links
			links.Each(func(j int, link *goquery.Selection) {
				header := strings.TrimSpace(link.Text())
				if header != "" {
					columnMap[header] = j
				}
			})
			headerFound = true
		}
	})

	// If no header links found, try to parse from the table structure
	if len(columnMap) == 0 {
		// Fallback: use hardcoded column positions based on known structure
		columnMap = map[string]int{
			"Mätbrevs-pdf":              0,
			"Ägare":                     1,
			"Båttyp":                    2,
			"Båtnamn":                   3,
			"Nat.":                      4,
			"Segelnr.":                  5,
			"Ver.":                      6,
			"Klassning":                 7,
			"SRS":                       8,
			"SRS u. flygande segel":     9,
			"SRS S/H":                   10,
			"SRS S/H u. flygande segel": 11,
		}
	}

	// Search for boats by owner
	doc.Find("tr").Each(func(i int, s *goquery.Selection) {
		// Skip the header rows
		if i <= 1 {
			return
		}

		// Get the owner using header mapping
		var boatOwner string
		if agareCol, exists := columnMap["Ägare"]; exists {
			boatOwner = strings.TrimSpace(s.Find("td").Eq(agareCol).Text())
		} else {
			// Fallback to second column if header mapping fails
			boatOwner = strings.TrimSpace(s.Find("td").Eq(1).Text())
		}

		// If the owner contains our search term
		if strings.Contains(strings.ToLower(boatOwner), strings.ToLower(owner)) {
			data := SRSData{
				Nationality: "SWE", // Default nationality to SWE
			}

			// Get the mätbrevs nummer (1st column)
			data.MatbrevsNummer = strings.TrimSpace(s.Find("td").First().Text())

			// Get the boat type using header mapping
			if battypCol, exists := columnMap["Båttyp"]; exists {
				data.Battyp = strings.TrimSpace(s.Find("td").Eq(battypCol).Text())
			}

			// Get the boat name using header mapping
			if batnamnCol, exists := columnMap["Båtnamn"]; exists {
				data.BatNamn = strings.TrimSpace(s.Find("td").Eq(batnamnCol).Text())
			}

			// Get the owner
			data.Agare = boatOwner

			// Get the nationality using header mapping
			if natCol, exists := columnMap["Nat."]; exists {
				nationality := strings.TrimSpace(s.Find("td").Eq(natCol).Text())
				if nationality != "" {
					data.Nationality = nationality
				}
			}

			// Get the sail number using header mapping
			if segelnrCol, exists := columnMap["Segelnr."]; exists {
				data.Segelnummer = strings.TrimSpace(s.Find("td").Eq(segelnrCol).Text())
			}

			// Get the SRS value using header mapping
			if srsCol, exists := columnMap["SRS"]; exists {
				srsText := strings.TrimSpace(s.Find("td").Eq(srsCol).Text())
				srsText = strings.Replace(srsText, ",", ".", -1) // Replace comma with dot for decimal
				srs, err := strconv.ParseFloat(srsText, 64)
				if err == nil {
					data.SRS = srs
				}
			}

			// Get the SRS utan undanvindsegel value using header mapping
			var srsUUCol int
			var srsUUExists bool
			if col, exists := columnMap["SRS u. flygande segel"]; exists {
				srsUUCol, srsUUExists = col, true
			} else if col, exists := columnMap["SRS u.flygande segel"]; exists {
				srsUUCol, srsUUExists = col, true
			} else if col, exists := columnMap["SRS u.\nflygande segel"]; exists {
				srsUUCol, srsUUExists = col, true
			}
			if srsUUExists {
				srsUUText := strings.TrimSpace(s.Find("td").Eq(srsUUCol).Text())
				srsUUText = strings.Replace(srsUUText, ",", ".", -1) // Replace comma with dot for decimal
				srsUU, err := strconv.ParseFloat(srsUUText, 64)
				if err == nil {
					data.SRSUtanUndanvindsegel = srsUU
				}
			}

			// Get the SRS S/H value using header mapping
			if srsSHCol, exists := columnMap["SRS S/H"]; exists {
				srsSHText := strings.TrimSpace(s.Find("td").Eq(srsSHCol).Text())
				srsSHText = strings.Replace(srsSHText, ",", ".", -1) // Replace comma with dot for decimal
				srsSH, err := strconv.ParseFloat(srsSHText, 64)
				if err == nil {
					data.SRSShorthanded = srsSH
				}
			}

			// Get the SRS S/H utan undanvindsegel value using header mapping
			if srsSHUUCol, exists := columnMap["SRS S/H u. flygande segel"]; exists {
				srsSHUUText := strings.TrimSpace(s.Find("td").Eq(srsSHUUCol).Text())
				srsSHUUText = strings.Replace(srsSHUUText, ",", ".", -1) // Replace comma with dot for decimal
				srsSHUU, err := strconv.ParseFloat(srsSHUUText, 64)
				if err == nil {
					data.SRSShorthandedUtanUndanvindsegel = srsSHUU
				}
			}

			results = append(results, data)
		}
	})

	if len(results) == 0 {
		return nil, fmt.Errorf("no boats found with owner matching '%s'", owner)
	}

	return results, nil
}

// FetchAllSRSData fetches all boat types from the BoatList page
func FetchAllSRSData() ([]SRSData, error) {
	var results []SRSData

	// Make a request to the SRS data website
	resp, err := http.Get(BoatListURL)
	if err != nil {
		return nil, fmt.Errorf("error fetching SRS data: %w", err)
	}
	defer resp.Body.Close()

	// Parse the HTML document
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error parsing HTML: %w", err)
	}

	// Parse header row to get column indices
	var columnMap = make(map[string]int)
	doc.Find("tr").First().Find("th, td").Each(func(i int, s *goquery.Selection) {
		header := strings.TrimSpace(s.Text())
		columnMap[header] = i
	})

	// Process each row in the table
	doc.Find("tr").Each(func(i int, s *goquery.Selection) {
		// Skip the header row
		if i == 0 {
			return
		}

		data := SRSData{}

		// Get the boat type using header mapping
		var boatType string
		if battypCol, exists := columnMap["Båttyp"]; exists {
			boatType = strings.TrimSpace(s.Find("td").Eq(battypCol).Text())
		} else {
			// Fallback to first column if header mapping fails
			boatType = strings.TrimSpace(s.Find("td").First().Text())
		}

		data.Battyp = boatType
		if data.Battyp == "" {
			return
		}

		// Get the SRS value using header mapping
		if srsCol, exists := columnMap["SRS"]; exists {
			srsText := strings.TrimSpace(s.Find("td").Eq(srsCol).Text())
			srsText = strings.Replace(srsText, ",", ".", -1) // Replace comma with dot for decimal
			srs, err := strconv.ParseFloat(srsText, 64)
			if err == nil {
				data.SRS = srs
			}
		}

		// Get the SRS utan undanvindsegel value using header mapping
		if srsUUCol, exists := columnMap["SRS utan undanvindsegel"]; exists {
			srsUUText := strings.TrimSpace(s.Find("td").Eq(srsUUCol).Text())
			srsUUText = strings.Replace(srsUUText, ",", ".", -1) // Replace comma with dot for decimal
			srsUU, err := strconv.ParseFloat(srsUUText, 64)
			if err == nil {
				data.SRSUtanUndanvindsegel = srsUU
			}
		}

		// Get the SRS S/H value using header mapping
		if srsSHCol, exists := columnMap["SRS S/H"]; exists {
			srsSHText := strings.TrimSpace(s.Find("td").Eq(srsSHCol).Text())
			srsSHText = strings.Replace(srsSHText, ",", ".", -1) // Replace comma with dot for decimal
			srsSH, err := strconv.ParseFloat(srsSHText, 64)
			if err == nil {
				data.SRSShorthanded = srsSH
			}
		}

		// Get the SRS S/H utan undanvindsegel value using header mapping
		if srsSHUUCol, exists := columnMap["SRS S/H u. flygande segel"]; exists {
			srsSHUUText := strings.TrimSpace(s.Find("td").Eq(srsSHUUCol).Text())
			srsSHUUText = strings.Replace(srsSHUUText, ",", ".", -1) // Replace comma with dot for decimal
			srsSHUU, err := strconv.ParseFloat(srsSHUUText, 64)
			if err == nil {
				data.SRSShorthandedUtanUndanvindsegel = srsSHUU
			}
		}

		results = append(results, data)
	})

	if len(results) == 0 {
		return nil, fmt.Errorf("no boat types found in SRS data")
	}

	log.Printf("Fetched %d boat types from SRS data", len(results))
	return results, nil
}

// FetchAllApprovedBoats fetches all approved boats from the ApprovedList page
func FetchAllApprovedBoats() ([]SRSData, error) {
	var results []SRSData

	// Make a request to the SRS data website
	resp, err := http.Get(ApprovedListURL)
	if err != nil {
		return nil, fmt.Errorf("error fetching SRS data: %w", err)
	}
	defer resp.Body.Close()

	// Parse the HTML document
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error parsing HTML: %w", err)
	}

	// Parse header row to get column indices - ApprovedList has a special structure
	var columnMap = make(map[string]int)

	// Look for the header row that contains the column links
	headerFound := false
	doc.Find("tr").Each(func(i int, s *goquery.Selection) {
		if headerFound {
			return
		}
		// Check if this row contains header links
		links := s.Find("a")
		if links.Length() > 5 { // Header row should have multiple links
			links.Each(func(j int, link *goquery.Selection) {
				header := strings.TrimSpace(link.Text())
				if header != "" {
					columnMap[header] = j
				}
			})
			headerFound = true
		}
	})

	// If no header links found, try to parse from the table structure
	if len(columnMap) == 0 {
		// Fallback: use hardcoded column positions based on known structure
		columnMap = map[string]int{
			"Mätbrevs-pdf":              0,
			"Ägare":                     1,
			"Båttyp":                    2,
			"Båtnamn":                   3,
			"Nat.":                      4,
			"Segelnr.":                  5,
			"Ver.":                      6,
			"Klassning":                 7,
			"SRS":                       8,
			"SRS u. flygande segel":     9,
			"SRS S/H":                   10,
			"SRS S/H u. flygande segel": 11,
		}
	}

	// Process each row in the table
	doc.Find("tr").Each(func(i int, s *goquery.Selection) {
		// Skip the header rows
		if i <= 1 {
			return
		}

		data := SRSData{
			Nationality: "SWE", // Default nationality to SWE
		}

		// Get the mätbrevs nummer (1st column)
		data.MatbrevsNummer = strings.TrimSpace(s.Find("td").First().Text())
		if data.MatbrevsNummer == "" {
			return
		}

		// Get the boat type using header mapping
		if battypCol, exists := columnMap["Båttyp"]; exists {
			data.Battyp = strings.TrimSpace(s.Find("td").Eq(battypCol).Text())
		}

		// Get the boat name using header mapping
		if batnamnCol, exists := columnMap["Båtnamn"]; exists {
			data.BatNamn = strings.TrimSpace(s.Find("td").Eq(batnamnCol).Text())
		}

		// Get the owner using header mapping
		if agareCol, exists := columnMap["Ägare"]; exists {
			data.Agare = strings.TrimSpace(s.Find("td").Eq(agareCol).Text())
		}

		// Get the nationality using header mapping
		if natCol, exists := columnMap["Nat."]; exists {
			nationality := strings.TrimSpace(s.Find("td").Eq(natCol).Text())
			if nationality != "" {
				data.Nationality = nationality
			}
		}

		// Get the sail number using header mapping
		if segelnrCol, exists := columnMap["Segelnr."]; exists {
			data.Segelnummer = strings.TrimSpace(s.Find("td").Eq(segelnrCol).Text())
		}

		// Get the SRS value using header mapping
		if srsCol, exists := columnMap["SRS"]; exists {
			srsText := strings.TrimSpace(s.Find("td").Eq(srsCol).Text())
			srsText = strings.Replace(srsText, ",", ".", -1) // Replace comma with dot for decimal
			srs, err := strconv.ParseFloat(srsText, 64)
			if err == nil {
				data.SRS = srs
			}
		}

		// Get the SRS utan undanvindsegel value using header mapping
		var srsUUCol int
		var srsUUExists bool
		if col, exists := columnMap["SRS u. flygande segel"]; exists {
			srsUUCol, srsUUExists = col, true
		} else if col, exists := columnMap["SRS u.flygande segel"]; exists {
			srsUUCol, srsUUExists = col, true
		} else if col, exists := columnMap["SRS u.\nflygande segel"]; exists {
			srsUUCol, srsUUExists = col, true
		}
		if srsUUExists {
			srsUUText := strings.TrimSpace(s.Find("td").Eq(srsUUCol).Text())
			srsUUText = strings.Replace(srsUUText, ",", ".", -1) // Replace comma with dot for decimal
			srsUU, err := strconv.ParseFloat(srsUUText, 64)
			if err == nil {
				data.SRSUtanUndanvindsegel = srsUU
			}
		}

		// Get the SRS S/H value using header mapping
		if srsSHCol, exists := columnMap["SRS S/H"]; exists {
			srsSHText := strings.TrimSpace(s.Find("td").Eq(srsSHCol).Text())
			srsSHText = strings.Replace(srsSHText, ",", ".", -1) // Replace comma with dot for decimal
			srsSH, err := strconv.ParseFloat(srsSHText, 64)
			if err == nil {
				data.SRSShorthanded = srsSH
			}
		}

		// Get the SRS S/H utan undanvindsegel value using header mapping
		if srsSHUUCol, exists := columnMap["SRS S/H u. flygande segel"]; exists {
			srsSHUUText := strings.TrimSpace(s.Find("td").Eq(srsSHUUCol).Text())
			srsSHUUText = strings.Replace(srsSHUUText, ",", ".", -1) // Replace comma with dot for decimal
			srsSHUU, err := strconv.ParseFloat(srsSHUUText, 64)
			if err == nil {
				data.SRSShorthandedUtanUndanvindsegel = srsSHUU
			}
		}

		results = append(results, data)
	})

	if len(results) == 0 {
		return nil, fmt.Errorf("no approved boats found in SRS data")
	}

	log.Printf("Fetched %d approved boats from SRS data", len(results))
	return results, nil
}
