package utils

import (
	"testing"
)

// TestCalculateStartTimeWithMultiplier tests the jaktstart calculation with different multipliers
func TestCalculateStartTimeWithMultiplier(t *testing.T) {
	tests := []struct {
		name        string
		distance    float64
		wind        int
		handicap    float64
		lowestSRS   float64
		multiplier  float64
		expected    int
		description string
	}{
		{
			name:        "Regular jaktstart (multiplier 1.0)",
			distance:    10.0,
			wind:        5,
			handicap:    1.1,
			lowestSRS:   0.9,
			multiplier:  1.0,
			expected:    1320, // 22 minutes later
			description: "Full jaktstart - boats meet at finish line",
		},
		{
			name:        "Half jaktstart (multiplier 0.5)",
			distance:    10.0,
			wind:        5,
			handicap:    1.1,
			lowestSRS:   0.9,
			multiplier:  0.5,
			expected:    660, // 11 minutes later (half of regular)
			description: "Half jaktstart - boats meet at half course distance",
		},
		{
			name:        "No jaktstart (multiplier 0.0)",
			distance:    10.0,
			wind:        5,
			handicap:    1.1,
			lowestSRS:   0.9,
			multiplier:  0.0,
			expected:    0, // No offset
			description: "No jaktstart - all boats start together",
		},
		{
			name:        "Half jaktstart with different boats",
			distance:    15.0,
			wind:        4,
			handicap:    1.2,
			lowestSRS:   0.85,
			multiplier:  0.5,
			expected:    1620, // Half of what would be full jaktstart (3300/2 = 1650, rounded to 1620)
			description: "Half jaktstart with longer course and different SRS values",
		},
		{
			name:        "Half jaktstart - reference boat",
			distance:    10.0,
			wind:        5,
			handicap:    0.9, // Same as lowestSRS
			lowestSRS:   0.9,
			multiplier:  0.5,
			expected:    0, // Reference boat always starts at 0 offset
			description: "Reference boat (lowest SRS) always has 0 offset regardless of multiplier",
		},
		{
			name:        "Half jaktstart - faster boat",
			distance:    10.0,
			wind:        5,
			handicap:    0.8, // Faster than reference
			lowestSRS:   0.9,
			multiplier:  0.5,
			expected:    -420, // Negative offset (starts before reference)
			description: "Faster boat starts before reference boat in half jaktstart",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateStartTimeWithMultiplier(tt.distance, tt.wind, tt.handicap, tt.lowestSRS, tt.multiplier)
			if result != tt.expected {
				t.Errorf("CalculateStartTimeWithMultiplier(%v, %v, %v, %v, %v) = %v, expected %v\nDescription: %s",
					tt.distance, tt.wind, tt.handicap, tt.lowestSRS, tt.multiplier, result, tt.expected, tt.description)
			}
		})
	}
}

// TestHalfJaktstartConsistency tests that half jaktstart produces exactly half the offset of regular jaktstart
func TestHalfJaktstartConsistency(t *testing.T) {
	testCases := []struct {
		distance  float64
		wind      int
		handicap  float64
		lowestSRS float64
	}{
		{10.0, 5, 1.1, 0.9},
		{15.0, 4, 1.2, 0.85},
		{8.0, 6, 1.05, 0.95},
		{20.0, 3, 1.3, 0.8},
	}

	for _, tc := range testCases {
		t.Run("Consistency check", func(t *testing.T) {
			// Calculate regular jaktstart offset
			regularOffset := CalculateStartTimeWithMultiplier(tc.distance, tc.wind, tc.handicap, tc.lowestSRS, 1.0)

			// Calculate half jaktstart offset
			halfOffset := CalculateStartTimeWithMultiplier(tc.distance, tc.wind, tc.handicap, tc.lowestSRS, 0.5)

			// Half jaktstart should be exactly half of regular jaktstart
			expectedHalfOffset := regularOffset / 2

			// Allow for rounding differences (within 60 seconds due to minute rounding)
			diff := halfOffset - expectedHalfOffset
			if diff < -60 || diff > 60 {
				t.Errorf("Half jaktstart offset (%d) is not approximately half of regular offset (%d). Expected around %d, difference: %d",
					halfOffset, regularOffset, expectedHalfOffset, diff)
			}

			t.Logf("Distance: %.1f nm, Wind: %d m/s, Handicap: %.3f, LowestSRS: %.3f",
				tc.distance, tc.wind, tc.handicap, tc.lowestSRS)
			t.Logf("Regular jaktstart offset: %d seconds (%s)", regularOffset, FormatStartTime(regularOffset))
			t.Logf("Half jaktstart offset: %d seconds (%s)", halfOffset, FormatStartTime(halfOffset))
		})
	}
}

// TestBackwardCompatibility tests that the original CalculateStartTime function still works
func TestBackwardCompatibility(t *testing.T) {
	distance := 10.0
	wind := 5
	handicap := 1.1
	lowestSRS := 0.9

	// Original function should give same result as new function with multiplier 1.0
	originalResult := CalculateStartTime(distance, wind, handicap, lowestSRS)
	newResult := CalculateStartTimeWithMultiplier(distance, wind, handicap, lowestSRS, 1.0)

	if originalResult != newResult {
		t.Errorf("Backward compatibility broken: CalculateStartTime() = %d, CalculateStartTimeWithMultiplier(..., 1.0) = %d",
			originalResult, newResult)
	}
}

// TestMultiplierEdgeCases tests edge cases for the multiplier
func TestMultiplierEdgeCases(t *testing.T) {
	distance := 10.0
	wind := 5
	handicap := 1.1
	lowestSRS := 0.9

	tests := []struct {
		name       string
		multiplier float64
		expected   int
	}{
		{"Zero multiplier", 0.0, 0},
		{"Quarter jaktstart", 0.25, 330},       // Quarter of 1320
		{"Three-quarter jaktstart", 0.75, 960}, // Three quarters of 1320
		{"Double jaktstart", 2.0, 2640},        // Double of 1320
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateStartTimeWithMultiplier(distance, wind, handicap, lowestSRS, tt.multiplier)

			// Allow for rounding differences (within 60 seconds due to minute rounding)
			diff := result - tt.expected
			if diff < -60 || diff > 60 {
				t.Errorf("CalculateStartTimeWithMultiplier(..., %v) = %d, expected around %d (difference: %d)",
					tt.multiplier, result, tt.expected, diff)
			}
		})
	}
}
