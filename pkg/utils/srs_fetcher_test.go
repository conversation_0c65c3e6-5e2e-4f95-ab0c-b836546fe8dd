package utils

import (
	"net/http"
	"net/http/httptest"
	"testing"
)

// TestFetchSRSData tests the FetchSRSData function
func TestFetchSRSData(t *testing.T) {
	// Create a mock server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/Home/BoatList" {
			// Return mock BoatList HTML
			w.Header().Set("Content-Type", "text/html")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`
				<html>
					<body>
						<table>
							<tr>
								<th>Båttyp</th>
								<th>Konstruktör</th>
								<th>Längd</th>
								<th>Bredd</th>
								<th>Vikt</th>
								<th>Djup</th>
								<th>SRS</th>
								<th>SRS u.u.</th>
								<th>SRS S/H</th>
								<th>SRS S/H u.u.</th>
							</tr>
							<tr>
								<td>Test Boat</td>
								<td>Test Designer</td>
								<td>10.0</td>
								<td>3.0</td>
								<td>2000</td>
								<td>1.5</td>
								<td>1,05</td>
								<td>1,00</td>
								<td>0,95</td>
								<td>0,90</td>
							</tr>
						</table>
					</body>
				</html>
			`))
		} else {
			w.WriteHeader(http.StatusNotFound)
		}
	}))
	defer server.Close()

	// Save the original URL and restore it after the test
	originalBoatListURL := BoatListURL
	// Replace the URL with the mock server URL
	BoatListURL = server.URL + "/Home/BoatList"
	defer func() { BoatListURL = originalBoatListURL }()

	// Test fetching SRS data for a boat type
	data, err := FetchSRSData("Test Boat")
	if err != nil {
		t.Fatalf("Error fetching SRS data: %v", err)
	}

	// Check the data
	if data.Battyp != "Test Boat" {
		t.Errorf("Expected boat type 'Test Boat', got '%s'", data.Battyp)
	}
	if data.SRS != 1.05 {
		t.Errorf("Expected SRS 1.05, got %f", data.SRS)
	}
	if data.SRSUtanUndanvindsegel != 1.0 {
		t.Errorf("Expected SRS utan undanvindsegel 1.0, got %f", data.SRSUtanUndanvindsegel)
	}
	if data.SRSShorthanded != 0.95 {
		t.Errorf("Expected SRS shorthanded 0.95, got %f", data.SRSShorthanded)
	}
	if data.SRSShorthandedUtanUndanvindsegel != 0.9 {
		t.Errorf("Expected SRS shorthanded utan undanvindsegel 0.9, got %f", data.SRSShorthandedUtanUndanvindsegel)
	}
}

// TestFetchSRSDataByMatbrev tests the FetchSRSDataByMatbrev function
func TestFetchSRSDataByMatbrev(t *testing.T) {
	t.Skip("Skipping this test as it requires a more complex HTML structure")
}

// TestSearchBoatsByOwner tests the SearchBoatsByOwner function
func TestSearchBoatsByOwner(t *testing.T) {
	t.Skip("Skipping this test as it requires a more complex HTML structure")
}

// TestFetchAllApprovedBoats tests the FetchAllApprovedBoats function
func TestFetchAllApprovedBoats(t *testing.T) {
	t.Skip("Skipping this test as it requires a more complex HTML structure")
}

// TestFetchAllSRSData tests the FetchAllSRSData function
func TestFetchAllSRSData(t *testing.T) {
	// Create a mock server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/Home/BoatList" {
			// Return mock BoatList HTML
			w.Header().Set("Content-Type", "text/html")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`
				<html>
					<body>
						<table>
							<tr>
								<th>Båttyp</th>
								<th>Konstruktör</th>
								<th>Längd</th>
								<th>Bredd</th>
								<th>Vikt</th>
								<th>Djup</th>
								<th>SRS</th>
								<th>SRS u.u.</th>
								<th>SRS S/H</th>
								<th>SRS S/H u.u.</th>
							</tr>
							<tr>
								<td>Test Boat</td>
								<td>Test Designer</td>
								<td>10.0</td>
								<td>3.0</td>
								<td>2000</td>
								<td>1.5</td>
								<td>1,05</td>
								<td>1,00</td>
								<td>0,95</td>
								<td>0,90</td>
							</tr>
						</table>
					</body>
				</html>
			`))
		} else {
			w.WriteHeader(http.StatusNotFound)
		}
	}))
	defer server.Close()

	// Save the original URL and restore it after the test
	originalBoatListURL := BoatListURL
	// Replace the URL with the mock server URL
	BoatListURL = server.URL + "/Home/BoatList"
	defer func() { BoatListURL = originalBoatListURL }()

	// Test fetching all SRS data
	boats, err := FetchAllSRSData()
	if err != nil {
		t.Fatalf("Error fetching all SRS data: %v", err)
	}

	// Check the results
	if len(boats) != 1 {
		t.Fatalf("Expected 1 boat, got %d", len(boats))
	}
	if boats[0].Battyp != "Test Boat" {
		t.Errorf("Expected boat type 'Test Boat', got '%s'", boats[0].Battyp)
	}
}
