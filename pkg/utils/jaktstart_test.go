package utils

import (
	"testing"
	"time"
)

// TestCalculateStartTime tests the core jaktstart calculation logic
func TestCalculateStartTime(t *testing.T) {
	tests := []struct {
		name        string
		distance    float64
		wind        int
		handicap    float64
		lowestSRS   float64
		expected    int
		description string
	}{
		{
			name:        "Reference boat (lowest SRS) should start at 0",
			distance:    10.0,
			wind:        5,
			handicap:    0.95,
			lowestSRS:   0.95,
			expected:    0,
			description: "Boat with lowest SRS should have no offset",
		},
		{
			name:        "Faster boat (higher SRS) should start later",
			distance:    10.0,
			wind:        5,
			handicap:    1.1,
			lowestSRS:   0.95,
			expected:    900, // Should be positive (starts after)
			description: "Faster boats (higher SRS) start later in jaktstart",
		},
		{
			name:        "Slowest boat (lowest SRS) should start at reference time",
			distance:    10.0,
			wind:        5,
			handicap:    0.85,
			lowestSRS:   0.85,
			expected:    0, // Slowest boat always starts at 0 offset
			description: "Slowest boat (lowest SRS) starts at reference time (0 offset)",
		},
		{
			name:        "Light wind conditions (≤3 m/s)",
			distance:    5.0,
			wind:        3,
			handicap:    1.0,
			lowestSRS:   0.9,
			expected:    480, // Rounded to nearest minute
			description: "Light wind uses 915 seconds per nautical mile",
		},
		{
			name:        "Medium wind conditions (4-6 m/s)",
			distance:    5.0,
			wind:        5,
			handicap:    1.0,
			lowestSRS:   0.9,
			expected:    360, // Rounded to nearest minute
			description: "Medium wind uses 639 seconds per nautical mile",
		},
		{
			name:        "Strong wind conditions (>6 m/s)",
			distance:    5.0,
			wind:        8,
			handicap:    1.0,
			lowestSRS:   0.9,
			expected:    300, // Rounded to nearest minute
			description: "Strong wind uses 548 seconds per nautical mile",
		},
		{
			name:        "Edge case: identical SRS values",
			distance:    10.0,
			wind:        5,
			handicap:    1.0,
			lowestSRS:   1.0,
			expected:    0,
			description: "Identical SRS should result in same start time",
		},
		{
			name:        "Short distance race",
			distance:    2.0,
			wind:        5,
			handicap:    1.1,
			lowestSRS:   0.9,
			expected:    240, // Rounded to nearest minute
			description: "Short races should still calculate proportional offsets",
		},
		{
			name:        "Long distance race",
			distance:    25.0,
			wind:        5,
			handicap:    1.1,
			lowestSRS:   0.9,
			expected:    3240, // 54 minutes later
			description: "Long races amplify handicap differences",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateStartTime(tt.distance, tt.wind, tt.handicap, tt.lowestSRS)
			if result != tt.expected {
				t.Errorf("CalculateStartTime(%v, %v, %v, %v) = %v, expected %v\nDescription: %s",
					tt.distance, tt.wind, tt.handicap, tt.lowestSRS, result, tt.expected, tt.description)
			}
		})
	}
}

// TestCalculateStartTimeWindBoundaries tests wind speed boundary conditions
func TestCalculateStartTimeWindBoundaries(t *testing.T) {
	distance := 10.0
	handicap := 1.0
	lowestSRS := 0.9 // Slowest boat starts at 0 offset

	tests := []struct {
		wind     int
		expected int
		category string
	}{
		{wind: 0, expected: 1020, category: "calm (≤3 m/s)"},
		{wind: 1, expected: 1020, category: "light (≤3 m/s)"},
		{wind: 3, expected: 1020, category: "light boundary (≤3 m/s)"},
		{wind: 4, expected: 720, category: "medium (4-6 m/s)"},
		{wind: 5, expected: 720, category: "medium (4-6 m/s)"},
		{wind: 6, expected: 720, category: "medium boundary (4-6 m/s)"},
		{wind: 7, expected: 600, category: "strong (>6 m/s)"},
		{wind: 10, expected: 600, category: "strong (>6 m/s)"},
		{wind: 15, expected: 600, category: "very strong (>6 m/s)"},
	}

	for _, tt := range tests {
		t.Run(tt.category, func(t *testing.T) {
			result := CalculateStartTime(distance, tt.wind, handicap, lowestSRS)
			if result != tt.expected {
				t.Errorf("Wind %d m/s (%s): got %d seconds, expected %d seconds",
					tt.wind, tt.category, result, tt.expected)
			}
		})
	}
}

// TestFormatStartTime tests the start time formatting function
func TestFormatStartTime(t *testing.T) {
	tests := []struct {
		name     string
		seconds  int
		expected string
	}{
		{
			name:     "Zero offset (reference boat)",
			seconds:  0,
			expected: "0 minuter (starttid)",
		},
		{
			name:     "One minute before",
			seconds:  -60,
			expected: "1 minut före",
		},
		{
			name:     "Multiple minutes before",
			seconds:  -300,
			expected: "5 minuter före",
		},
		{
			name:     "One minute after",
			seconds:  60,
			expected: "1 minut efter",
		},
		{
			name:     "Multiple minutes after",
			seconds:  480,
			expected: "8 minuter efter",
		},
		{
			name:     "Large negative offset",
			seconds:  -1800,
			expected: "30 minuter före",
		},
		{
			name:     "Large positive offset",
			seconds:  1200,
			expected: "20 minuter efter",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FormatStartTime(tt.seconds)
			if result != tt.expected {
				t.Errorf("FormatStartTime(%d) = %q, expected %q", tt.seconds, result, tt.expected)
			}
		})
	}
}

// TestCalculateAbsoluteStartTime tests absolute start time calculation
func TestCalculateAbsoluteStartTime(t *testing.T) {
	baseTime := time.Date(2025, 1, 6, 12, 0, 0, 0, time.UTC)

	tests := []struct {
		name          string
		offsetSeconds int
		expected      time.Time
	}{
		{
			name:          "No offset (reference boat)",
			offsetSeconds: 0,
			expected:      time.Date(2025, 1, 6, 12, 0, 0, 0, time.UTC),
		},
		{
			name:          "Start 5 minutes earlier",
			offsetSeconds: -300,
			expected:      time.Date(2025, 1, 6, 11, 55, 0, 0, time.UTC),
		},
		{
			name:          "Start 10 minutes later",
			offsetSeconds: 600,
			expected:      time.Date(2025, 1, 6, 12, 10, 0, 0, time.UTC),
		},
		{
			name:          "Start 1 hour earlier",
			offsetSeconds: -3600,
			expected:      time.Date(2025, 1, 6, 11, 0, 0, 0, time.UTC),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateAbsoluteStartTime(baseTime, tt.offsetSeconds)
			if !result.Equal(tt.expected) {
				t.Errorf("CalculateAbsoluteStartTime(%v, %d) = %v, expected %v",
					baseTime, tt.offsetSeconds, result, tt.expected)
			}
		})
	}
}

// TestFormatAbsoluteStartTime tests absolute start time formatting
func TestFormatAbsoluteStartTime(t *testing.T) {
	tests := []struct {
		name      string
		startTime time.Time
		expected  string
	}{
		{
			name:      "Morning start time",
			startTime: time.Date(2025, 1, 6, 9, 30, 0, 0, time.UTC),
			expected:  "09:30",
		},
		{
			name:      "Noon start time",
			startTime: time.Date(2025, 1, 6, 12, 0, 0, 0, time.UTC),
			expected:  "12:00",
		},
		{
			name:      "Afternoon start time",
			startTime: time.Date(2025, 1, 6, 15, 45, 0, 0, time.UTC),
			expected:  "15:45",
		},
		{
			name:      "Evening start time",
			startTime: time.Date(2025, 1, 6, 18, 15, 30, 0, time.UTC),
			expected:  "18:15",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FormatAbsoluteStartTime(tt.startTime)
			if result != tt.expected {
				t.Errorf("FormatAbsoluteStartTime(%v) = %q, expected %q",
					tt.startTime, result, tt.expected)
			}
		})
	}
}

// TestJaktstartEdgeCases tests edge cases and potential error conditions
func TestJaktstartEdgeCases(t *testing.T) {
	tests := []struct {
		name        string
		distance    float64
		wind        int
		handicap    float64
		lowestSRS   float64
		description string
	}{
		{
			name:        "Very small distance",
			distance:    0.1,
			wind:        5,
			handicap:    1.0,
			lowestSRS:   0.95,
			description: "Should handle very short races",
		},
		{
			name:        "Very large distance",
			distance:    100.0,
			wind:        5,
			handicap:    1.0,
			lowestSRS:   0.95,
			description: "Should handle very long races",
		},
		{
			name:        "Very high SRS difference",
			distance:    10.0,
			wind:        5,
			handicap:    1.5,
			lowestSRS:   0.8,
			description: "Should handle large handicap differences",
		},
		{
			name:        "Very small SRS difference",
			distance:    10.0,
			wind:        5,
			handicap:    0.951,
			lowestSRS:   0.95,
			description: "Should handle small handicap differences",
		},
		{
			name:        "Negative wind (should still work)",
			distance:    10.0,
			wind:        -1,
			handicap:    1.0,
			lowestSRS:   0.95,
			description: "Should handle invalid wind gracefully",
		},
		{
			name:        "Very high wind",
			distance:    10.0,
			wind:        50,
			handicap:    1.0,
			lowestSRS:   0.95,
			description: "Should handle extreme wind conditions",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Should not panic and should return a reasonable result
			result := CalculateStartTime(tt.distance, tt.wind, tt.handicap, tt.lowestSRS)

			// Basic sanity checks
			if tt.distance > 0 && tt.handicap > 0 && tt.lowestSRS > 0 {
				// Result should be a multiple of 60 (rounded to minutes)
				if result%60 != 0 {
					t.Errorf("Result %d is not rounded to nearest minute", result)
				}

				// For reasonable inputs, result should be within reasonable bounds
				if tt.distance <= 50 && tt.handicap <= 2.0 && tt.lowestSRS >= 0.5 {
					if result < -7200 || result > 7200 { // ±2 hours seems reasonable
						t.Errorf("Result %d seconds (%.1f minutes) seems unreasonable for inputs: distance=%.1f, wind=%d, handicap=%.3f, lowestSRS=%.3f",
							result, float64(result)/60, tt.distance, tt.wind, tt.handicap, tt.lowestSRS)
					}
				}
			}

			t.Logf("%s: distance=%.1f, wind=%d, handicap=%.3f, lowestSRS=%.3f → %d seconds (%.1f minutes)",
				tt.description, tt.distance, tt.wind, tt.handicap, tt.lowestSRS, result, float64(result)/60)
		})
	}
}

// TestJaktstartRealWorldScenarios tests realistic sailing scenarios
func TestJaktstartRealWorldScenarios(t *testing.T) {
	tests := []struct {
		name     string
		distance float64
		wind     int
		boats    []struct {
			name      string
			srs       float64
			expectDir string // "before", "after", or "same"
		}
		description string
	}{
		{
			name:     "Typical club race - mixed fleet",
			distance: 8.0,
			wind:     5,
			boats: []struct {
				name      string
				srs       float64
				expectDir string
			}{
				{"Slow cruiser", 0.85, "same"},      // This will be the reference (lowest SRS = slowest boat)
				{"Medium cruiser", 1.0, "after"},    // Faster boat starts later
				{"Fast racing boat", 1.15, "after"}, // Fastest boat starts last
				{"Other boat", 0.95, "after"},       // Faster than reference
			},
			description: "Mixed fleet with typical SRS range",
		},
		{
			name:     "Light wind long distance",
			distance: 15.0,
			wind:     2,
			boats: []struct {
				name      string
				srs       float64
				expectDir string
			}{
				{"Heavy cruiser", 0.9, "same"},     // This will be the reference (lowest SRS = slowest boat)
				{"Performance boat", 1.1, "after"}, // Faster boat starts later
				{"Other boat", 0.95, "after"},      // Faster than reference
			},
			description: "Light wind amplifies time differences",
		},
		{
			name:     "Strong wind short race",
			distance: 5.0,
			wind:     12,
			boats: []struct {
				name      string
				srs       float64
				expectDir string
			}{
				{"Family cruiser", 0.88, "same"}, // This will be the reference (lowest SRS = slowest boat)
				{"Racing yacht", 1.05, "after"},  // Faster boat starts later
				{"Other boat", 0.95, "after"},    // Faster than reference
			},
			description: "Strong wind reduces time differences",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Find the reference boat (lowest SRS)
			lowestSRS := tt.boats[0].srs
			for _, boat := range tt.boats {
				if boat.srs < lowestSRS {
					lowestSRS = boat.srs
				}
			}

			t.Logf("Scenario: %s", tt.description)
			t.Logf("Distance: %.1f nm, Wind: %d m/s, Reference SRS: %.3f", tt.distance, tt.wind, lowestSRS)

			for _, boat := range tt.boats {
				offset := CalculateStartTime(tt.distance, tt.wind, boat.srs, lowestSRS)
				formatted := FormatStartTime(offset)

				// Verify direction expectation
				switch boat.expectDir {
				case "before":
					if offset >= 0 {
						t.Errorf("Boat %s (SRS %.3f) expected to start before reference, but offset is %d seconds",
							boat.name, boat.srs, offset)
					}
				case "after":
					if offset <= 0 {
						t.Errorf("Boat %s (SRS %.3f) expected to start after reference, but offset is %d seconds",
							boat.name, boat.srs, offset)
					}
				case "same":
					if offset != 0 {
						t.Errorf("Boat %s (SRS %.3f) expected to start at same time as reference, but offset is %d seconds",
							boat.name, boat.srs, offset)
					}
				}

				t.Logf("  %s (SRS %.3f): %s", boat.name, boat.srs, formatted)
			}
		})
	}
}

// TestJaktstartConsistency tests mathematical consistency of calculations
func TestJaktstartConsistency(t *testing.T) {
	distance := 10.0
	wind := 5
	srsValues := []float64{0.8, 0.85, 0.9, 0.95, 1.0, 1.05, 1.1, 1.15, 1.2}

	// Find the lowest SRS (slowest boat) to use as reference
	lowestSRS := srsValues[0]
	for _, srs := range srsValues {
		if srs < lowestSRS {
			lowestSRS = srs
		}
	}

	// Test that slower boats (lower SRS) start at 0 and faster boats (higher SRS) start later

	var previousOffset int
	var previousSRS float64

	for i, srs := range srsValues {
		offset := CalculateStartTime(distance, wind, srs, lowestSRS)

		// The slowest boat (lowest SRS) should always have 0 offset
		if srs == lowestSRS && offset != 0 {
			t.Errorf("Slowest boat (SRS %.3f) should have 0 offset, got %d", srs, offset)
		}

		// All other boats should have positive offsets (start later)
		if srs > lowestSRS && offset <= 0 {
			t.Errorf("Faster boat (SRS %.3f) should have positive offset, got %d", srs, offset)
		}

		if i > 0 {
			// Higher SRS should result in later start time (more positive offset)
			if srs > previousSRS && offset < previousOffset {
				t.Errorf("Consistency check failed: SRS %.3f (offset %d) should start later than SRS %.3f (offset %d)",
					srs, offset, previousSRS, previousOffset)
			}
		}

		previousOffset = offset
		previousSRS = srs

		t.Logf("SRS %.3f → %d seconds (%s)", srs, offset, FormatStartTime(offset))
	}
}
