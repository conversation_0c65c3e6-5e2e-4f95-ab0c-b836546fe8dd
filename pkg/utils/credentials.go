package utils

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
)

const (
	// CredentialsDir is the directory where credentials are stored
	CredentialsDir = "credentials"

	// GitHubTokenFile is the file where the GitHub token is stored
	GitHubTokenFile = "github_token.txt"
)

// EnsureCredentialsDirExists creates the credentials directory if it doesn't exist
func EnsureCredentialsDirExists() error {
	if _, err := os.Stat(CredentialsDir); os.IsNotExist(err) {
		log.Printf("Creating credentials directory: %s", CredentialsDir)
		if err := os.MkdirAll(CredentialsDir, 0700); err != nil {
			return fmt.Errorf("failed to create credentials directory: %w", err)
		}
	}
	return nil
}

// SaveGitHubToken saves the GitHub token to a file
func SaveGitHubToken(token string) error {
	if err := EnsureCredentialsDirExists(); err != nil {
		return err
	}

	// Create the file with restricted permissions (readable only by the owner)
	tokenPath := filepath.Join(CredentialsDir, GitHubTokenFile)
	err := os.WriteFile(tokenPath, []byte(token), 0600)
	if err != nil {
		return fmt.Errorf("failed to save GitHub token: %w", err)
	}

	log.Printf("GitHub token saved to: %s", tokenPath)
	return nil
}

// GetGitHubToken reads the GitHub token from the file
func GetGitHubToken() (string, error) {
	tokenPath := filepath.Join(CredentialsDir, GitHubTokenFile)

	// Check if the file exists
	if _, err := os.Stat(tokenPath); os.IsNotExist(err) {
		log.Printf("GitHub token file not found: %s", tokenPath)
		return "", nil // Return empty string but no error if file doesn't exist
	}

	// Read the token from the file
	tokenBytes, err := os.ReadFile(tokenPath)
	if err != nil {
		return "", fmt.Errorf("failed to read GitHub token: %w", err)
	}

	// Trim any whitespace
	token := strings.TrimSpace(string(tokenBytes))
	return token, nil
}

// DeleteGitHubToken deletes the GitHub token file
func DeleteGitHubToken() error {
	tokenPath := filepath.Join(CredentialsDir, GitHubTokenFile)

	// Check if the file exists
	if _, err := os.Stat(tokenPath); os.IsNotExist(err) {
		return nil // File doesn't exist, nothing to delete
	}

	// Delete the file
	if err := os.Remove(tokenPath); err != nil {
		return fmt.Errorf("failed to delete GitHub token: %w", err)
	}

	log.Printf("GitHub token deleted: %s", tokenPath)
	return nil
}

// MigrateGitHubTokenFromDB migrates the GitHub token from the database to a file
func MigrateGitHubTokenFromDB(getTokenFunc func() (string, error)) error {
	// Get the token from the database
	token, err := getTokenFunc()
	if err != nil {
		return fmt.Errorf("failed to get GitHub token from database: %w", err)
	}

	// If token is empty, nothing to migrate
	if token == "" {
		log.Printf("No GitHub token found in database, nothing to migrate")
		return nil
	}

	// Save the token to a file
	if err := SaveGitHubToken(token); err != nil {
		return err
	}

	log.Printf("GitHub token migrated from database to file")
	return nil
}
