package utils

import (
	"fmt"
	"log"
	"strings"
)

// LogDebug logs a debug message
func LogDebug(v ...interface{}) {
	log.Printf("[DEBUG] %s", fmt.Sprint(v...))
}

// LogInfo logs an info message
func LogInfo(v ...interface{}) {
	log.Printf("[INFO] %s", fmt.Sprint(v...))
}

// LogWarning logs a warning message
func LogWarning(v ...interface{}) {
	log.Printf("[WARNING] %s", fmt.Sprint(v...))
}

// LogError logs an error message
func LogError(v ...interface{}) {
	log.Printf("[ERROR] %s", fmt.Sprint(v...))
}

// FormatParams formats parameters for logging
func FormatParams(params map[string]string) string {
	var parts []string
	for k, v := range params {
		parts = append(parts, fmt.Sprintf("%s=%s", k, v))
	}
	return strings.Join(parts, ", ")
}
