{{ template "header.html" . }}

<div class="container mt-4">
    <h1>Hjälp</h1>

    <div class="row">
        <div class="col-md-3">
            <div class="list-group mb-4 sticky-top" style="top: 20px;">
                <a href="#overview" class="list-group-item list-group-item-action">Översikt</a>
                <a href="#sailors" class="list-group-item list-group-item-action">Seglare</a>
                <a href="#boats" class="list-group-item list-group-item-action">Båtar</a>
                <a href="#events" class="list-group-item list-group-item-action">Tävlingar</a>
                <a href="#participants" class="list-group-item list-group-item-action">Deltagare</a>
                <a href="#entypsegling" class="list-group-item list-group-item-action">Entypsegling</a>
                <a href="#jaktstart" class="list-group-item list-group-item-action">Jaktstart</a>
                <a href="#finish-times" class="list-group-item list-group-item-action"><PERSON><PERSON><PERSON>ider</a>
                <a href="#results" class="list-group-item list-group-item-action">Resultat</a>
                <a href="#github-pages" class="list-group-item list-group-item-action">GitHub Pages</a>
                <a href="#google-drive" class="list-group-item list-group-item-action">Google Drive</a>
                <a href="#srs-data" class="list-group-item list-group-item-action">SRS Data</a>
                <a href="#weather" class="list-group-item list-group-item-action">Väder</a>
                <a href="#settings" class="list-group-item list-group-item-action">Inställningar</a>
            </div>
        </div>

        <div class="col-md-9">
            <div class="card mb-4" id="overview">
                <div class="card-header">
                    <h2>Översikt</h2>
                </div>
                <div class="card-body">
                    <p>Segling är en applikation för att hantera seglingstävlingar. Med denna applikation kan du:</p>
                    <ul>
                        <li>Hantera seglare och båtar</li>
                        <li>Skapa och hantera tävlingar</li>
                        <li>Lägga till deltagare i tävlingar</li>
                        <li>Beräkna jaktstartstider</li>
                        <li>Hantera entypsegling (entypsflottor) med placeringsbaserade resultat</li>
                        <li>Registrera måltider eller placeringar</li>
                        <li>Visa resultat</li>
                        <li>Visa aktuell väderinformation från Trafikverkets väderstationer</li>
                        <li>Publicera resultat till GitHub Pages för att dela dem online</li>
                        <li>Exportera resultat till Google Drive som Google Sheets</li>
                        <li>Exportera resultat till CSV-format</li>
                        <li>Hantera SRS-data</li>
                        <li>Konfigurera applikationsinställningar</li>
                        <li>Skapa automatiska säkerhetskopior av databasen</li>
                    </ul>
                </div>
            </div>

            <div class="card mb-4" id="sailors">
                <div class="card-header">
                    <h2>Seglare</h2>
                </div>
                <div class="card-body">
                    <p>Under menyn "Seglare" kan du hantera alla seglare i systemet.</p>
                    <h4>Funktioner:</h4>
                    <ul>
                        <li><strong>Visa alla seglare:</strong> Se en lista över alla seglare i systemet</li>
                        <li><strong>Sök seglare:</strong> Använd sökfunktionen för att hitta specifika seglare</li>
                        <li><strong>Lägg till seglare:</strong> Registrera nya seglare i systemet</li>
                        <li><strong>Redigera seglare:</strong> Uppdatera information om befintliga seglare</li>
                        <li><strong>Ta bort seglare:</strong> Radera seglare från systemet</li>
                    </ul>
                </div>
            </div>

            <div class="card mb-4" id="boats">
                <div class="card-header">
                    <h2>Båtar</h2>
                </div>
                <div class="card-body">
                    <p>Under menyn "Båtar" kan du hantera alla båtar i systemet.</p>
                    <h4>Funktioner:</h4>
                    <ul>
                        <li><strong>Visa alla båtar:</strong> Se en lista över alla båtar i systemet</li>
                        <li><strong>Sök båtar:</strong> Använd sökfunktionen med live-filtrering för att hitta specifika båtar</li>
                        <li><strong>Lägg till båt:</strong> Registrera nya båtar i systemet</li>
                        <li><strong>Redigera båt:</strong> Uppdatera information om befintliga båtar</li>
                        <li><strong>Ta bort båt:</strong> Radera båtar från systemet</li>
                        <li><strong>Hämta data:</strong> Hämta SRS-data för en båt baserat på mätbrevsnummer</li>
                    </ul>
                    <h4>Mätbrevsnummer:</h4>
                    <p>I mätbrevsnummerfältet kan du söka på mätbrevsnummer, båttyp, båtnamn, ägare eller segelnummer:</p>
                    <ul>
                        <li>Börja skriva för att se förslag från den lokala databasen</li>
                        <li>Välj ett mätbrev från förslagen för att automatiskt fylla i båttyp, båtnamn och SRS-värden</li>
                        <li>Om du anger ett komplett mätbrevsnummer i formatet B#### eller E#### hämtas data automatiskt</li>
                    </ul>
                    <h4>SRS-värden:</h4>
                    <p>Varje båt har fyra olika SRS-värden som visas i båtlistan:</p>
                    <ul>
                        <li><strong>SRS:</strong> Standardvärde för båten</li>
                        <li><strong>SRS utan undanvindsegel:</strong> Värde när båten seglar utan spinnaker/gennaker</li>
                        <li><strong>SRS S/H:</strong> Värde när båten seglas med reducerad besättning (Shorthanded)</li>
                        <li><strong>SRS S/H utan undanvindsegel:</strong> Värde när båten seglas med reducerad besättning och utan spinnaker/gennaker</li>
                    </ul>
                </div>
            </div>

            <div class="card mb-4" id="events">
                <div class="card-header">
                    <h2>Tävlingar</h2>
                </div>
                <div class="card-body">
                    <p>Under menyn "Tävlingar" kan du hantera alla seglingstävlingar i systemet.</p>
                    <h4>Funktioner:</h4>
                    <ul>
                        <li><strong>Visa alla tävlingar:</strong> Se en lista över alla tävlingar i systemet</li>
                        <li><strong>Filtrera tävlingar per år:</strong> Visa tävlingar för ett specifikt år</li>
                        <li><strong>Skapa ny tävling:</strong> Registrera en ny tävling i systemet</li>
                        <li><strong>Redigera tävling:</strong> Uppdatera information om en befintlig tävling</li>
                        <li><strong>Ta bort tävling:</strong> Radera en tävling från systemet</li>
                    </ul>
                    <h4>Tävlingsinformation:</h4>
                    <p>För varje tävling kan du ange följande information:</p>
                    <ul>
                        <li><strong>Namn:</strong> Tävlingens namn</li>
                        <li><strong>Datum:</strong> Datum för tävlingen</li>
                        <li><strong>Starttid:</strong> Starttid för tävlingen (används för jaktstart)</li>
                        <li><strong>Vind:</strong> Vindstyrka i m/s (används för beräkning av jaktstart)</li>
                        <li><strong>Banlängd:</strong> Banlängd i nautiska mil (används för beräkning av jaktstart)</li>
                        <li><strong>Jaktstart:</strong> Markera om tävlingen använder jaktstart</li>
                        <li><strong>Entypsegling:</strong> Markera om tävlingen är för entypsegling (entypsflottor)</li>
                        <li><strong>Båttyp:</strong> Båttyp för entypsegling-tävlingar (t.ex. "Laser", "Optimist")</li>
                        <li><strong>Beskrivning:</strong> Valfri beskrivning av tävlingen</li>
                    </ul>

                    <h4>Låsning av tävlingar:</h4>
                    <p>När en tävling är avslutad och resultaten är klara kan du låsa tävlingen för att bevara resultaten:</p>
                    <ul>
                        <li><strong>Låsa tävling:</strong> Klicka på "Lås tävling" på resultatsidan för att låsa tävlingen och bevara resultaten</li>
                        <li><strong>Låsta tävlingar:</strong> Låsta tävlingar markeras med ett hänglåsikon i tävlingslistan</li>
                        <li><strong>Låsta resultat:</strong> Resultaten för låsta tävlingar bevaras även om båtdata ändras</li>
                        <li><strong>Låsa upp tävling:</strong> Om du behöver göra ändringar i en låst tävling kan du låsa upp den genom att klicka på "Lås upp tävling" på resultatsidan</li>
                    </ul>
                    <p>Observera att när du låser upp en tävling kommer de sparade resultaten att raderas och nya resultat kommer att beräknas baserat på aktuella båtdata och måltider.</p>
                </div>
            </div>

            <div class="card mb-4" id="participants">
                <div class="card-header">
                    <h2>Deltagare</h2>
                </div>
                <div class="card-body">
                    <p>När du redigerar en tävling kan du hantera deltagare för tävlingen.</p>
                    <h4>Funktioner:</h4>
                    <ul>
                        <li><strong>Lägg till deltagare:</strong> Registrera en ny deltagare i tävlingen</li>
                        <li><strong>Redigera deltagare:</strong> Uppdatera information om en befintlig deltagare</li>
                        <li><strong>Ta bort deltagare:</strong> Radera en deltagare från tävlingen</li>
                        <li><strong>Kopiera deltagare:</strong> Kopiera deltagare från en annan tävling</li>
                    </ul>
                    <h4>SRS-typ:</h4>
                    <p>För varje deltagare kan du välja vilken SRS-typ som ska användas:</p>
                    <ul>
                        <li><strong>SRS med undanvind:</strong> Standardvärde för båten</li>
                        <li><strong>SRS utan undanvindsegel:</strong> Värde när båten seglar utan spinnaker/gennaker</li>
                        <li><strong>S/H med undanvind:</strong> Värde när båten seglas med reducerad besättning (Shorthanded)</li>
                        <li><strong>S/H utan undanvindsegel:</strong> Värde när båten seglas med reducerad besättning och utan spinnaker/gennaker</li>
                    </ul>

                    <h4>SRS-värden:</h4>
                    <p>När du lägger till eller redigerar en deltagare visas två SRS-värden:</p>
                    <ul>
                        <li><strong>Båtens SRS-värde:</strong> Det valda SRS-värdet för båten baserat på vald SRS-typ (skrivskyddat)</li>
                        <li><strong>Anpassat SRS-värde:</strong> Ett manuellt angivet SRS-värde som kan användas istället för båtens SRS-värde</li>
                    </ul>
                    <p>För att använda ett anpassat SRS-värde:</p>
                    <ol>
                        <li>Markera kryssrutan "Använd" bredvid det anpassade SRS-värdet</li>
                        <li>Ange det önskade SRS-värdet i fältet</li>
                    </ol>
                    <p>Om kryssrutan "Använd" inte är markerad kommer båtens ordinarie SRS-värde att användas för beräkningar.</p>
                </div>
            </div>

            <div class="card mb-4" id="entypsegling">
                <div class="card-header">
                    <h2>Entypsegling</h2>
                </div>
                <div class="card-body">
                    <p>Entypsegling är ett tävlingsformat för entypsflottor där alla deltagare seglar med samma båttyp. Istället för tidsbaserade beräkningar med SRS-handikapp baseras resultaten enbart på målgångsordning (placeringar).</p>

                    <h4>Skapa en entypsegling-tävling:</h4>
                    <ol>
                        <li>Navigera till "Tävlingar" (Competitions)</li>
                        <li>Klicka "Skapa ny tävling" (Create new competition)</li>
                        <li>Fyll i tävlingsdetaljerna (namn, datum, etc.)</li>
                        <li>Markera kryssrutan "Entypsegling"</li>
                        <li>Ange båttypen i fältet "Boat Type" (t.ex. "Laser", "Optimist", "420")</li>
                        <li>Spara tävlingen</li>
                    </ol>
                    <p><strong>Observera:</strong> När entypsegling är aktiverat inaktiveras jaktstart automatiskt eftersom de är inkompatibla format.</p>

                    <h4>Hantera deltagare i entypsegling:</h4>
                    <p>För entypsegling-tävlingar hanteras deltagare annorlunda:</p>
                    <ul>
                        <li>Deltagare kopplas inte till båtar från båtdatabasen</li>
                        <li>Varje deltagare har ett personligt segelnummer (t.ex. "SWE 123", "FIN 456")</li>
                        <li>Deltagare kopplas fortfarande till seglare från seglardatabasen</li>
                    </ul>
                    <p>För att lägga till deltagare:</p>
                    <ol>
                        <li>Navigera till tävlingens "Hantera deltagare" sida</li>
                        <li>Välj en seglare från databasen</li>
                        <li>Ange det personliga segelnumret i fältet "Personal Number"</li>
                        <li>Spara deltagaren</li>
                    </ol>

                    <h4>Registrera resultat:</h4>
                    <p>För entypsegling-tävlingar registrerar du placeringar istället för måltider:</p>
                    <ol>
                        <li>Navigera till tävlingens "Måltider" sida</li>
                        <li>För varje deltagare, ange deras placering (1:a, 2:a, 3:e, etc.)</li>
                        <li>För deltagare som inte startade eller inte fullföljde, markera DNS eller DNF kryssrutan</li>
                        <li>Spara resultaten</li>
                    </ol>

                    <h4>Visa resultat:</h4>
                    <p>Resultat för entypsegling-tävlingar visar:</p>
                    <ul>
                        <li>Position (slutlig placering)</li>
                        <li>Seglarinformation</li>
                        <li>Personligt segelnummer</li>
                        <li>Besättningsantal</li>
                        <li>Status (DNF/DNS om tillämpligt)</li>
                    </ul>
                    <p>För tävlingar med flera heat visar resultaten också:</p>
                    <ul>
                        <li>Totalposition</li>
                        <li>Totalpoäng</li>
                        <li>Individuella heat-placeringar</li>
                    </ul>

                    <h4>Lika placeringar:</h4>
                    <p>Entypsegling stöder lika placeringar:</p>
                    <ul>
                        <li>När flera båtar tilldelas samma placering får de medelpoängen för dessa positioner</li>
                        <li>Poäng visas med en decimals precision (t.ex. 1.5p för en 2-vägs delning av 1:a plats)</li>
                        <li>Lika placeringar indikeras tydligt i resultaten med identiska placeringsnummer</li>
                    </ul>

                    <h4>Exportera resultat:</h4>
                    <p>Alla exportformat (CSV, Google Drive, GitHub Pages) stöder entypsegling-tävlingar med lämplig formatering för placeringsbaserade resultat.</p>
                </div>
            </div>

            <div class="card mb-4" id="jaktstart">
                <div class="card-header">
                    <h2>Jaktstart</h2>
                </div>
                <div class="card-body">
                    <p>Om en tävling använder jaktstart kan du visa jaktstartstider för alla deltagare.</p>
                    <h4>Funktioner:</h4>
                    <ul>
                        <li><strong>Visa jaktstartstider:</strong> Se en lista över alla deltagares starttider</li>
                        <li><strong>Skriv ut jaktstartstider:</strong> Skriv ut en utskriftsvänlig version av jaktstartstiderna</li>
                    </ul>
                    <h4>Beräkning av jaktstartstider:</h4>
                    <p>Jaktstartstider beräknas baserat på båtarnas SRS-värden, den planerade banlängden och den förväntade vindhastigheten. Båten med lägst SRS-värde startar först (referensbåten), och övriga båtar startar senare baserat på deras SRS-värde och den teoretiska tidsskillnaden över den planerade banan.</p>
                    <p>Observera att jaktstartstiderna är teoretiska och baseras på ideala förhållanden. Om de faktiska förhållandena (vind, banlängd) avviker från de planerade, kommer detta att påverka resultatet. Därför beräknas alltid de slutliga resultaten baserat på faktiska måltider och SRS-värden.</p>
                </div>
            </div>

            <div class="card mb-4" id="finish-times">
                <div class="card-header">
                    <h2>Måltider</h2>
                </div>
                <div class="card-body">
                    <p>Under "Måltider" kan du registrera måltider för alla deltagare i en tävling.</p>
                    <h4>Funktioner för vanliga tävlingar:</h4>
                    <ul>
                        <li><strong>Registrera måltid:</strong> Ange måltid för en deltagare i formatet HH:MM:SS</li>
                        <li><strong>Rensa måltid:</strong> Ta bort en registrerad måltid</li>
                    </ul>
                    <h4>Funktioner för entypsegling-tävlingar:</h4>
                    <ul>
                        <li><strong>Registrera placering:</strong> Ange placering för en deltagare (1, 2, 3, etc.)</li>
                        <li><strong>DNS/DNF:</strong> Markera deltagare som inte startade (DNS) eller inte fullföljde (DNF)</li>
                        <li><strong>Lika placeringar:</strong> Flera deltagare kan tilldelas samma placering</li>
                    </ul>
                </div>
            </div>

            <div class="card mb-4" id="results">
                <div class="card-header">
                    <h2>Resultat</h2>
                </div>
                <div class="card-body">
                    <p>Under "Resultat" kan du se resultatlistan för en tävling.</p>
                    <h4>Funktioner:</h4>
                    <ul>
                        <li><strong>Visa resultat:</strong> Se en lista över alla deltagare sorterade efter korrigerad tid</li>
                        <li><strong>Skriv ut resultat:</strong> Skriv ut en utskriftsvänlig version av resultatlistan</li>
                        <li><strong>Publicera resultat:</strong> Publicera resultaten till GitHub Pages för att dela dem online</li>
                        <li><strong>Exportera till Google Drive:</strong> Exportera resultaten till Google Drive som Google Sheets för enkel delning och samarbete</li>
                        <li><strong>Exportera resultat:</strong> Exportera resultaten till CSV-format för användning i andra program</li>
                    </ul>

                    <h4>Beräkning av resultat:</h4>
                    <p><strong>Vanliga tävlingar:</strong> Resultaten beräknas baserat på deltagarnas måltider och SRS-värden.</p>
                    <p><strong>Entypsegling-tävlingar:</strong> Resultaten baseras enbart på placeringar utan tidsberäkningar eller SRS-handikapp.</p>
                    <h4>Jaktstart och resultat:</h4>
                    <p>För jaktstart är målgångsordningen <strong>inte nödvändigtvis</strong> samma som den slutliga resultatlistan. Jaktstartstiderna beräknas baserat på planerad vind och banlängd, men om de faktiska förhållandena avviker från de planerade (t.ex. om vinden ändras under tävlingen eller om banan blir kortare/längre än planerat) påverkas resultatet. Systemet beräknar alltid korrigerade tider baserat på faktiska måltider och SRS-värden för att säkerställa rättvisa resultat.</p>
                </div>
            </div>

            <div class="card mb-4" id="github-pages">
                <div class="card-header">
                    <h2>GitHub Pages</h2>
                </div>
                <div class="card-body">
                    <p>Segling har stöd för att publicera tävlingsresultat till GitHub Pages, vilket gör det möjligt att dela resultaten online med deltagare, åskådare och andra intresserade.</p>

                    <h4>Vad är GitHub Pages?</h4>
                    <p>GitHub Pages är en gratis webbtjänst från GitHub som låter dig publicera webbsidor direkt från ett GitHub-repository. I Segling används denna tjänst för att publicera tävlingsresultat i ett format som är tillgängligt via en webbläsare.</p>

                    <h4>Förutsättningar för publicering:</h4>
                    <ul>
                        <li><strong>Låst tävling:</strong> Tävlingen måste vara låst innan den kan publiceras</li>
                        <li><strong>GitHub-konfiguration:</strong> GitHub-inställningarna måste vara korrekt konfigurerade i applikationens inställningar</li>
                    </ul>

                    <h4>Publicera resultat till GitHub Pages:</h4>
                    <ol>
                        <li>Gå till resultatsidan för en låst tävling</li>
                        <li>Klicka på knappen "Publicera till GitHub Pages"</li>
                        <li>Vänta medan systemet:
                            <ul>
                                <li>Genererar HTML-sidor för tävlingsresultaten</li>
                                <li>Laddar upp filerna till GitHub</li>
                                <li>Väntar på att GitHub Pages ska göra sidorna tillgängliga</li>
                            </ul>
                        </li>
                        <li>När publiceringen är klar aktiveras knappen "Visa publicerad sida"</li>
                        <li>Klicka på "Visa publicerad sida" för att öppna den publicerade sidan i en ny flik</li>
                    </ol>

                    <h4>Publicerade sidor:</h4>
                    <p>De publicerade sidorna innehåller:</p>
                    <ul>
                        <li><strong>Indexsida:</strong> En lista över alla publicerade tävlingar, sorterade efter datum</li>
                        <li><strong>Resultatsidor:</strong> Detaljerade resultatsidor för varje tävling med:
                            <ul>
                                <li>Tävlingsnamn och datum</li>
                                <li>Vind, banlängd och starttid</li>
                                <li>Komplett resultattabell med alla deltagare</li>
                                <li>Samma kolumner som i den utskriftsvänliga versionen:
                                    <ul>
                                        <li>Placering</li>
                                        <li>Seglare</li>
                                        <li>Klubb</li>
                                        <li>Båttyp</li>
                                        <li>Segelnummer</li>
                                        <li>Mätbrev</li>
                                        <li>SRS-värde</li>
                                        <li>Besättning</li>
                                        <li>Starttid</li>
                                        <li>Måltid</li>
                                        <li>Seglad tid</li>
                                        <li>Korrigerad tid</li>
                                        <li>Efter vinnare</li>
                                    </ul>
                                </li>
                            </ul>
                        </li>
                    </ul>

                    <h4>Dela publicerade resultat:</h4>
                    <p>När resultaten är publicerade kan du dela webbadressen med andra. Adressen visas efter publiceringen och har formatet:</p>
                    <pre>https://[användarnamn].github.io/sailapp/</pre>
                    <p>Denna adress leder till indexsidan med alla publicerade tävlingar. För att dela en specifik tävling, navigera till tävlingen och kopiera adressen från webbläsarens adressfält.</p>

                    <h4>Uppdatera publicerade resultat:</h4>
                    <p>Om du behöver göra ändringar i en tävling efter att den har publicerats:</p>
                    <ol>
                        <li>Lås upp tävlingen</li>
                        <li>Gör nödvändiga ändringar</li>
                        <li>Lås tävlingen igen</li>
                        <li>Publicera tävlingen på nytt genom att klicka på "Publicera till GitHub Pages"</li>
                    </ol>
                    <p>Detta kommer att uppdatera de publicerade resultaten med de senaste ändringarna.</p>

                    <h4>Ta bort publicerade resultat:</h4>
                    <p>För att ta bort publicerade resultat, klicka på "Ta bort från GitHub Pages" på resultatsidan. Detta kommer att ta bort tävlingen från den publicerade indexsidan och radera resultatsidan.</p>

                    <h4>Mobilanpassning:</h4>
                    <p>De publicerade sidorna är responsiva och anpassar sig automatiskt till olika skärmstorlekar, vilket gör dem lätta att läsa på både datorer, surfplattor och mobiltelefoner.</p>
                </div>
            </div>

            <div class="card mb-4" id="google-drive">
                <div class="card-header">
                    <h2>Google Drive Export</h2>
                </div>
                <div class="card-body">
                    <p>Segling har stöd för att exportera tävlingsresultat direkt till Google Drive som Google Sheets, vilket gör det enkelt att dela och samarbeta kring resultaten.</p>

                    <h4>Vad är Google Drive Export?</h4>
                    <p>Google Drive Export låter dig skapa Google Sheets-dokument med tävlingsresultat direkt från Segling-applikationen. Dokumenten sparas i din Google Drive och kan enkelt delas med andra.</p>

                    <h4>Förutsättningar för Google Drive Export:</h4>
                    <ul>
                        <li><strong>Google Cloud-projekt:</strong> Du behöver ett Google Cloud-projekt med aktiverade APIs</li>
                        <li><strong>Autentisering:</strong> Du måste autentisera applikationen med ditt Google-konto</li>
                        <li><strong>Aktiverad integration:</strong> Google Drive-integration måste vara aktiverad i inställningarna</li>
                    </ul>

                    <h4>Konfigurera Google Drive Export:</h4>
                    <ol>
                        <li><strong>Skapa Google Cloud-projekt:</strong>
                            <ul>
                                <li>Gå till <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
                                <li>Skapa ett nytt projekt eller välj ett befintligt</li>
                            </ul>
                        </li>
                        <li><strong>Aktivera nödvändiga APIs:</strong>
                            <ul>
                                <li>Gå till "APIs & Services" > "Library"</li>
                                <li>Sök efter och aktivera <strong>"Google Drive API"</strong></li>
                                <li>Sök efter och aktivera <strong>"Google Sheets API"</strong></li>
                                <li><strong>Viktigt:</strong> Båda APIs måste vara aktiverade!</li>
                            </ul>
                        </li>
                        <li><strong>Skapa autentiseringsuppgifter:</strong>
                            <ul>
                                <li>Gå till "APIs & Services" > "Credentials"</li>
                                <li>Klicka "Create Credentials" > "OAuth client ID"</li>
                                <li>Välj "Desktop application" som applikationstyp</li>
                                <li>Ladda ner JSON-filen med autentiseringsuppgifter</li>
                            </ul>
                        </li>
                        <li><strong>Konfigurera i Segling:</strong>
                            <ul>
                                <li>Gå till "Inställningar" i Segling</li>
                                <li>Scrolla ner till "Google Drive Integration"</li>
                                <li>Ladda upp din client_secrets.json-fil</li>
                                <li>Aktivera Google Drive-integration</li>
                                <li>Konfigurera mapp-ID och namnkonvention (valfritt)</li>
                            </ul>
                        </li>
                        <li><strong>Autentisera:</strong>
                            <ul>
                                <li>Klicka "Kontrollera autentisering"</li>
                                <li>Klicka "Autentisera" för att starta OAuth-flödet</li>
                                <li>Logga in med ditt Google-konto och ge behörigheter</li>
                                <li>Du kommer automatiskt tillbaka till inställningssidan</li>
                            </ul>
                        </li>
                    </ol>

                    <h4>Exportera resultat till Google Drive:</h4>
                    <ol>
                        <li>Gå till resultatsidan för en tävling</li>
                        <li>Klicka på knappen "Exportera till Google Drive"</li>
                        <li>Vänta medan systemet:
                            <ul>
                                <li>Skapar ett nytt Google Sheets-dokument</li>
                                <li>Fyller i tävlingsdata och resultat</li>
                                <li>Sparar dokumentet i din Google Drive</li>
                            </ul>
                        </li>
                        <li>När exporten är klar öppnas det nya dokumentet automatiskt i en ny flik</li>
                    </ol>

                    <h4>Exporterade dokument innehåller:</h4>
                    <ul>
                        <li><strong>Tävlingsinformation:</strong> Namn, datum, starttid, vind och banlängd</li>
                        <li><strong>Komplett resultattabell:</strong> Alla deltagare med:
                            <ul>
                                <li>Placering</li>
                                <li>Seglare och klubb</li>
                                <li>Båtinformation (namn, typ, segelnummer, mätbrev)</li>
                                <li>SRS-värde och typ</li>
                                <li>Besättning</li>
                                <li>Tider (start, mål, seglad, korrigerad)</li>
                                <li>Tidsskillnader (efter föregående, efter vinnare)</li>
                            </ul>
                        </li>
                    </ul>

                    <h4>Mapporganisation:</h4>
                    <p>Du kan organisera dina exporterade resultat genom att ange ett mapp-ID i inställningarna:</p>
                    <ul>
                        <li><strong>Hitta mapp-ID:</strong> Öppna mappen i Google Drive och kopiera ID:t från URL:en</li>
                        <li><strong>Exempel:</strong> URL: https://drive.google.com/drive/folders/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74mMngDnt</li>
                        <li><strong>Mapp-ID:</strong> 1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74mMngDnt</li>
                    </ul>

                    <h4>Namnkonvention:</h4>
                    <p>Du kan anpassa hur exporterade filer namnges med hjälp av platshållare:</p>
                    <ul>
                        <li><strong>&#123;&#123;event_name&#125;&#125;:</strong> Tävlingens namn</li>
                        <li><strong>&#123;&#123;date&#125;&#125;:</strong> Datum (YYYY-MM-DD)</li>
                        <li><strong>&#123;&#123;competition_type&#125;&#125;:</strong> Tävlingstyp</li>
                        <li><strong>&#123;&#123;year&#125;&#125;, &#123;&#123;month&#125;&#125;, &#123;&#123;day&#125;&#125;:</strong> Datumkomponenter</li>
                    </ul>
                    <p><strong>Exempel:</strong> "&#123;&#123;competition_type&#125;&#125;_&#123;&#123;event_name&#125;&#125;_&#123;&#123;date&#125;&#125;" blir "Kvällssegling_Midsommarsegling_2024-06-21"</p>

                    <h4>Felsökning:</h4>
                    <ul>
                        <li><strong>"Project not enabled" fel:</strong> Kontrollera att både Google Drive API och Google Sheets API är aktiverade</li>
                        <li><strong>"Not authenticated" fel:</strong> Genomför autentiseringsprocessen igen</li>
                        <li><strong>"This app isn't verified" varning:</strong> Klicka "Advanced" > "Go to [App Name] (unsafe)" - detta är normalt för personliga appar</li>
                        <li><strong>404-fel vid autentisering:</strong> Kontrollera att Segling körs på http://localhost:8080</li>
                    </ul>

                    <h4>Dela exporterade resultat:</h4>
                    <p>När resultaten är exporterade till Google Drive kan du:</p>
                    <ul>
                        <li><strong>Dela dokumentet:</strong> Använd Googles delningsfunktioner för att ge andra åtkomst</li>
                        <li><strong>Samarbeta:</strong> Flera personer kan kommentera och redigera dokumentet samtidigt</li>
                        <li><strong>Ladda ner:</strong> Exportera till Excel, PDF eller andra format från Google Sheets</li>
                    </ul>
                </div>
            </div>

            <div class="card mb-4" id="srs-data">
                <div class="card-header">
                    <h2>SRS Data</h2>
                </div>
                <div class="card-body">
                    <p>Under menyn "SRS Data" kan du hantera SRS-data från Svensk Segling.</p>
                    <h4>Funktioner:</h4>
                    <ul>
                        <li><strong>Uppdatera SRS-data:</strong> Hämta senaste SRS-data från https://matbrev.svensksegling.se/</li>
                        <li><strong>Visa SRS Tabell:</strong> Se en lista över alla båttyper i SRS-tabellen</li>
                        <li><strong>Visa Mätbrev:</strong> Se en lista över alla båtar med mätbrev</li>
                        <li><strong>Sök SRS-data:</strong> Använd sökfunktionen med live-filtrering för att hitta specifika båttyper eller mätbrev</li>
                        <li><strong>Validering av mätbrevsnummer:</strong> Automatisk validering av format (B#### eller E####)</li>
                        <li><strong>Offline-användning:</strong> All SRS-data lagras lokalt för snabb åtkomst utan internetanslutning</li>
                    </ul>
                    <h4>Offline-åtkomst:</h4>
                    <p>När du har uppdaterat SRS-data lagras den i den lokala databasen, vilket ger följande fördelar:</p>
                    <ul>
                        <li><strong>Snabb sökning:</strong> Omedelbar åtkomst till SRS-data utan att behöva vänta på nätverksanrop</li>
                        <li><strong>Offline-användning:</strong> Tillgång till SRS-data även utan internetanslutning</li>
                        <li><strong>Automatisk ifyllning:</strong> När du lägger till eller redigerar en båt med mätbrevsnummer fylls båttyp och båtnamn i automatiskt</li>
                    </ul>
                    <h4>Mätbrev vs SRS Tabell:</h4>
                    <ul>
                        <li><strong>Mätbrev:</strong> Innehåller data för specifika båtar med mätbrevsnummer, inklusive båtnamn och ägare</li>
                        <li><strong>SRS Tabell:</strong> Innehåller standardvärden för olika båttyper utan koppling till specifika båtar</li>
                    </ul>
                </div>
            </div>

            <div class="card mb-4" id="weather">
                <div class="card-header">
                    <h2>Väder</h2>
                </div>
                <div class="card-body">
                    <p>Segling har inbyggt stöd för att visa aktuell väderinformation från Trafikverkets väderstationer på huvudsidan.</p>

                    <h4>Väderwidget:</h4>
                    <p>På huvudsidan visas en väderwidget som innehåller:</p>
                    <ul>
                        <li><strong>Vindhastighet:</strong> Aktuell vindhastighet i m/s</li>
                        <li><strong>Vindriktning:</strong> Visas både som grader och svenska kompassriktningar (t.ex. "Väst-sydväst")</li>
                        <li><strong>Vindriktningspil:</strong> En pil som visar åt vilket håll vinden blåser</li>
                        <li><strong>Senaste uppdatering:</strong> Tidpunkt för när väderinformationen senast uppdaterades</li>
                        <li><strong>Väderstation:</strong> Namn på den väderstation som data hämtas från</li>
                    </ul>

                    <h4>Automatisk uppdatering:</h4>
                    <p>Väderinformationen uppdateras automatiskt var femte minut utan att du behöver ladda om sidan. Om väderdata inte kan hämtas visas ett felmeddelande istället för väderwidgeten.</p>

                    <h4>Datakälla:</h4>
                    <p>Väderdata hämtas från Trafikverkets öppna API som tillhandahåller realtidsdata från vägväderstationer över hela Sverige. Detta ger tillförlitlig och aktuell väderinformation som är särskilt användbar för seglingsaktiviteter.</p>

                    <h4>Konfiguration:</h4>
                    <p>Du kan konfigurera väderintegrationen i inställningarna genom att ange:</p>
                    <ul>
                        <li><strong>API-nyckel:</strong> Trafikverkets API-nyckel</li>
                        <li><strong>Väderstation:</strong> Vilken väderstation som ska användas för att hämta data</li>
                    </ul>

                    <p><a href="/settings#weather" class="btn btn-primary btn-sm">
                        <i class="bi bi-gear"></i> Konfigurera väderinställningar
                    </a></p>
                </div>
            </div>

            <div class="card mb-4" id="settings">
                <div class="card-header">
                    <h2>Inställningar</h2>
                </div>
                <div class="card-body">
                    <p>Under menyn "Inställningar" kan du konfigurera applikationens beteende.</p>
                    <h4>Databas:</h4>
                    <ul>
                        <li><strong>Automatisk säkerhetskopiering:</strong> När denna inställning är aktiverad skapas automatiskt en säkerhetskopia av databasen varje gång en ny tävling skapas.</li>
                    </ul>

                    <h4>Säkerhetskopior:</h4>
                    <p>Säkerhetskopior sparas i mappen "backups" i samma katalog som databasen. Varje säkerhetskopia namnges med datum och tid för när den skapades, t.ex. "segling_20230515_123045.db".</p>
                    <p>Säkerhetskopior är användbara om du behöver återställa databasen till ett tidigare tillstånd, t.ex. om data har gått förlorad eller om du har gjort ändringar som du vill ångra.</p>

                    <h4>Seglare:</h4>
                    <ul>
                        <li><strong>Standard klubb:</strong> Ange standardvärdet för klubb som används när nya seglare skapas. Standardvärdet är "LSS".</li>
                    </ul>

                    <h4>Visning:</h4>
                    <ul>
                        <li><strong>Tidsformat:</strong> Välj mellan 24-timmarsformat (t.ex. 14:30) och 12-timmarsformat (t.ex. 2:30 PM) för visning av tider i applikationen.</li>
                    </ul>

                    <h4>Tävlingstyper:</h4>
                    <ul>
                        <li><strong>Tillgängliga tävlingstyper:</strong> Hantera listan över tillgängliga tävlingstyper som kan väljas när en ny tävling skapas. Standardvärdena är "Kvällssegling" och "Regatta".</li>
                        <li><strong>Standard tävlingstyp:</strong> Välj vilken tävlingstyp som ska vara förvald när en ny tävling skapas.</li>
                    </ul>
                    <p>Tävlingstyper används för att kategorisera tävlingar och organisera publicerade resultat på GitHub Pages. När en ny tävling skapas används den valda tävlingstypen tillsammans med datumet för att automatiskt generera ett förslag på tävlingsnamn.</p>

                    <h4>Väderinställningar:</h4>
                    <p>Konfigurera väderintegrationen för att visa aktuell vind- och väderinformation på huvudsidan:</p>
                    <ul>
                        <li><strong>Trafikverkets API-nyckel:</strong> API-nyckel för Trafikverkets väder-API. Standard-nyckeln fungerar för de flesta användare, men du kan ange din egen nyckel om det behövs.</li>
                        <li><strong>Väderstation:</strong> Namn på väderstation enligt Trafikverkets API (t.ex. "Linköping", "Stockholm", "Göteborg"). Standardvärdet är "Linköping".</li>
                    </ul>
                    <p>Väderinformationen visas som en widget på huvudsidan och uppdateras automatiskt var femte minut. Widgeten visar vindhastighet, vindriktning (både som grader och svenska kompassriktningar) samt senaste uppdateringstid.</p>
                    <p><a href="https://www.trafikverket.se/trafikinformation/vag/?map_x=650778.00005&map_y=7200000&map_z=2&map_l=100000001000000" target="_blank" class="btn btn-info btn-sm">
                        <i class="bi bi-map"></i> Se karta med väderstationer
                    </a></p>

                    <h4>GitHub Pages-konfiguration:</h4>
                    <p>För att kunna publicera tävlingsresultat till GitHub Pages behöver du konfigurera följande inställningar:</p>
                    <ul>
                        <li><strong>GitHub-användarnamn:</strong> Ditt användarnamn på GitHub</li>
                        <li><strong>GitHub-token:</strong> En personlig åtkomsttoken från GitHub med behörighet att skapa och uppdatera repositories</li>
                        <li><strong>Repository-namn:</strong> Namnet på det repository där resultaten ska publiceras (vanligtvis "sailapp")</li>
                    </ul>
                    <p>För att skapa en personlig åtkomsttoken på GitHub:</p>
                    <ol>
                        <li>Logga in på ditt GitHub-konto</li>
                        <li>Gå till Inställningar > Utvecklarinställningar > Personliga åtkomsttoken</li>
                        <li>Klicka på "Generera ny token"</li>
                        <li>Ge token en beskrivning, t.ex. "Segling GitHub Pages"</li>
                        <li>Välj behörigheter: minst "repo" (full kontroll över privata repositories)</li>
                        <li>Klicka på "Generera token"</li>
                        <li>Kopiera den genererade token och klistra in den i fältet "GitHub-token" i Seglings inställningar</li>
                    </ol>
                    <p>När dessa inställningar är konfigurerade kan du publicera tävlingsresultat till GitHub Pages från resultatsidan för låsta tävlingar.</p>
                </div>
            </div>
        </div>
    </div>
</div>

{{ template "footer.html" . }}
