{{ template "header.html" . }}

<div class="container mt-4">
    <h1>{{ .title }}</h1>

    {{ if .import_success }}
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        Importerade {{ .count }} poster från CSV-filen.
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    {{ end }}

    {{ if .import_error }}
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        {{ .message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    {{ end }}

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>SRS Data Status</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <p class="mb-0"><strong>SRS tabell:</strong> {{ .boatTypeCount }}</p>
                        <a href="/srs/boat-types" class="btn btn-sm btn-primary">Visa</a>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <p class="mb-0"><strong>Mätbrev:</strong> {{ .matbrevCount }}</p>
                        <a href="/srs/matbrev" class="btn btn-sm btn-primary">Visa</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Uppdatera SRS Data från https://matbrev.svensksegling.se/</h5>
                </div>
                <div class="card-body">
                    <form action="/srs/sync" method="post">
                        <div class="form-group mb-3">
                            <label for="source">Välj data att uppdatera:</label>
                            <select class="form-control" id="source" name="source">
                                <option value="all">Alla data</option>
                                <option value="srs_tabell">SRS tabell (BoatList)</option>
                                <option value="matbrev">Mätbrev (ApprovedList)</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">Uppdatera</button>
                    </form>

                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>Exportera/Importera SRS Data</h5>
                </div>
                <div class="card-body">
                    <div class="form-group mb-3">
                        <label for="data_type">Välj datatyp:</label>
                        <select class="form-control" id="data_type" name="data_type">
                            <option value="srs_tabell">SRS Tabell</option>
                            <option value="matbrev">Mätbrev</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <a href="#" id="export_btn" class="btn btn-primary">Exportera CSV</a>
                    </div>

                    <hr>

                    <form action="/srs/data/import" method="post" enctype="multipart/form-data" id="import_form">
                        <input type="hidden" id="import_type" name="import_type" value="srs_tabell">
                        <div class="form-group mb-3">
                            <label for="file">Importera från CSV:</label>
                            <input type="file" class="form-control" id="file" name="file" accept=".csv">
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="clear_existing" name="clear_existing">
                            <label class="form-check-label" for="clear_existing">
                                Rensa befintliga data före import
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary">Importera</button>
                    </form>

                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            const dataTypeSelect = document.getElementById('data_type');
                            const exportBtn = document.getElementById('export_btn');
                            const importForm = document.getElementById('import_form');
                            const importTypeInput = document.getElementById('import_type');

                            // Update export button URL and import form action based on selected data type
                            function updateActions() {
                                const dataType = dataTypeSelect.value;

                                // Update export button URL
                                if (dataType === 'srs_tabell') {
                                    exportBtn.href = '/srs/boat-types/export';
                                } else {
                                    exportBtn.href = '/srs/matbrev/export';
                                }

                                // Update import type hidden field
                                importTypeInput.value = dataType;
                            }

                            // Initial setup
                            updateActions();

                            // Update when selection changes
                            dataTypeSelect.addEventListener('change', updateActions);
                        });
                    </script>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5>Uppdateringslogg</h5>
        </div>
        <div class="card-body">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Datum</th>
                        <th>Källa</th>
                        <th>Status</th>
                        <th>Meddelande</th>
                    </tr>
                </thead>
                <tbody>
                    {{ range .logs }}
                    <tr>
                        <td>{{ .CreatedAt.Format "2006-01-02 15:04:05" }}</td>
                        <td>
                            {{ if eq .Source "approved_boats" }}
                            Mätbrev
                            {{ else if eq .Source "boat_types" }}
                            SRS tabell
                            {{ else }}
                            {{ .Source }}
                            {{ end }}
                        </td>
                        <td>
                            {{ if eq .Status "success" }}
                            <span class="badge bg-success">Lyckades</span>
                            {{ else }}
                            <span class="badge bg-danger">Misslyckades</span>
                            {{ end }}
                        </td>
                        <td>{{ .Message }}</td>
                    </tr>
                    {{ else }}
                    <tr>
                        <td colspan="4">Inga loggar hittades</td>
                    </tr>
                    {{ end }}
                </tbody>
            </table>
        </div>
    </div>
</div>

{{ template "footer.html" . }}
