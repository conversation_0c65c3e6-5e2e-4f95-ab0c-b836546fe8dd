{{ template "header.html" . }}

<div class="container mt-4">
    <h1>{{ .title }}</h1>

    <div class="card">
        <div class="card-header">
            <h2>{{ if .boat }}<PERSON><PERSON><PERSON> båt{{ else }}N<PERSON> b<PERSON>t{{ end }}</h2>
        </div>
        <div class="card-body">
            <form action="/boats" method="POST" autocomplete="off">
                <input type="hidden" name="id" value="{{ if .boat }}{{ .boat.ID }}{{ end }}">

                <div class="mb-3">
                    <label for="namn" class="form-label">Båtnamn</label>
                    <input type="text" class="form-control" id="namn" name="namn" value="{{ if .boat }}{{ .boat.Namn }}{{ end }}" required>
                    <small id="namn-help" class="form-text text-muted">{{ if .boat.MatbrevsNummer }}B<PERSON>tnamn hämtad från mätbrev{{ end }}</small>
                </div>

                <div class="mb-3">
                    <label for="matbrevs_nummer" class="form-label">Mätbrevsnummer</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="matbrevs_nummer" name="matbrevs_nummer" value="{{ if .boat }}{{ .boat.MatbrevsNummer }}{{ end }}"
                            autocomplete="off"
                            autocomplete="new-password"
                            autocomplete="chrome-off"
                            hx-get="/api/matbrev/search"
                            hx-trigger="keyup changed delay:500ms"
                            hx-target="#matbrevs-search-results"
                            hx-indicator="#srs-loading">
                        <div id="srs-loading" style="display: none;">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">Laddar...</span>
                            </div>
                        </div>
                    </div>
                    <small id="matbrevs-search-help" class="form-text text-muted">Sök på mätbrevsnummer, båttyp, båtnamn, ägare eller segelnummer. Börja skriva för att se förslag.</small>
                    <div id="matbrevs-search-results" class="dropdown-menu"></div>
                </div>

                <div class="mb-3">
                    <label for="battyp" class="form-label">Båttyp</label>
                    <div class="input-group">
                        <select class="form-select" id="battyp" name="battyp" required
                            onchange="handleBattypChange(this.value)"
                            {{ if .boat.MatbrevsNummer }}disabled{{ end }}>
                            <option value="">Välj båttyp</option>
                            {{ range .boatTypes }}
                            <option value="{{ . }}" {{ if $.boat }}{{ if eq $.boat.Battyp . }}selected{{ end }}{{ end }}>{{ . }}</option>
                            {{ end }}
                        </select>
                        <!-- Always include the hidden input for båttyp to ensure it's submitted correctly -->
                        <input type="hidden" name="battyp" value="{{ if .boat }}{{ .boat.Battyp }}{{ end }}" id="hidden-battyp">
                        <button class="btn btn-outline-primary" type="button" id="refresh-srs"
                            onclick="fetchSRSDataByBoatType(document.getElementById('battyp').value)">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                        {{ if .boat.MatbrevsNummer }}
                        <button class="btn btn-outline-secondary" type="button" id="enable-battyp"
                            onclick="enableBattypSelection()">
                            <i class="bi bi-pencil"></i> Välj båttyp
                        </button>
                        {{ end }}
                    </div>
                    <small id="battyp-help" class="form-text text-muted">{{ if .boat.MatbrevsNummer }}Båttyp hämtad från mätbrev{{ end }}</small>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="segelnummer" class="form-label">Segelnummer</label>
                            <input type="text" class="form-control" id="segelnummer" name="segelnummer" value="{{ if .boat }}{{ .boat.Segelnummer }}{{ end }}">
                            <small id="segelnummer-help" class="form-text text-muted">{{ if .boat.MatbrevsNummer }}Segelnummer hämtad från mätbrev{{ end }}</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="nationality" class="form-label">Nationalitet</label>
                            <input type="text" class="form-control" id="nationality" name="nationality" value="{{ if .boat }}{{ .boat.Nationality }}{{ else }}SWE{{ end }}">
                            <small id="nationality-help" class="form-text text-muted">{{ if .boat.MatbrevsNummer }}Nationalitet hämtad från mätbrev{{ end }}</small>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">SRS-värden</label>
                    <div id="srs-data-container">
                        <div id="srs-data">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="srs" class="form-label">SRS</label>
                                        <div class="input-group">
                                            <input type="number" step="0.001" class="form-control" id="srs" name="srs" value="{{ if .boat }}{{ .boat.SRS }}{{ end }}">
                                            <span class="input-group-text" id="srs-display"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="srs_utan_undanvindsegel" class="form-label">SRS utan undanvindsegel</label>
                                        <div class="input-group">
                                            <input type="number" step="0.001" class="form-control" id="srs_utan_undanvindsegel" name="srs_utan_undanvindsegel" value="{{ if .boat }}{{ .boat.SRSUtanUndanvindsegel }}{{ end }}">
                                            <span class="input-group-text" id="srs-uu-display"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="srs_shorthanded" class="form-label">SRS Shorthanded</label>
                                        <div class="input-group">
                                            <input type="number" step="0.001" class="form-control" id="srs_shorthanded" name="srs_shorthanded" value="{{ if .boat }}{{ .boat.SRSShorthanded }}{{ end }}">
                                            <span class="input-group-text" id="srs-sh-display"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="srs_shorthanded_utan_undanvindsegel" class="form-label">SRS Shorthanded utan undanvindsegel</label>
                                        <div class="input-group">
                                            <input type="number" step="0.001" class="form-control" id="srs_shorthanded_utan_undanvindsegel" name="srs_shorthanded_utan_undanvindsegel" value="{{ if .boat }}{{ .boat.SRSShorthandedUtanUndanvindsegel }}{{ end }}">
                                            <span class="input-group-text" id="srs-sh-uu-display"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="srs-data-source" class="alert alert-info mt-2" style="display: none;">
                                <small>Data hämtad från svensksegling.se</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <button type="submit" class="btn btn-primary" id="save-boat-btn">Spara</button>
                    <a href="/boats" class="btn btn-outline-secondary">Avbryt</a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Function to handle båttyp change
    function handleBattypChange(battyp) {
        console.log('Båttyp changed to:', battyp);

        // Check if there's a mätbrevsnummer value
        const matbrevsNummerInput = document.getElementById('matbrevs_nummer');
        if (matbrevsNummerInput && matbrevsNummerInput.value) {
            console.log('Clearing mätbrevsnummer field');
            matbrevsNummerInput.value = '';

            // Clear any help texts that indicate data was fetched from mätbrev
            const namnHelp = document.getElementById('namn-help');
            if (namnHelp) {
                namnHelp.textContent = '';
            }

            const segelnummerHelp = document.getElementById('segelnummer-help');
            if (segelnummerHelp) {
                segelnummerHelp.textContent = '';
            }

            const nationalityHelp = document.getElementById('nationality-help');
            if (nationalityHelp) {
                nationalityHelp.textContent = '';
            }
        }

        // Call the existing function to fetch SRS data
        fetchSRSDataByBoatType(battyp);
    }

    // Function to fetch SRS data by boat type
    function fetchSRSDataByBoatType(battyp) {
        console.log('Fetching SRS data for boat type:', battyp);

        if (!battyp) {
            console.log('No boat type selected');
            return;
        }

        // Show loading indicator
        const loadingIndicator = document.getElementById('srs-loading');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'inline-block';
        }

        // Fetch data from API
        fetch(`/api/srs?battyp=${encodeURIComponent(battyp)}`)
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.text().then(text => {
                    console.log('Raw response text:', text);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('Error parsing JSON:', e);
                        throw new Error('Invalid JSON response');
                    }
                });
            })
            .then(data => {
                console.log('Received SRS data for boat type:', data);

                // Check if we have the expected data structure
                if (!data || typeof data !== 'object') {
                    throw new Error('Invalid data format received');
                }

                // Update the SRS fields
                const srsField = document.getElementById('srs');
                const srsUuField = document.getElementById('srs_utan_undanvindsegel');
                const srsShField = document.getElementById('srs_shorthanded');
                const srsShUuField = document.getElementById('srs_shorthanded_utan_undanvindsegel');

                console.log('SRS data fields:',
                    'srs:', data.srs,
                    'srs_utan_undanvindsegel:', data.srs_utan_undanvindsegel,
                    'srs_shorthanded:', data.srs_shorthanded,
                    'srs_shorthanded_utan_undanvindsegel:', data.srs_shorthanded_utan_undanvindsegel
                );

                if (srsField) srsField.value = data.srs || '';
                if (srsUuField) srsUuField.value = data.srs_utan_undanvindsegel || '';
                if (srsShField) srsShField.value = data.srs_shorthanded || '';
                if (srsShUuField) srsShUuField.value = data.srs_shorthanded_utan_undanvindsegel || '';

                // Show the data source info
                const dataSource = document.getElementById('srs-data-source');
                if (dataSource) {
                    dataSource.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error fetching SRS data:', error);
                alert('Ett fel uppstod vid hämtning av data: ' + error.message);
            })
            .finally(() => {
                // Hide loading indicator
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'none';
                }
            });
    }

    // Function to fetch mätbrev data from the local database
    function fetchMatbrevData(matbrevsNummer) {
        console.log('Fetching mätbrev data for:', matbrevsNummer);

        if (!matbrevsNummer) {
            console.log('No mätbrevsnummer provided');
            return;
        }

        // Normalize the mätbrevsnummer: trim whitespace and convert to uppercase
        matbrevsNummer = matbrevsNummer.trim().toUpperCase();

        // Validate the format: B or E followed by 4 digits
        const validFormat = /^[BE]\d{4}$/.test(matbrevsNummer);
        if (!validFormat) {
            console.log('Invalid mätbrevsnummer format:', matbrevsNummer);
            return;
        }

        // Show loading indicator
        const loadingIndicator = document.getElementById('srs-loading');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'inline-block';
        }

        // Fetch data from API
        fetch(`/api/matbrev?matbrevs_nummer=${encodeURIComponent(matbrevsNummer)}`)
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.text().then(text => {
                    console.log('Raw response text:', text);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('Error parsing JSON:', e);
                        throw new Error('Invalid JSON response');
                    }
                });
            })
            .then(data => {
                console.log('Received mätbrev data:', data);

                // Check if we have the expected data structure
                if (!data || typeof data !== 'object') {
                    throw new Error('Invalid data format received');
                }

                // Update the form fields with the data
                const namnField = document.getElementById('namn');
                const battypField = document.getElementById('battyp');
                const hiddenBattypField = document.getElementById('hidden-battyp');
                const segelnummerField = document.getElementById('segelnummer');
                const nationalityField = document.getElementById('nationality');
                const srsField = document.getElementById('srs');
                const srsUuField = document.getElementById('srs_utan_undanvindsegel');
                const srsShField = document.getElementById('srs_shorthanded');
                const srsShUuField = document.getElementById('srs_shorthanded_utan_undanvindsegel');

                // Log the data structure for debugging
                console.log('Data structure received:', JSON.stringify(data, null, 2));

                // Normalize property names (API might return different formats)
                const normalizedData = {
                    matbrevsNummer: data.matbrevs_nummer || data.MatbrevsNummer || '',
                    battyp: data.battyp || data.Battyp || '',
                    batNamn: data.bat_namn || data.BatNamn || '',
                    agare: data.agare || data.Agare || '',
                    segelnummer: data.segelnummer || data.Segelnummer || '',
                    nationality: data.nationality || data.Nationality || 'SWE',
                    srs: data.srs || data.SRS || 0,
                    srsUtanUndanvindsegel: data.srs_utan_undanvindsegel || data.SRSUtanUndanvindsegel || 0,
                    srsShorthanded: data.srs_shorthanded || data.SRSShorthanded || 0,
                    srsShorthandedUtanUndanvindsegel: data.srs_shorthanded_utan_undanvindsegel || data.SRSShorthandedUtanUndanvindsegel || 0
                };

                console.log('Normalized data:', normalizedData);

                // Update the fields if they exist
                if (namnField) {
                    // Combine boat type and owner for the name if there's no boat name
                    if (normalizedData.batNamn) {
                        namnField.value = normalizedData.batNamn;
                    } else {
                        namnField.value = `${normalizedData.battyp} (${normalizedData.agare})`;
                    }

                    // Update the help text
                    const namnHelp = document.getElementById('namn-help');
                    if (namnHelp) {
                        namnHelp.textContent = 'Båtnamn hämtad från mätbrev';
                    }
                }

                if (battypField) {
                    // Disable the båttyp dropdown and set the hidden field
                    battypField.disabled = true;

                    // Try to find and select the matching option in the dropdown
                    const options = battypField.options;
                    let optionFound = false;

                    for (let i = 0; i < options.length; i++) {
                        if (options[i].value === normalizedData.battyp) {
                            battypField.selectedIndex = i;
                            optionFound = true;
                            console.log('Found matching båttyp option at index', i);
                            break;
                        }
                    }

                    // If no matching option was found, add a new option
                    if (!optionFound && normalizedData.battyp) {
                        console.log('No matching båttyp option found, adding new option:', normalizedData.battyp);
                        const newOption = document.createElement('option');
                        newOption.value = normalizedData.battyp;
                        newOption.text = normalizedData.battyp;
                        newOption.selected = true;
                        battypField.add(newOption);
                    }

                    // Update the help text
                    const battypHelp = document.getElementById('battyp-help');
                    if (battypHelp) {
                        battypHelp.textContent = 'Båttyp hämtad från mätbrev';
                    }
                }

                if (hiddenBattypField) {
                    hiddenBattypField.value = normalizedData.battyp;
                    console.log('Updated hidden båttyp field to:', normalizedData.battyp);
                }

                if (segelnummerField) {
                    segelnummerField.value = normalizedData.segelnummer || '';

                    // Update the help text
                    const segelnummerHelp = document.getElementById('segelnummer-help');
                    if (segelnummerHelp) {
                        segelnummerHelp.textContent = 'Segelnummer hämtad från mätbrev';
                    }
                }

                if (nationalityField) {
                    nationalityField.value = normalizedData.nationality || 'SWE';

                    // Update the help text
                    const nationalityHelp = document.getElementById('nationality-help');
                    if (nationalityHelp) {
                        nationalityHelp.textContent = 'Nationalitet hämtad från mätbrev';
                    }
                }

                if (srsField) srsField.value = normalizedData.srs || '';
                if (srsUuField) srsUuField.value = normalizedData.srsUtanUndanvindsegel || '';
                if (srsShField) srsShField.value = normalizedData.srsShorthanded || '';
                if (srsShUuField) srsShUuField.value = normalizedData.srsShorthandedUtanUndanvindsegel || '';

                // Show the data source info
                const dataSource = document.getElementById('srs-data-source');
                if (dataSource) {
                    dataSource.style.display = 'block';
                    dataSource.innerHTML = `<small>Data hämtad från mätbrev: ${normalizedData.matbrevsNummer}</small>`;
                }
            })
            .catch(error => {
                console.error('Error fetching mätbrev data:', error);
                alert('Ett fel uppstod vid hämtning av mätbrevsdata: ' + error.message);
            })
            .finally(() => {
                // Hide loading indicator
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'none';
                }
            });
    }

    document.addEventListener('htmx:beforeSwap', function(event) {
        // Prevent the default swap behavior for SRS data
        if (event.detail.target.id === 'srs-data-container') {
            event.detail.shouldSwap = false;
        }
    });

    // Handle matbrevs search results
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'matbrevs-search-results') {
            const results = event.detail.target;

            // Show the dropdown if there are results
            if (results.innerHTML.trim() !== '') {
                results.classList.add('show');

                // Add click handlers to the results
                const items = results.querySelectorAll('.dropdown-item');
                items.forEach(item => {
                    item.addEventListener('click', function() {
                        // Get just the mätbrevsnummer from the data attribute
                        const matbrevsnummer = this.dataset.matbrev;
                        console.log('Selected mätbrevsnummer:', matbrevsnummer);

                        // Set the input field value
                        document.getElementById('matbrevs_nummer').value = matbrevsnummer;
                        results.classList.remove('show');

                        // Fetch data from the local database
                        fetchMatbrevData(matbrevsnummer);

                        // Show the "Välj båttyp" button if it's not already visible
                        const enableBattypButton = document.getElementById('enable-battyp');
                        if (enableBattypButton) {
                            enableBattypButton.style.display = 'inline-block';
                        } else {
                            // If the button doesn't exist yet (for new boats), create it
                            const refreshButton = document.getElementById('refresh-srs');
                            if (refreshButton && refreshButton.parentNode) {
                                const newButton = document.createElement('button');
                                newButton.id = 'enable-battyp';
                                newButton.className = 'btn btn-outline-secondary';
                                newButton.type = 'button';
                                newButton.onclick = enableBattypSelection;
                                newButton.innerHTML = '<i class="bi bi-pencil"></i> Välj båttyp';
                                refreshButton.parentNode.appendChild(newButton);
                            }
                        }
                    });
                });
            } else {
                results.classList.remove('show');
            }
        }
    });

    // Close the dropdown when clicking outside
    document.addEventListener('click', function(event) {
        const results = document.getElementById('matbrevs-search-results');
        if (results && !results.contains(event.target) && event.target.id !== 'matbrevs_nummer') {
            results.classList.remove('show');
        }
    });

    // Function to enable båttyp selection and clear mätbrevsnummer
    function enableBattypSelection() {
        // Enable the båttyp dropdown
        const battypSelect = document.getElementById('battyp');
        if (battypSelect) {
            battypSelect.disabled = false;
        }

        // Clear the mätbrevsnummer field
        const matbrevsNummerInput = document.getElementById('matbrevs_nummer');
        if (matbrevsNummerInput) {
            matbrevsNummerInput.value = '';
        }

        // Clear the help text
        const battypHelp = document.getElementById('battyp-help');
        if (battypHelp) {
            battypHelp.textContent = '';
        }

        // Update the hidden båttyp field with the current selection
        const hiddenBattyp = document.getElementById('hidden-battyp');
        if (hiddenBattyp && battypSelect) {
            hiddenBattyp.value = battypSelect.value;
        }

        // Hide the "Välj båttyp" button since it's no longer needed
        const enableBattypButton = document.getElementById('enable-battyp');
        if (enableBattypButton) {
            enableBattypButton.style.display = 'none';
        }

        // Clear any help texts that indicate data was fetched from mätbrev
        const namnHelp = document.getElementById('namn-help');
        if (namnHelp) {
            namnHelp.textContent = '';
        }

        const segelnummerHelp = document.getElementById('segelnummer-help');
        if (segelnummerHelp) {
            segelnummerHelp.textContent = '';
        }

        const nationalityHelp = document.getElementById('nationality-help');
        if (nationalityHelp) {
            nationalityHelp.textContent = '';
        }

        // Focus on the båttyp dropdown
        if (battypSelect) {
            battypSelect.focus();
        }
    }

    // Ensure the form submits with the correct båttyp
    document.addEventListener('DOMContentLoaded', function() {
        // Add event listener for the mätbrevsnummer input field
        const matbrevsNummerInput = document.getElementById('matbrevs_nummer');
        if (matbrevsNummerInput) {
            matbrevsNummerInput.addEventListener('blur', function() {
                let value = this.value.trim();

                // Check if the value contains a dash followed by text (like "B1234 - Boat Name")
                // If so, extract just the mätbrevsnummer part
                if (value.includes(' - ')) {
                    value = value.split(' - ')[0].trim();
                    this.value = value; // Update the input field with just the mätbrevsnummer
                }

                // Validate the format: B or E followed by 4 digits
                const validFormat = /^[BE]\d{4}$/i.test(value);

                if (value && validFormat) {
                    // Normalize to uppercase
                    value = value.toUpperCase();
                    this.value = value; // Update the input field with normalized value
                    fetchMatbrevData(value);
                }
            });
        }

        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function() {
                const battypSelect = document.getElementById('battyp');
                const hiddenBattyp = document.getElementById('hidden-battyp');
                const namnInput = document.getElementById('namn');

                // If the select is not disabled, update the hidden field
                if (!battypSelect.disabled && battypSelect.value) {
                    hiddenBattyp.value = battypSelect.value;
                }

                // Log form data for debugging
                console.log('Form submission:');
                console.log('Namn:', namnInput.value);
                console.log('Båttyp (select):', battypSelect.value);
                console.log('Båttyp (hidden):', hiddenBattyp ? hiddenBattyp.value : 'none');

                // Add a visual indicator that the form is being submitted with the correct båttyp
                if (hiddenBattyp && hiddenBattyp.value) {
                    const battypHelp = document.getElementById('battyp-help');
                    if (battypHelp) {
                        battypHelp.innerHTML = 'Sparar båttyp: <strong>' + hiddenBattyp.value + '</strong>';
                    }
                }
            });
        }
    });
</script>

{{ template "footer.html" . }}
