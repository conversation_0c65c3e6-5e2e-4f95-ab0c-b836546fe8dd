{{ template "header.html" . }}

    <div class="container mt-4">
        <!-- Weather Widget -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0" id="weather-header">
                            <i class="bi bi-wind"></i> Väder - Laddar...
                        </h5>
                        <small class="text-muted" id="weather-last-updated">Laddar...</small>
                    </div>
                    <div class="card-body">
                        <div id="weather-data" class="d-flex align-items-center justify-content-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Laddar väderdata...</span>
                            </div>
                        </div>
                        <div id="weather-error" class="alert alert-warning d-none" role="alert">
                            <i class="bi bi-exclamation-triangle"></i> Kunde inte hämta väderdata
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3>Tävlingar</h3>
                        <div class="filters d-flex gap-2">
                            <form action="/" method="get" id="filter-form" class="d-flex align-items-center bg-light p-2 rounded">
                                <div class="d-flex align-items-center me-3">
                                    <label for="year" class="me-2 fw-bold">År:</label>
                                    <select name="year" id="year" class="form-select form-select-sm" onchange="document.getElementById('filter-form').submit()">
                                        <option value="0" {{ if eq .selectedYear 0 }}selected{{ end }}>Alla år</option>
                                        {{ range .years }}
                                        <option value="{{ . }}" {{ if eq . $.selectedYear }}selected{{ end }}>{{ . }}</option>
                                        {{ end }}
                                    </select>
                                </div>
                                <div class="d-flex align-items-center">
                                    <label for="type" class="me-2 fw-bold">Typ:</label>
                                    <select name="type" id="type" class="form-select form-select-sm" onchange="document.getElementById('filter-form').submit()">
                                        <option value="" {{ if eq .selectedType "" }}selected{{ end }}>Alla typer</option>
                                        {{ range .competitionTypesList }}
                                        <option value="{{ . }}" {{ if eq . $.selectedType }}selected{{ end }}>{{ . }}</option>
                                        {{ end }}
                                    </select>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="card-body">
                        {{ if .events }}
                        <div class="events-list-container" style="max-height: 650px; overflow-y: auto; margin-bottom: 15px;">
                            <ul class="list-group">
                                {{ range .events }}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center" style="flex: 1;">
                                        <div class="me-3" style="min-width: 100px;">
                                            <span class="text-muted">{{ .Datum.Format "2006-01-02" }}</span>
                                        </div>
                                        <div class="me-3" style="flex: 2;">
                                            <a href="/events/{{ .ID }}" class="fw-bold">{{ .Namn }}</a>
                                        </div>
                                        {{ if .Tavlingstyp }}
                                        <div class="me-3" style="min-width: 120px;">
                                            <span class="badge bg-secondary">{{ .Tavlingstyp }}</span>
                                        </div>
                                        {{ end }}
                                        <div class="me-3">
                                            <span class="badge bg-secondary" title="Antal deltagare">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                    <path d="M0 13.5C0 12.1 1.1 11 2.5 11h11c1.4 0 2.5 1.1 2.5 2.5S14.9 16 13.5 16h-11C1.1 16 0 14.9 0 13.5z"/>
                                                    <path d="M2 9l1-1h10l1 1"/>
                                                    <path d="M13 7l-1-5H8L4 7"/>
                                                    <path d="M8 2V1"/>
                                                </svg>
                                                {{ .BoatCount }}
                                            </span>
                                        </div>
                                        <div class="me-3">
                                            <span class="badge bg-info" title="Antal deltagare">
                                                <i class="bi bi-people-fill"></i> {{ .ParticipantCount }}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <!-- TEMPORARILY DISABLED: Lock status icons -->
                                        <!-- {{ if .Locked }}
                                        <span class="badge bg-danger" title="Låst tävling">
                                            <i class="bi bi-lock-fill"></i>
                                        </span>
                                        {{ else }}
                                        <span class="badge bg-success" title="Öppen tävling">
                                            <i class="bi bi-unlock-fill"></i>
                                        </span>
                                        {{ end }} -->
                                    </div>
                                </li>
                                {{ end }}
                                <!-- Summary row at the bottom -->
                                <li class="list-group-item d-flex justify-content-between align-items-center bg-light">
                                    <div class="d-flex align-items-center" style="flex: 1;">
                                        <div class="me-3" style="min-width: 100px;">
                                            <span class="fw-bold">Totalt</span>
                                        </div>
                                        <div class="me-3" style="flex: 2;">
                                            <span class="fw-bold">{{ len .events }} tävlingar</span>
                                        </div>
                                        <div class="me-3" style="min-width: 120px;">
                                            <!-- Empty space to align with competition type -->
                                        </div>
                                        <div class="me-3">
                                            <span class="badge bg-dark" title="Totalt antal deltagare">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                    <path d="M0 13.5C0 12.1 1.1 11 2.5 11h11c1.4 0 2.5 1.1 2.5 2.5S14.9 16 13.5 16h-11C1.1 16 0 14.9 0 13.5z"/>
                                                    <path d="M2 9l1-1h10l1 1"/>
                                                    <path d="M13 7l-1-5H8L4 7"/>
                                                    <path d="M8 2V1"/>
                                                </svg>
                                                {{ .totalBoats }}
                                            </span>
                                        </div>
                                        <div class="me-3">
                                            <span class="badge bg-dark" title="Totalt antal deltagare">
                                                <i class="bi bi-people-fill"></i> {{ .totalParticipants }}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <!-- Empty space to align with lock icon -->
                                    </div>
                                </li>
                            </ul>
                        </div>
                        {{ else }}
                        <p>Inga tävlingar hittades.</p>
                        {{ end }}
                        <div class="mt-3">
                            <a href="/events/new" class="btn btn-primary">Skapa ny tävling</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

{{ template "footer.html" . }}

<script>
// Weather widget functionality
function loadWeatherData() {
    fetch('/api/weather')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showWeatherError();
                return;
            }

            // Format the weather data
            const windSpeed = data.wind_speed.toFixed(1);
            const windSpeedMax = data.wind_speed_max.toFixed(1);
            const windSpeedAverage = data.wind_speed_average.toFixed(1);
            const temperature = data.temperature.toFixed(1);
            const windDirection = data.wind_direction;
            const windDirectionText = data.wind_direction_text;
            const windDirectionName = data.wind_direction_name;
            const lastUpdated = new Date(data.last_updated);



            // Create weather display
            const weatherHtml = `
                <div class="row text-center">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="bi bi-thermometer-half fs-2 text-danger me-3"></i>
                            <div>
                                <div class="fs-4 fw-bold">${temperature}°C</div>
                                <div class="text-muted">Temperatur</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="bi bi-wind fs-2 text-primary me-3"></i>
                            <div>
                                <div class="fs-4 fw-bold">${windSpeed} m/s</div>
                                <div class="text-muted">Vind nu</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="bi bi-speedometer2 fs-2 text-warning me-3"></i>
                            <div>
                                <div class="fs-5 fw-bold">${windSpeedAverage} / ${windSpeedMax} m/s</div>
                                <div class="text-muted">Medel / Max (10min)</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="wind-direction-indicator me-3" style="transform: rotate(${windDirection + 180}deg);">
                                <i class="bi bi-arrow-up fs-2 text-info"></i>
                            </div>
                            <div>
                                <div class="fs-5 fw-bold">${windDirectionName}</div>
                                <div class="text-muted">${windDirectionText} (${windDirection}°)</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('weather-data').innerHTML = weatherHtml;
            document.getElementById('weather-header').innerHTML =
                '<i class="bi bi-wind"></i> Väder - ' + data.location;
            // Format the timestamp using browser's actual system timezone
            const utcDate = new Date(data.last_updated);

            // Get the browser's reported timezone
            const reportedTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

            // Check if timezone detection is broken (reports UTC but system shows offset)
            const testDate = new Date();
            const isTimezoneDetectionBroken = reportedTimezone === 'UTC' && testDate.toString().includes('GMT+');

            let localTimeString;

            if (isTimezoneDetectionBroken) {
                // Use the browser's default locale conversion (which uses actual system timezone)
                localTimeString = utcDate.toLocaleString('sv-SE', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });

                // Determine timezone name from the actual offset
                const offsetMinutes = testDate.getTimezoneOffset();
                const offsetHours = Math.abs(offsetMinutes / 60);
                let timezoneName;

                if (offsetMinutes === -120) timezoneName = 'CEST';
                else if (offsetMinutes === -60) timezoneName = 'CET';
                else if (offsetMinutes === 0) timezoneName = 'UTC';
                else timezoneName = `UTC${offsetMinutes > 0 ? '-' : '+'}${offsetHours}`;

                localTimeString += ' ' + timezoneName;
            } else {
                // Normal timezone conversion using detected timezone
                localTimeString = utcDate.toLocaleString('sv-SE', {
                    timeZone: reportedTimezone,
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    timeZoneName: 'short'
                });
            }

            document.getElementById('weather-last-updated').textContent =
                'Uppdaterad: ' + localTimeString;
            document.getElementById('weather-error').classList.add('d-none');
        })
        .catch(error => {
            console.error('Error fetching weather data:', error);
            showWeatherError();
        });
}

function showWeatherError() {
    document.getElementById('weather-data').innerHTML = '';
    document.getElementById('weather-header').innerHTML =
        '<i class="bi bi-wind"></i> Väder - Fel';
    document.getElementById('weather-error').classList.remove('d-none');
    document.getElementById('weather-last-updated').textContent = '';
}

// Load weather data when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadWeatherData();

    // Refresh weather data every 5 minutes
    setInterval(loadWeatherData, 5 * 60 * 1000);
});
</script>

<style>
.wind-direction-indicator {
    transition: transform 0.3s ease;
}
</style>
