{{ template "header.html" . }}

<script>
    // Function to refresh the dropdown with the latest events
    function refreshEventDropdown() {
        const selectedEvent = document.getElementById('selected-event');
        if (!selectedEvent) return;

        console.log('Refreshing dropdown from events.html');

        // Get current selection
        const currentSelection = selectedEvent.value;

        // Fetch the latest events from the server
        fetch('/api/events')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch events');
                }
                return response.json();
            })
            .then(events => {
                console.log('Refreshing dropdown with events:', events);

                // Clear all options except the first one (Välj tävling...)
                while (selectedEvent.options.length > 1) {
                    selectedEvent.remove(1);
                }

                // Add the updated events to the dropdown
                events.forEach(event => {
                    const option = document.createElement('option');
                    option.value = event.id.toString();
                    option.text = `${event.namn} (${new Date(event.datum).toISOString().split('T')[0]})`;

                    // If this was the selected event, select it
                    if (currentSelection && currentSelection === event.id.toString()) {
                        option.selected = true;
                    }

                    selectedEvent.add(option);
                });

                // If the current selection is not in the updated list, clear it
                let selectionExists = false;
                for (let i = 0; i < selectedEvent.options.length; i++) {
                    if (selectedEvent.options[i].value === currentSelection) {
                        selectionExists = true;
                        break;
                    }
                }

                if (!selectionExists && currentSelection) {
                    localStorage.removeItem('selectedEventID');
                    selectedEvent.value = '';
                }
            })
            .catch(error => {
                console.error('Error refreshing events dropdown:', error);
            });
    }

    // Refresh the dropdown when the page loads
    document.addEventListener('DOMContentLoaded', function() {
        refreshEventDropdown();

        // Check for any recent participant count updates
        const updatedCountData = localStorage.getItem('updated_participant_count');
        if (updatedCountData) {
            try {
                const data = JSON.parse(updatedCountData);
                // Only use data that's less than 5 seconds old
                if (data && data.timestamp && (Date.now() - data.timestamp < 5000)) {
                    updateParticipantCountInTable(data.eventId, data.count);
                }
            } catch (error) {
                console.error('Error parsing updated_participant_count:', error);
            }
        }

        // Participant count functionality has been removed
    });

    // Participant count functionality has been removed
</script>

<div class="container mt-4">
    <h1>{{ .title }}</h1>

    <div class="row">
        <div class="col-md-3">
            {{ template "event_nav.html" . }}
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2>Tävlingar</h2>
                    <div class="year-filter">
                        <form action="/events" method="get" class="d-flex align-items-center bg-light p-2 rounded">
                            <label for="year" class="me-2 fw-bold">Visa tävlingar för år:</label>
                            <select name="year" id="year" class="form-select form-select-sm" onchange="this.form.submit()">
                                <option value="0" {{ if eq .selectedYear 0 }}selected{{ end }}>Alla år</option>
                                {{ range .years }}
                                <option value="{{ . }}" {{ if eq . $.selectedYear }}selected{{ end }}>{{ . }}</option>
                                {{ end }}
                            </select>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    {{ if .error }}
                    <div class="alert alert-danger">
                        {{ .error }}
                    </div>
                    {{ end }}
                    {{ if .events }}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Namn</th>
                                    <th>Datum</th>
                                    <th>Starttid</th>
                                    <th>Vind (m/s)</th>
                                    <th>Banlängd (nm)</th>
                                    <th>Jaktstart</th>
                                    <th>Status</th>
                                    <th>Åtgärder</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{ range .events }}
                                <tr>
                                    <td>{{ .Namn }}</td>
                                    <td>{{ .Datum.Format "2006-01-02" }}</td>
                                    <td>{{ .Starttid }}</td>
                                    <td>{{ .Vind }}</td>
                                    <td>{{ .Banlangd }}</td>
                                    <td>{{ if .Jaktstart }}Ja{{ else }}Nej{{ end }}</td>
                                    <td>
                                        <!-- TEMPORARILY DISABLED: Lock status badges -->
                                        <!-- {{ if .Locked }}
                                        <span class="badge bg-danger">Låst</span>
                                        {{ else }}
                                        <span class="badge bg-success">Öppen</span>
                                        {{ end }} -->
                                        <span class="badge bg-secondary">Aktiv</span>
                                    </td>
                                    <td class="action-buttons">
                                        <div class="d-flex flex-column gap-1">
                                            <button class="btn btn-sm btn-outline-success w-100"
                                                onclick="selectEvent('{{ .ID }}', '{{ .Jaktstart }}')">
                                                Välj
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger w-100"
                                                hx-delete="/events/{{ .ID }}"
                                                hx-confirm="Är du säker på att du vill ta bort denna tävling?"
                                                hx-target="closest tr"
                                                hx-swap="delete swap:1s"
                                                onclick="window.deletedEventId = '{{ .ID }}'; setTimeout(function() { removeEventFromDropdown('{{ .ID }}'); }, 500);">
                                                Ta bort
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {{ end }}
                            </tbody>
                        </table>
                    </div>
                    {{ else }}
                    <p>Inga tävlingar hittades.</p>
                    {{ end }}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Handle errors in HTMX requests
    document.addEventListener('htmx:responseError', function(event) {
        // Show an error message
        alert('Ett fel uppstod: ' + (event.detail.xhr.responseText || 'Okänt fel'));
    });

    // Handle successful event deletion
    document.addEventListener('htmx:afterSwap', function(event) {
        // Check if this was a delete operation
        if (event.detail.target && event.detail.target.matches('tr') && event.detail.xhr.status === 200) {
            // Get the deleted event ID from the window object or extract it from the URL
            let deletedEventId;

            if (window.deletedEventId) {
                deletedEventId = window.deletedEventId;
                console.log('Using stored deletedEventId:', deletedEventId);
                // Clear the stored ID to avoid issues with future operations
                window.deletedEventId = null;
            } else {
                // Fallback: Extract the event ID from the delete request URL
                const deleteUrl = event.detail.requestConfig.path;
                const match = deleteUrl.match(/\/events\/(\d+)/);

                if (match && match[1]) {
                    deletedEventId = match[1];
                    console.log('Extracted deletedEventId from URL:', deletedEventId);
                }
            }

            if (deletedEventId) {

                // Get the currently selected event ID
                const selectedEventId = localStorage.getItem('selectedEventID');

                // If the deleted event was the selected one, clear the selection
                if (selectedEventId === deletedEventId) {
                    localStorage.removeItem('selectedEventID');

                    // Update the dropdown to show no selected event
                    const selectedEvent = document.getElementById('selected-event');
                    if (selectedEvent) {
                        selectedEvent.value = '';
                    }

                    // Disable all menu items
                    const menuItems = document.querySelectorAll('.list-group-item:not(.disabled)');
                    menuItems.forEach(item => {
                        // Skip the "Create" and "List" items
                        if (item.textContent.includes('Skapa ny tävling') || item.textContent.includes('Tävlingar')) {
                            return;
                        }

                        // Replace links with disabled spans
                        if (item.tagName === 'A' && !item.classList.contains('disabled')) {
                            const icon = item.querySelector('i').outerHTML;
                            const text = item.textContent.trim();

                            const span = document.createElement('span');
                            span.className = 'list-group-item list-group-item-action disabled';
                            span.setAttribute('data-event-id', '0');
                            span.setAttribute('data-href-template', item.getAttribute('href').replace(/\/\d+/, '/%eventId%'));
                            span.innerHTML = icon + ' ' + text;

                            item.parentNode.replaceChild(span, item);
                        }
                    });
                }

                // Refresh the dropdown with the latest events
                console.log('Event deleted, refreshing dropdown');

                // Check if refreshEventDropdown function exists
                if (typeof refreshEventDropdown === 'function') {
                    refreshEventDropdown();
                } else {
                    console.error('refreshEventDropdown function not found');

                    // Fallback: reload the page to get the updated dropdown
                    window.location.reload();
                }
            }
        }
    });

    // Direct function to remove an event from the dropdown
    function removeEventFromDropdown(eventId) {
        console.log('Directly removing event from dropdown:', eventId);
        const selectedEvent = document.getElementById('selected-event');
        if (!selectedEvent) return;

        // Find and remove the option
        for (let i = 0; i < selectedEvent.options.length; i++) {
            if (selectedEvent.options[i].value === eventId) {
                console.log('Found event option at index:', i);
                selectedEvent.remove(i);

                // If this was the selected event, clear the selection
                if (localStorage.getItem('selectedEventID') === eventId) {
                    localStorage.removeItem('selectedEventID');
                    selectedEvent.value = '';
                }

                break;
            }
        }
    }

    // Function to handle the "Välj" button click
    function selectEvent(eventId, jaktstart) {
        // Store the selected event ID in localStorage
        localStorage.setItem('selectedEventID', eventId);

        // Redirect to the event edit page
        window.location.href = '/events/' + eventId;
    }
</script>

{{ template "footer.html" . }}
