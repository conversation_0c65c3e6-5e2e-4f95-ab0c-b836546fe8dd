{{template "header.html" .}}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2>Resultat - {{.heat.Name}}</h2>
                    <div>
                        <a href="/events/{{.event.ID}}/results" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left"></i> Tillbaka till alla resultat
                        </a>
                        <a href="/events/{{.event.ID}}/heats" class="btn btn-outline-secondary">
                            <i class="fas fa-cog"></i> <PERSON><PERSON> deltävlingar
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {{if .results}}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Plats</th>
                                        <th>Seglare</th>
                                        <th><PERSON><PERSON>b</th>
                                        <th>Båt</th>
                                        <th><PERSON><PERSON><PERSON><PERSON></th>
                                        <th>Segelnr</th>
                                        <th>SRS</th>
                                        <th>Besättning</th>
                                        {{if .event.Jaktstart}}
                                        <th>Starttid</th>
                                        {{end}}
                                        <th>Måltid</th>
                                        <th>Seglad tid</th>
                                        <th>Korrigerad tid</th>
                                        <th>Efter föregående</th>
                                        <th>Efter vinnare</th>
                                        <th>Poäng</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {{range .results}}
                                    <tr>
                                        <td>
                                            {{if .DNS}}
                                                <span class="badge bg-warning">DNS</span>
                                            {{else if .DNF}}
                                                <span class="badge bg-danger">DNF</span>
                                            {{else}}
                                                {{.Position}}
                                            {{end}}
                                        </td>
                                        <td>{{.Sailor.Namn}}</td>
                                        <td>{{.Sailor.Klubb}}</td>
                                        <td>{{.Boat.Namn}}</td>
                                        <td>{{.Boat.Battyp}}</td>
                                        <td>
                                            {{if .Boat.Nationality}}{{.Boat.Nationality}}-{{end}}{{.Boat.Segelnummer}}
                                        </td>
                                        <td>{{printf "%.2f" .EventParticipant.SelectedSRSValue}}</td>
                                        <td>{{.TotalPersons}}</td>
                                        {{if $.event.Jaktstart}}
                                        <td>{{.StartTime}}</td>
                                        {{end}}
                                        <td>{{.FinishTime}}</td>
                                        <td>{{.ElapsedTime}}</td>
                                        <td>{{.CorrectedTime}}</td>
                                        <td>{{.TimeToPrevious}}</td>
                                        <td>{{.TimeToWinner}}</td>
                                        <td>{{.Points}}</td>
                                    </tr>
                                    {{end}}
                                </tbody>
                            </table>
                        </div>
                    {{else}}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Inga resultat att visa för denna deltävling ännu.
                        </div>
                    {{end}}
                </div>
            </div>
        </div>
    </div>
</div>

{{template "footer.html" .}}
