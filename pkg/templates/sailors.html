{{ template "header.html" . }}

<div class="container mt-4">
    <h1>{{ .title }}</h1>

    <div class="row mb-3">
        <div class="col-md-6">
            <a href="/sailors/new" class="btn btn-primary"><PERSON><PERSON>gg till seglare</a>
        </div>
        <div class="col-md-6">
            <div class="d-flex">
                <input type="text"
                       name="search"
                       class="form-control me-2"
                       placeholder="Sök seglare..."
                       value="{{ .search }}"
                       hx-get="/sailors/search"
                       hx-trigger="keyup changed delay:300ms, search"
                       hx-target="#sailors-list"
                       hx-indicator="#search-indicator">
                {{ if .search }}
                <button class="btn btn-outline-secondary ms-2"
                        hx-get="/sailors/search"
                        hx-target="#sailors-list"
                        hx-indicator="#search-indicator">
                    Rensa
                    <input type="hidden" name="search" value="">
                </button>
                {{ end }}
                <div id="search-indicator" class="htmx-indicator ms-2">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Söker...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2>Seglare</h2>
        </div>
        <div class="card-body">
            <div id="sailors-list">
                {{ template "sailor_list.html" . }}
            </div>
        </div>
    </div>
</div>

<script>
    // Handle errors in HTMX requests
    document.addEventListener('htmx:responseError', function(event) {
        // Show an error message
        alert('Ett fel uppstod: ' + (event.detail.xhr.responseText || 'Okänt fel'));
    });
</script>

{{ template "footer.html" . }}
