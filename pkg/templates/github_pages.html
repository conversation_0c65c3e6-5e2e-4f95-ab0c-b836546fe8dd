{{ template "header.html" . }}

<div class="container mt-4">
    <h1>GitHub Pages</h1>

    <div class="row">
        <div class="col-md-12">
            {{ if not .githubPagesEnabled }}
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> GitHub Pages integration är inte aktiverad. Aktivera den i <a href="/settings">inställningarna</a>.
                </div>
            {{ else if not .githubPagesRepo }}
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> GitHub repository är inte konfigurerat. Konfigurera det i <a href="/settings">inställningarna</a>.
                </div>
            {{ else if not .githubPagesToken }}
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> GitHub token är inte konfigurerat. Konfigurera det i <a href="/settings">inställningarna</a>.
                </div>
            {{ else }}
                <div class="card mb-4">
                    <div class="card-header">
                        <h2>Publicerade resultat</h2>
                    </div>
                    <div class="card-body">
                        <p>Dina publicerade resultat finns på:</p>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" readonly value="https://{{ (index (split .githubPagesRepo "/") 0) }}.github.io/{{ (index (split .githubPagesRepo "/") 1) }}/" id="github-pages-url">
                            <button class="btn btn-outline-secondary" type="button" onclick="copyGitHubPagesUrl()">
                                <i class="bi bi-clipboard"></i> Kopiera
                            </button>
                            <a href="https://{{ (index (split .githubPagesRepo "/") 0) }}.github.io/{{ (index (split .githubPagesRepo "/") 1) }}/" target="_blank" class="btn btn-outline-primary">
                                <i class="bi bi-box-arrow-up-right"></i> Öppna
                            </a>
                        </div>

                        <div id="github-pages-list" class="mt-4">
                            <div class="d-flex justify-content-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Laddar...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {{ end }}
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Only fetch GitHub Pages if the integration is enabled
        {{ if and .githubPagesEnabled .githubPagesRepo .githubPagesToken }}
            fetchGitHubPages();
        {{ end }}
    });

    function fetchGitHubPages() {
        const container = document.getElementById('github-pages-list');
        if (!container) return;

        // Add a timestamp to prevent caching
        const timestamp = new Date().getTime();
        fetch(`/api/github-pages?t=${timestamp}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Failed to fetch GitHub Pages: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('GitHub Pages data:', data);
                if (data.success) {
                    renderGitHubPagesList(container, data);
                } else {
                    container.innerHTML = `<div class="alert alert-danger">${data.error || 'Kunde inte hämta publicerade sidor'}</div>`;
                }
            })
            .catch(error => {
                console.error('Error fetching GitHub Pages:', error);
                container.innerHTML = `<div class="alert alert-danger">Kunde inte hämta publicerade sidor: ${error.message}</div>`;
            });
    }

    // Render GitHub Pages list with year grouping
    function renderGitHubPagesList(container, data) {
        if (!container) {
            console.error('Container element not found');
            return;
        }

        // Clear the container
        container.innerHTML = '';

        if (!data || !data.years || data.years.length === 0) {
            container.innerHTML = '<p>Inga publicerade sidor hittades.</p>';
            return;
        }

        // Create an accordion for the years
        const accordion = document.createElement('div');
        accordion.className = 'accordion';
        accordion.id = 'githubPagesAccordion';

        // Add each year as an accordion item
        data.years.forEach((yearData, index) => {
            const year = yearData.year;
            const pages = yearData.pages;

            const accordionItem = document.createElement('div');
            accordionItem.className = 'accordion-item';

            const headerId = `heading-${year}`;
            const collapseId = `collapse-${year}`;

            // Create the accordion header
            const header = document.createElement('h2');
            header.className = 'accordion-header';
            header.id = headerId;

            const button = document.createElement('button');
            button.className = 'accordion-button';
            if (index !== 0) {
                button.className += ' collapsed';
            }
            button.type = 'button';
            button.setAttribute('data-bs-toggle', 'collapse');
            button.setAttribute('data-bs-target', `#${collapseId}`);
            button.setAttribute('aria-expanded', index === 0 ? 'true' : 'false');
            button.setAttribute('aria-controls', collapseId);
            button.innerHTML = `${year} <span class="badge bg-primary ms-2">${pages.length}</span>`;

            header.appendChild(button);
            accordionItem.appendChild(header);

            // Create the accordion body
            const collapseDiv = document.createElement('div');
            collapseDiv.id = collapseId;
            collapseDiv.className = 'accordion-collapse collapse';
            if (index === 0) {
                collapseDiv.className += ' show';
            }
            collapseDiv.setAttribute('aria-labelledby', headerId);
            collapseDiv.setAttribute('data-bs-parent', '#githubPagesAccordion');

            const accordionBody = document.createElement('div');
            accordionBody.className = 'accordion-body';

            // Create a list of pages for this year
            const list = document.createElement('ul');
            list.className = 'list-group';

            pages.forEach(page => {
                const listItem = document.createElement('li');
                listItem.className = 'list-group-item d-flex justify-content-between align-items-center';

                // Create the page link
                const pageLink = document.createElement('a');
                pageLink.href = page.url;
                pageLink.target = '_blank';
                pageLink.className = 'text-decoration-none';

                // Format the date if available
                let dateText = '';
                if (page.date) {
                    const dateParts = page.date.split('-');
                    if (dateParts.length >= 3) {
                        dateText = `${dateParts[2]}/${dateParts[1]}`;
                    }
                }

                pageLink.innerHTML = `
                    <div>
                        <strong>${page.event_name}</strong>
                        ${dateText ? `<small class="text-muted d-block">${dateText}</small>` : ''}
                    </div>
                `;

                // Create the delete button
                const deleteButton = document.createElement('button');
                deleteButton.className = 'btn btn-sm btn-outline-danger github-pages-delete-btn';
                deleteButton.setAttribute('data-filename', page.filename);
                deleteButton.innerHTML = '<i class="bi bi-trash"></i>';
                deleteButton.onclick = function(event) {
                    deletePublishedPage(null, page.filename);
                };

                listItem.appendChild(pageLink);
                listItem.appendChild(deleteButton);
                list.appendChild(listItem);
            });

            accordionBody.appendChild(list);
            collapseDiv.appendChild(accordionBody);
            accordionItem.appendChild(collapseDiv);

            accordion.appendChild(accordionItem);
        });

        container.appendChild(accordion);
    }

    // Copy GitHub Pages URL to clipboard
    function copyGitHubPagesUrl() {
        const urlInput = document.getElementById('github-pages-url');
        if (!urlInput) return;

        // Use the modern Clipboard API if available
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(urlInput.value)
                .then(() => {
                    // Show a temporary tooltip or message
                    const copyBtn = urlInput.nextElementSibling;
                    const originalText = copyBtn.innerHTML;
                    copyBtn.innerHTML = '<i class="bi bi-check"></i> Kopierad!';

                    setTimeout(() => {
                        copyBtn.innerHTML = originalText;
                    }, 2000);
                })
                .catch(err => {
                    console.error('Could not copy text: ', err);
                    // Fallback to the old method
                    urlInput.select();
                    document.execCommand('copy');
                });
        } else {
            // Fallback for older browsers
            urlInput.select();
            document.execCommand('copy');

            // Show a temporary tooltip or message
            const copyBtn = urlInput.nextElementSibling;
            const originalText = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="bi bi-check"></i> Kopierad!';

            setTimeout(() => {
                copyBtn.innerHTML = originalText;
            }, 2000);
        }
    }

    // Delete a published page
    function deletePublishedPage(eventId, filename) {
        console.log('deletePublishedPage called with:', eventId, filename);

        if (!filename) {
            console.error('No filename provided for deletion');
            alert('Fel: Inget filnamn angivet för borttagning');
            return;
        }

        if (confirm(`Är du säker på att du vill ta bort den publicerade sidan? Detta kan inte ångras.`)) {
            console.log('User confirmed deletion');

            // Get the button that triggered this event
            // Use the event target if available, otherwise try to find it by selector
            let deleteBtn = event && event.currentTarget;

            // If we don't have the button from the event, try to find it
            if (!deleteBtn) {
                // Try to find the button using the data-filename attribute
                deleteBtn = document.querySelector(`button.github-pages-delete-btn[data-filename="${filename}"]`);

                // If still not found, try other selectors
                if (!deleteBtn) {
                    // Try different selectors to find the button
                    deleteBtn = document.querySelector(`button[onclick*="deletePublishedPage"][onclick*="${filename}"]`);

                    // If still not found, try a more general approach
                    if (!deleteBtn) {
                        // Find all delete buttons
                        const allDeleteButtons = document.querySelectorAll('button.btn-outline-danger');
                        // Try to find one that might be related to this filename
                        for (const btn of allDeleteButtons) {
                            if (btn.onclick && btn.onclick.toString().includes(filename)) {
                                deleteBtn = btn;
                                break;
                            }
                        }
                    }
                }
            }

            console.log('Delete button element:', deleteBtn);

            // Store the original button for reference
            const originalButton = deleteBtn;

            // Log the request URL
            const requestUrl = `/api/github-pages/${encodeURIComponent(filename)}`;
            console.log('Sending DELETE request to:', requestUrl);

            // If we found the button, update its state
            if (deleteBtn) {
                const originalText = deleteBtn.innerHTML;
                deleteBtn.disabled = true;
                deleteBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Tar bort...';
            }

            // Send delete request
            fetch(requestUrl, {
                method: 'DELETE',
            })
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', [...response.headers.entries()]);

                    if (!response.ok) {
                        throw new Error(`Failed to delete page: ${response.status} ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);

                    if (!data.success) {
                        throw new Error(data.error || 'Failed to delete page');
                    }

                    // Show success message
                    alert('Publicerad sida har tagits bort.');

                    // Refresh the list after a longer delay to ensure the server has processed the deletion
                    setTimeout(() => {
                        console.log('Refreshing GitHub Pages list after deletion');
                        // Force a complete reload of the page to ensure fresh data
                        window.location.reload();
                    }, 1000);
                })
                .catch(error => {
                    console.error('Error deleting page:', error);
                    alert(`Kunde inte ta bort sidan: ${error.message}`);

                    // Reset button if we found it
                    if (originalButton) {
                        originalButton.disabled = false;
                        originalButton.innerHTML = '<i class="bi bi-trash"></i>';
                    }
                });
        } else {
            console.log('User cancelled deletion');
        }
    }
</script>

{{ template "footer.html" . }}
