package handlers

import (
	"log"
	"net/http"
	"sort"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rbjoregren/segling/pkg/models"
	"github.com/rbjoregren/segling/pkg/utils"
)

// GetJaktstartTimes renders the jaktstart times page
func (h *Handler) GetJaktstartTimes(c *gin.Context) {
	// Get all events for the dropdown (do this first to ensure we have events for the template)
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Error fetching events: " + err.Error(),
		})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		// If the ID is invalid, redirect to the events page
		c.Redirect(http.StatusSeeOther, "/events")
		return
	}

	event, err := h.DB.GetEvent(id)
	if err != nil {
		// If the event doesn't exist, show a more user-friendly error
		c.HTML(http.StatusOK, "events.html", gin.H{
			"title":                  "Seglingstävlingar",
			"events":                 allEvents,
			"allEvents":              allEvents,
			"activeMenu":             "list",
			"error":                  "Tävlingen kunde inte hittas. Välj en annan tävling.",
			"selectedEventJaktstart": false,
		})
		return
	}

	// Check if jaktstart is enabled for this event
	if !event.IsJaktstart() {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Jaktstart är inte aktiverat för denna tävling",
		})
		return
	}

	// Get heats for this event
	heats, err := h.DB.GetEventHeats(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Ensure default heat exists
	err = h.DB.EnsureDefaultHeat(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get heats again after ensuring default
	heats, err = h.DB.GetEventHeats(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get selected heat from query parameter
	selectedHeatID := int64(0)
	heatIDStr := c.Query("heat")
	if heatIDStr != "" {
		selectedHeatID, _ = strconv.ParseInt(heatIDStr, 10, 64)
	}

	// Find the selected heat or use the first heat
	var selectedHeat models.Heat
	for _, heat := range heats {
		if selectedHeatID > 0 && heat.ID == selectedHeatID {
			selectedHeat = heat
			break
		}
	}

	// If no heat selected or invalid heat ID, use first heat
	if selectedHeat.ID == 0 && len(heats) > 0 {
		selectedHeat = heats[0]
	}

	// Get participants for this event
	participants, err := h.DB.GetEventParticipants(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Debug log
	log.Printf("Event ID: %d, Jaktstart: %v, JaktstartType: %s, Participants count: %d, Selected heat: %d", id, event.IsJaktstart(), event.JaktstartType, len(participants), selectedHeat.ID)

	// Parse the base start time - use heat start time if available, otherwise event start time
	var baseStartTimeStr string
	if selectedHeat.StartTime != "" {
		baseStartTimeStr = selectedHeat.StartTime
	} else {
		baseStartTimeStr = event.Starttid
	}

	baseStartTime, err := time.Parse("15:04", baseStartTimeStr)
	if err != nil {
		// If the start time is invalid, use the current time
		baseStartTime = time.Now()
	}

	// First, find the lowest SRS value among all participants
	lowestSRS := 9999.0 // Start with a high value
	var validParticipants []struct {
		Participant models.EventParticipant
		Sailor      models.Sailor
		Boat        models.Boat
		SRSValue    float64
	}

	log.Printf("Starting to process %d participants", len(participants))

	for _, p := range participants {
		// Get the sailor information
		sailor, err := h.DB.GetSailor(p.SailorID)
		if err != nil {
			continue
		}

		// Get the boat information
		boat, err := h.DB.GetBoat(p.BoatID)
		if err != nil {
			continue
		}

		// Get the SRS value to use
		var srsValue float64
		log.Printf("DEBUG: Participant %d - SRS Type: %s, Boat SRS values: SRS=%f, SRSUtanUndanvindsegel=%f, SRSShorthanded=%f, SRSShorthandedUtanUndanvindsegel=%f",
			p.ID, p.SRSType, boat.SRS, boat.SRSUtanUndanvindsegel, boat.SRSShorthanded, boat.SRSShorthandedUtanUndanvindsegel)

		if p.UseCustomSRSValue && p.CustomSRSValue > 0 {
			srsValue = p.CustomSRSValue
			log.Printf("DEBUG: Participant %d using custom SRS value: %f", p.ID, srsValue)
		} else {
			// Always get the SRS value from the boat based on SRS type
			switch p.SRSType {
			case "srs_utan_undanvindsegel":
				if boat.SRSUtanUndanvindsegel > 0 {
					srsValue = boat.SRSUtanUndanvindsegel
					log.Printf("DEBUG: Participant %d using boat SRS utan undanvindsegel value: %f", p.ID, srsValue)
				} else {
					srsValue = boat.SRS
					log.Printf("DEBUG: Participant %d using boat SRS value (fallback): %f", p.ID, srsValue)
				}
			case "srs_shorthanded":
				if boat.SRSShorthanded > 0 {
					srsValue = boat.SRSShorthanded
					log.Printf("DEBUG: Participant %d using boat SRS shorthanded value: %f", p.ID, srsValue)
				} else {
					srsValue = boat.SRS
					log.Printf("DEBUG: Participant %d using boat SRS value (fallback): %f", p.ID, srsValue)
				}
			case "srs_shorthanded_utan_undanvindsegel":
				if boat.SRSShorthandedUtanUndanvindsegel > 0 {
					srsValue = boat.SRSShorthandedUtanUndanvindsegel
					log.Printf("DEBUG: Participant %d using boat SRS shorthanded utan undanvindsegel value: %f", p.ID, srsValue)
				} else {
					srsValue = boat.SRS
					log.Printf("DEBUG: Participant %d using boat SRS value (fallback): %f", p.ID, srsValue)
				}
			default:
				if boat.SRS > 0 {
					srsValue = boat.SRS
					log.Printf("DEBUG: Participant %d using boat SRS value: %f", p.ID, srsValue)
				} else {
					log.Printf("DEBUG: Skipping participant %d due to invalid SRS value", p.ID)
					continue
				}
			}
		}

		// Skip invalid SRS values
		if srsValue <= 0 {
			log.Printf("Skipping participant %d due to invalid SRS value: %f", p.ID, srsValue)
			continue
		}

		// Update lowest SRS if needed
		if srsValue < lowestSRS {
			lowestSRS = srsValue
			log.Printf("DEBUG: New lowest SRS value: %f from participant %d with SRS type %s", lowestSRS, p.ID, p.SRSType)
		}

		// Add to valid participants
		validParticipants = append(validParticipants, struct {
			Participant models.EventParticipant
			Sailor      models.Sailor
			Boat        models.Boat
			SRSValue    float64
		}{
			Participant: p,
			Sailor:      sailor,
			Boat:        boat,
			SRSValue:    srsValue,
		})
	}

	// Calculate start times for each participant
	log.Printf("Found %d valid participants with SRS values, lowest SRS: %f", len(validParticipants), lowestSRS)
	log.Printf("Event details - Banlängd: %d nm, Vind: %d m/s, Starttid: %s", event.Banlangd, event.Vind, event.Starttid)

	var participantsWithStartTimes []ParticipantWithStartTime
	for _, vp := range validParticipants {
		// Calculate the start time offset in seconds with multiplier support
		multiplier := event.GetJaktstartMultiplier()
		offsetSeconds := utils.CalculateStartTimeWithMultiplier(float64(event.Banlangd), event.Vind, vp.SRSValue, lowestSRS, multiplier)
		log.Printf("Calculated offset for participant %d (SRS: %f, multiplier: %f): %d seconds", vp.Participant.ID, vp.SRSValue, multiplier, offsetSeconds)

		// Format the offset as a human-readable string
		offsetStr := utils.FormatStartTime(offsetSeconds)

		// Calculate the absolute start time
		absoluteStartTime := utils.CalculateAbsoluteStartTime(baseStartTime, offsetSeconds)
		absoluteStartTimeStr := utils.FormatAbsoluteStartTime(absoluteStartTime)
		log.Printf("Participant %d start time: %s (offset: %s)", vp.Participant.ID, absoluteStartTimeStr, offsetStr)

		// Update the participant's SRS value for display
		updatedParticipant := vp.Participant

		// If we're using a boat's SRS value, make sure we're using the correct SRS type
		if updatedParticipant.SelectedSRSValue <= 0 {
			updatedParticipant.SelectedSRSValue = vp.SRSValue

			// If SRS type is empty or default, set it based on the boat's SRS value we're using
			if updatedParticipant.SRSType == "" || updatedParticipant.SRSType == "srs" {
				// Determine which SRS value we're using from the boat
				switch vp.SRSValue {
				case vp.Boat.SRSUtanUndanvindsegel:
					updatedParticipant.SRSType = "srs_utan_undanvindsegel"
				case vp.Boat.SRSShorthanded:
					updatedParticipant.SRSType = "srs_shorthanded"
				case vp.Boat.SRSShorthandedUtanUndanvindsegel:
					updatedParticipant.SRSType = "srs_shorthanded_utan_undanvindsegel"
				default:
					updatedParticipant.SRSType = "srs"
				}
			}
		}

		// Add to the list
		participantsWithStartTimes = append(participantsWithStartTimes, ParticipantWithStartTime{
			EventParticipant:  updatedParticipant,
			StartTimeOffset:   offsetStr,
			AbsoluteStartTime: absoluteStartTimeStr,
			Sailor:            vp.Sailor,
			Boat:              vp.Boat,
		})
	}

	// Sort participants by start time (earliest first)
	sort.Slice(participantsWithStartTimes, func(i, j int) bool {
		timeI, _ := time.Parse("15:04", participantsWithStartTimes[i].AbsoluteStartTime)
		timeJ, _ := time.Parse("15:04", participantsWithStartTimes[j].AbsoluteStartTime)
		return timeI.Before(timeJ)
	})

	// We already have allEvents from earlier

	c.HTML(http.StatusOK, "jaktstart_times.html", gin.H{
		"title":                  "Jaktstarttider - " + event.Namn,
		"event":                  event,
		"participants":           participantsWithStartTimes,
		"heats":                  heats,
		"selectedHeat":           selectedHeat,
		"allEvents":              allEvents,
		"selectedEventID":        id,
		"selectedEventJaktstart": event.IsJaktstart(),
		"activeMenu":             "jaktstart",
	})
}

// GetJaktstartTimesPrint renders the print-friendly jaktstart times page
func (h *Handler) GetJaktstartTimesPrint(c *gin.Context) {
	// Get all events for the dropdown (do this first to ensure we have events for the template)
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Error fetching events: " + err.Error(),
		})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		// If the ID is invalid, redirect to the events page
		c.Redirect(http.StatusSeeOther, "/events")
		return
	}

	event, err := h.DB.GetEvent(id)
	if err != nil {
		// If the event doesn't exist, show a more user-friendly error
		c.HTML(http.StatusOK, "events.html", gin.H{
			"title":                  "Seglingstävlingar",
			"events":                 allEvents,
			"allEvents":              allEvents,
			"activeMenu":             "list",
			"error":                  "Tävlingen kunde inte hittas. Välj en annan tävling.",
			"selectedEventJaktstart": false,
		})
		return
	}

	// Check if jaktstart is enabled for this event
	if !event.IsJaktstart() {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Jaktstart är inte aktiverat för denna tävling",
		})
		return
	}

	// Get heats for this event
	heats, err := h.DB.GetEventHeats(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Ensure default heat exists
	err = h.DB.EnsureDefaultHeat(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get heats again after ensuring default
	heats, err = h.DB.GetEventHeats(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get selected heat from query parameter
	selectedHeatID := int64(0)
	heatIDStr := c.Query("heat")
	if heatIDStr != "" {
		selectedHeatID, _ = strconv.ParseInt(heatIDStr, 10, 64)
	}

	// Find the selected heat or use the first heat
	var selectedHeat models.Heat
	for _, heat := range heats {
		if selectedHeatID > 0 && heat.ID == selectedHeatID {
			selectedHeat = heat
			break
		}
	}

	// If no heat selected or invalid heat ID, use first heat
	if selectedHeat.ID == 0 && len(heats) > 0 {
		selectedHeat = heats[0]
	}

	// Get participants for this event
	participants, err := h.DB.GetEventParticipants(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Parse the base start time - use heat start time if available, otherwise event start time
	var baseStartTimeStr string
	if selectedHeat.StartTime != "" {
		baseStartTimeStr = selectedHeat.StartTime
	} else {
		baseStartTimeStr = event.Starttid
	}

	baseStartTime, err := time.Parse("15:04", baseStartTimeStr)
	if err != nil {
		// If the start time is invalid, use the current time
		baseStartTime = time.Now()
	}

	// First, find the lowest SRS value among all participants
	lowestSRS := 9999.0 // Start with a high value
	var validParticipants []struct {
		Participant models.EventParticipant
		Sailor      models.Sailor
		Boat        models.Boat
		SRSValue    float64
	}

	for _, p := range participants {
		// Get the sailor information
		sailor, err := h.DB.GetSailor(p.SailorID)
		if err != nil {
			continue
		}

		// Get the boat information
		boat, err := h.DB.GetBoat(p.BoatID)
		if err != nil {
			continue
		}

		// Get the SRS value to use
		var srsValue float64
		if p.UseCustomSRSValue && p.CustomSRSValue > 0 {
			srsValue = p.CustomSRSValue
		} else {
			// Always get the SRS value from the boat based on SRS type
			switch p.SRSType {
			case "srs_utan_undanvindsegel":
				if boat.SRSUtanUndanvindsegel > 0 {
					srsValue = boat.SRSUtanUndanvindsegel
				} else {
					srsValue = boat.SRS
				}
			case "srs_shorthanded":
				if boat.SRSShorthanded > 0 {
					srsValue = boat.SRSShorthanded
				} else {
					srsValue = boat.SRS
				}
			case "srs_shorthanded_utan_undanvindsegel":
				if boat.SRSShorthandedUtanUndanvindsegel > 0 {
					srsValue = boat.SRSShorthandedUtanUndanvindsegel
				} else {
					srsValue = boat.SRS
				}
			default:
				if boat.SRS > 0 {
					srsValue = boat.SRS
				} else {
					continue
				}
			}
		}

		// Skip invalid SRS values
		if srsValue <= 0 {
			continue
		}

		// Update lowest SRS if needed
		if srsValue < lowestSRS {
			lowestSRS = srsValue
		}

		// Add to valid participants
		validParticipants = append(validParticipants, struct {
			Participant models.EventParticipant
			Sailor      models.Sailor
			Boat        models.Boat
			SRSValue    float64
		}{
			Participant: p,
			Sailor:      sailor,
			Boat:        boat,
			SRSValue:    srsValue,
		})
	}

	// Calculate start times for each participant
	var participantsWithStartTimes []ParticipantWithStartTime
	for _, vp := range validParticipants {
		// Calculate the start time offset in seconds with multiplier support
		multiplier := event.GetJaktstartMultiplier()
		offsetSeconds := utils.CalculateStartTimeWithMultiplier(float64(event.Banlangd), event.Vind, vp.SRSValue, lowestSRS, multiplier)

		// Format the offset as a human-readable string
		offsetStr := utils.FormatStartTime(offsetSeconds)

		// Calculate the absolute start time
		absoluteStartTime := utils.CalculateAbsoluteStartTime(baseStartTime, offsetSeconds)
		absoluteStartTimeStr := utils.FormatAbsoluteStartTime(absoluteStartTime)

		// Update the participant's SRS value for display
		updatedParticipant := vp.Participant

		// If we're using a boat's SRS value, make sure we're using the correct SRS type
		if updatedParticipant.SelectedSRSValue <= 0 {
			updatedParticipant.SelectedSRSValue = vp.SRSValue

			// If SRS type is empty or default, set it based on the boat's SRS value we're using
			if updatedParticipant.SRSType == "" || updatedParticipant.SRSType == "srs" {
				// Determine which SRS value we're using from the boat
				switch vp.SRSValue {
				case vp.Boat.SRSUtanUndanvindsegel:
					updatedParticipant.SRSType = "srs_utan_undanvindsegel"
				case vp.Boat.SRSShorthanded:
					updatedParticipant.SRSType = "srs_shorthanded"
				case vp.Boat.SRSShorthandedUtanUndanvindsegel:
					updatedParticipant.SRSType = "srs_shorthanded_utan_undanvindsegel"
				default:
					updatedParticipant.SRSType = "srs"
				}
			}
		}

		// Add to the list
		participantsWithStartTimes = append(participantsWithStartTimes, ParticipantWithStartTime{
			EventParticipant:  updatedParticipant,
			StartTimeOffset:   offsetStr,
			AbsoluteStartTime: absoluteStartTimeStr,
			Sailor:            vp.Sailor,
			Boat:              vp.Boat,
		})
	}

	// Sort participants by start time (earliest first)
	sort.Slice(participantsWithStartTimes, func(i, j int) bool {
		timeI, _ := time.Parse("15:04", participantsWithStartTimes[i].AbsoluteStartTime)
		timeJ, _ := time.Parse("15:04", participantsWithStartTimes[j].AbsoluteStartTime)
		return timeI.Before(timeJ)
	})

	c.HTML(http.StatusOK, "jaktstart_times_print.html", gin.H{
		"title":        "Jaktstarttider - " + event.Namn + " - Utskrift",
		"event":        event,
		"participants": participantsWithStartTimes,
	})
}
