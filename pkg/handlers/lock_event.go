package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// LockEvent handles the request to lock an event
func (h *Handler) LockEvent(c *gin.Context) {
	// Get the event ID from the URL
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Invalid event ID",
		})
		return
	}

	// Get the event to check if it exists
	event, err := h.DB.GetEvent(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to get event: " + err.Error(),
		})
		return
	}

	// Check if the event is already locked
	if event.Locked {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Event is already locked",
		})
		return
	}

	// Lock the event
	err = h.DB.LockEvent(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to lock event: " + err.Error(),
		})
		return
	}

	// Redirect to the results page
	c.Redirect(http.StatusSeeOther, "/events/"+idStr+"/results")
}

// UnlockEvent handles the request to unlock an event
func (h *Handler) UnlockEvent(c *gin.Context) {
	// Get the event ID from the URL
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Invalid event ID",
		})
		return
	}

	// Get the event to check if it exists
	event, err := h.DB.GetEvent(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to get event: " + err.Error(),
		})
		return
	}

	// Check if the event is locked
	if !event.Locked {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Event is not locked",
		})
		return
	}

	// Unlock the event
	err = h.DB.UnlockEvent(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to unlock event: " + err.Error(),
		})
		return
	}

	// Redirect to the results page
	c.Redirect(http.StatusSeeOther, "/events/"+idStr+"/results")
}
