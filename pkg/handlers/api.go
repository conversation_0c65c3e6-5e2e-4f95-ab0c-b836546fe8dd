package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// GetEventAPI returns a single event by ID as JSON
func (h *Handler) GetEventAPI(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
		return
	}

	event, err := h.DB.GetEvent(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Event not found"})
		return
	}

	c.<PERSON>(http.StatusOK, event)
}

// GetEventsAPI returns all events as JSON
func (h *Handler) GetEventsAPI(c *gin.Context) {
	events, err := h.DB.GetEvents()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, events)
}

// GetEventParticipantCountAPI returns 0 for participant count (deprecated)
func (h *Handler) GetEventParticipantCountAPI(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
		return
	}

	// Always return 0 for participant count
	c.JSON(http.StatusOK, gin.H{
		"event_id": id,
		"count":    0,
	})
}
