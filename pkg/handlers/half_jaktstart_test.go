package handlers

import (
	"net/http"
	"net/http/httptest"
	"net/url"
	"os"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/rbjoregren/segling/pkg/database"
	"github.com/rbjoregren/segling/pkg/models"
)

// TestHalfJaktstartFeature tests the half jaktstart functionality
// Half jaktstart means boats start with half the normal jaktstart offset
// so they meet at half the course distance instead of the finish line
func TestHalfJaktstartFeature(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create test database
	testDB := "test_half_jaktstart.db"
	os.Remove(testDB) // Remove any existing test database

	db, err := database.New(testDB)
	if err != nil {
		t.Fatalf("Failed to create test database: %v", err)
	}
	defer func() {
		db.Close()
		os.Remove(testDB)
	}()

	// Initialize database
	err = db.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize database: %v", err)
	}

	// Create handler
	handler := NewHandler(db)

	t.Run("Create Event with Half Jaktstart", func(t *testing.T) {
		// Create form data for half jaktstart event
		form := url.Values{}
		form.Add("namn", "Test Half Jaktstart Event")
		form.Add("datum", "2025-01-15")
		form.Add("starttid", "12:00")
		form.Add("vind", "5")
		form.Add("banlangd", "10")
		form.Add("jaktstart_type", "half")
		form.Add("tavlingstyp", "Test")
		form.Add("beskrivning", "Test event with half jaktstart")

		// Create request
		req, _ := http.NewRequest("POST", "/events", strings.NewReader(form.Encode()))
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

		// Create response recorder
		w := httptest.NewRecorder()

		// Create router and add route
		router := gin.New()
		router.POST("/events", handler.PostEvent)

		// Perform request
		router.ServeHTTP(w, req)

		// Check that the request was successful (should redirect)
		if w.Code != http.StatusSeeOther {
			t.Errorf("Expected status %d, got %d. Response: %s", http.StatusSeeOther, w.Code, w.Body.String())
			return
		}

		// Get the created event from the database
		events, err := db.GetEvents()
		if err != nil {
			t.Fatalf("Failed to get events: %v", err)
		}

		if len(events) != 1 {
			t.Fatalf("Expected 1 event, got %d", len(events))
		}

		event := events[0]

		// Verify that jaktstart_type is set to "half"
		if event.JaktstartType != models.JaktstartTypeHalf {
			t.Errorf("Expected jaktstart_type 'half', got '%s'", event.JaktstartType)
		}

		// Verify helper methods work correctly
		if !event.IsJaktstart() {
			t.Error("Expected IsJaktstart() to return true for half jaktstart")
		}

		if !event.IsHalfJaktstart() {
			t.Error("Expected IsHalfJaktstart() to return true")
		}

		if event.IsEntypsegling() {
			t.Error("Expected IsEntypsegling() to return false")
		}

		// Verify multiplier is 0.5 for half jaktstart
		multiplier := event.GetJaktstartMultiplier()
		if multiplier != 0.5 {
			t.Errorf("Expected multiplier 0.5 for half jaktstart, got %f", multiplier)
		}

		// Verify legacy fields are set correctly for backward compatibility
		if !event.Jaktstart {
			t.Error("Expected legacy Jaktstart field to be true for half jaktstart")
		}

		if event.Entypsegling {
			t.Error("Expected legacy Entypsegling field to be false for half jaktstart")
		}
	})

	t.Run("Create Event with Regular Jaktstart", func(t *testing.T) {
		// Create form data for regular jaktstart event
		form := url.Values{}
		form.Add("namn", "Test Regular Jaktstart Event")
		form.Add("datum", "2025-01-16")
		form.Add("starttid", "13:00")
		form.Add("vind", "6")
		form.Add("banlangd", "15")
		form.Add("jaktstart_type", "regular")
		form.Add("tavlingstyp", "Test")
		form.Add("beskrivning", "Test event with regular jaktstart")

		// Create request
		req, _ := http.NewRequest("POST", "/events", strings.NewReader(form.Encode()))
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

		// Create response recorder
		w := httptest.NewRecorder()

		// Create router and add route
		router := gin.New()
		router.POST("/events", handler.PostEvent)

		// Perform request
		router.ServeHTTP(w, req)

		// Check that the request was successful (should redirect)
		if w.Code != http.StatusSeeOther {
			t.Errorf("Expected status %d, got %d. Response: %s", http.StatusSeeOther, w.Code, w.Body.String())
			return
		}

		// Get all events from the database
		events, err := db.GetEvents()
		if err != nil {
			t.Fatalf("Failed to get events: %v", err)
		}

		if len(events) != 2 {
			t.Fatalf("Expected 2 events, got %d", len(events))
		}

		// Find the regular jaktstart event
		var regularEvent *models.Event
		for _, event := range events {
			if event.Namn == "Test Regular Jaktstart Event" {
				regularEvent = &event
				break
			}
		}

		if regularEvent == nil {
			t.Fatal("Could not find the regular jaktstart event")
		}

		// Verify that jaktstart_type is set to "regular"
		if regularEvent.JaktstartType != models.JaktstartTypeRegular {
			t.Errorf("Expected jaktstart_type 'regular', got '%s'", regularEvent.JaktstartType)
		}

		// Verify helper methods work correctly
		if !regularEvent.IsJaktstart() {
			t.Error("Expected IsJaktstart() to return true for regular jaktstart")
		}

		if regularEvent.IsHalfJaktstart() {
			t.Error("Expected IsHalfJaktstart() to return false for regular jaktstart")
		}

		// Verify multiplier is 1.0 for regular jaktstart
		multiplier := regularEvent.GetJaktstartMultiplier()
		if multiplier != 1.0 {
			t.Errorf("Expected multiplier 1.0 for regular jaktstart, got %f", multiplier)
		}
	})

	t.Run("Create Event with No Jaktstart", func(t *testing.T) {
		// Create form data for regular sailing event (no jaktstart)
		form := url.Values{}
		form.Add("namn", "Test Regular Sailing Event")
		form.Add("datum", "2025-01-17")
		form.Add("starttid", "14:00")
		form.Add("vind", "4")
		form.Add("banlangd", "8")
		form.Add("jaktstart_type", "none")
		form.Add("tavlingstyp", "Test")
		form.Add("beskrivning", "Test event with no jaktstart")

		// Create request
		req, _ := http.NewRequest("POST", "/events", strings.NewReader(form.Encode()))
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

		// Create response recorder
		w := httptest.NewRecorder()

		// Create router and add route
		router := gin.New()
		router.POST("/events", handler.PostEvent)

		// Perform request
		router.ServeHTTP(w, req)

		// Check that the request was successful (should redirect)
		if w.Code != http.StatusSeeOther {
			t.Errorf("Expected status %d, got %d. Response: %s", http.StatusSeeOther, w.Code, w.Body.String())
			return
		}

		// Get all events from the database
		events, err := db.GetEvents()
		if err != nil {
			t.Fatalf("Failed to get events: %v", err)
		}

		if len(events) != 3 {
			t.Fatalf("Expected 3 events, got %d", len(events))
		}

		// Find the regular sailing event
		var regularEvent *models.Event
		for _, event := range events {
			if event.Namn == "Test Regular Sailing Event" {
				regularEvent = &event
				break
			}
		}

		if regularEvent == nil {
			t.Fatal("Could not find the regular sailing event")
		}

		// Verify that jaktstart_type is set to "none"
		if regularEvent.JaktstartType != models.JaktstartTypeNone {
			t.Errorf("Expected jaktstart_type 'none', got '%s'", regularEvent.JaktstartType)
		}

		// Verify helper methods work correctly
		if regularEvent.IsJaktstart() {
			t.Error("Expected IsJaktstart() to return false for no jaktstart")
		}

		if regularEvent.IsHalfJaktstart() {
			t.Error("Expected IsHalfJaktstart() to return false for no jaktstart")
		}

		// Verify multiplier is 0.0 for no jaktstart
		multiplier := regularEvent.GetJaktstartMultiplier()
		if multiplier != 0.0 {
			t.Errorf("Expected multiplier 0.0 for no jaktstart, got %f", multiplier)
		}

		// Verify legacy fields are set correctly
		if regularEvent.Jaktstart {
			t.Error("Expected legacy Jaktstart field to be false for no jaktstart")
		}
	})

	t.Run("Create Entypsegling Event", func(t *testing.T) {
		// Create form data for entypsegling event
		form := url.Values{}
		form.Add("namn", "Test Entypsegling Event")
		form.Add("datum", "2025-01-18")
		form.Add("starttid", "15:00")
		form.Add("vind", "3")
		form.Add("banlangd", "12")
		form.Add("jaktstart_type", "entypsegling")
		form.Add("boat_type", "Laser")
		form.Add("tavlingstyp", "Test")
		form.Add("beskrivning", "Test entypsegling event")

		// Create request
		req, _ := http.NewRequest("POST", "/events", strings.NewReader(form.Encode()))
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

		// Create response recorder
		w := httptest.NewRecorder()

		// Create router and add route
		router := gin.New()
		router.POST("/events", handler.PostEvent)

		// Perform request
		router.ServeHTTP(w, req)

		// Check that the request was successful (should redirect)
		if w.Code != http.StatusSeeOther {
			t.Errorf("Expected status %d, got %d. Response: %s", http.StatusSeeOther, w.Code, w.Body.String())
			return
		}

		// Get all events from the database
		events, err := db.GetEvents()
		if err != nil {
			t.Fatalf("Failed to get events: %v", err)
		}

		if len(events) != 4 {
			t.Fatalf("Expected 4 events, got %d", len(events))
		}

		// Find the entypsegling event
		var entypEvent *models.Event
		for _, event := range events {
			if event.Namn == "Test Entypsegling Event" {
				entypEvent = &event
				break
			}
		}

		if entypEvent == nil {
			t.Fatal("Could not find the entypsegling event")
		}

		// Verify that jaktstart_type is set to "entypsegling"
		if entypEvent.JaktstartType != models.JaktstartTypeEntypsegling {
			t.Errorf("Expected jaktstart_type 'entypsegling', got '%s'", entypEvent.JaktstartType)
		}

		// Verify helper methods work correctly
		if entypEvent.IsJaktstart() {
			t.Error("Expected IsJaktstart() to return false for entypsegling")
		}

		if !entypEvent.IsEntypsegling() {
			t.Error("Expected IsEntypsegling() to return true for entypsegling")
		}

		// Verify boat type is set
		if entypEvent.BoatType != "Laser" {
			t.Errorf("Expected boat type 'Laser', got '%s'", entypEvent.BoatType)
		}

		// Verify legacy fields are set correctly
		if entypEvent.Jaktstart {
			t.Error("Expected legacy Jaktstart field to be false for entypsegling")
		}

		if !entypEvent.Entypsegling {
			t.Error("Expected legacy Entypsegling field to be true for entypsegling")
		}
	})
}
