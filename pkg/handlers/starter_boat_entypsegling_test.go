package handlers

import (
	"fmt"
	"html/template"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestStarterBoatEntypsegling tests that the starter-boat print page works correctly for entypsegling events
// This test verifies:
// 1. Entypsegling events show placement form with seglare, segelnr, and heat columns
// 2. Normal sailing events continue to show the original starter boat format
// 3. Heat columns are properly displayed for entypsegling events
func TestStarterBoatEntypsegling(t *testing.T) {
	// Setup test database
	db := setupResultsTestDB(t)
	defer func() {
		db.Close()
		os.Remove("test_starter_boat_entypsegling.db")
	}()

	// Create handler
	handler := &Handler{DB: db}

	// Create test entypsegling event
	entypEventID, err := db.CreateEventWithEntypsegling(
		"Test Entypsegling Starter Boat", time.Now(), "12:00", 5, 10, false,
		"Test", "Test Description", true, "Laser")
	require.NoError(t, err)

	// Create test regular sailing event
	regularEventID, err := db.CreateEvent(
		"Test Regular Starter Boat", time.Now(), "12:00", 5, 10, false,
		"Test", "Test Description")
	require.NoError(t, err)

	// Create test sailors
	sailor1ID, err := db.CreateSailor("Test Sailor 1", "123456789", "Test Club 1")
	require.NoError(t, err)

	sailor2ID, err := db.CreateSailor("Test Sailor 2", "987654321", "Test Club 2")
	require.NoError(t, err)

	// Create test boat for regular event
	boatID, err := db.CreateBoat("Test Boat", "Test Type", "TEST123", "123", "SWE", 1.05, 1.0, 0.95, 0.9)
	require.NoError(t, err)

	// Add participants to entypsegling event (no boats, personal numbers)
	_, err = db.AddParticipantToEvent(entypEventID, sailor1ID, 0, "entypsegling", 0, 0, false)
	require.NoError(t, err)
	_, err = db.AddParticipantToEvent(entypEventID, sailor2ID, 0, "entypsegling", 0, 0, false)
	require.NoError(t, err)

	// Set personal numbers for entypsegling participants
	_, err = db.Exec("UPDATE event_participants SET personal_number = 'SWE-201' WHERE event_id = ? AND sailor_id = ?", entypEventID, sailor1ID)
	require.NoError(t, err)
	_, err = db.Exec("UPDATE event_participants SET personal_number = 'SWE-202' WHERE event_id = ? AND sailor_id = ?", entypEventID, sailor2ID)
	require.NoError(t, err)

	// Add participants to regular event (with boats)
	_, err = db.AddParticipantToEvent(regularEventID, sailor1ID, boatID, "srs", 1.05, 0, false)
	require.NoError(t, err)
	_, err = db.AddParticipantToEvent(regularEventID, sailor2ID, boatID, "srs", 1.05, 0, false)
	require.NoError(t, err)

	// Create heats for entypsegling event
	_, err = db.CreateHeat(entypEventID, 1, "Heat 1", "")
	require.NoError(t, err)
	_, err = db.CreateHeat(entypEventID, 2, "Heat 2", "")
	require.NoError(t, err)

	// Create heat for regular event
	_, err = db.CreateHeat(regularEventID, 1, "Heat 1", "12:00")
	require.NoError(t, err)

	// Test cases
	testCases := []struct {
		name              string
		eventID           int64
		expectedInHTML    []string
		notExpectedInHTML []string
		description       string
	}{
		{
			name:    "Entypsegling Starter Boat Print",
			eventID: entypEventID,
			expectedInHTML: []string{
				"Placeringsformulär", // Title should be placement form
				"Seglare",            // Sailor column
				"Segelnr",            // Personal number column
				"Heat 1",             // Heat 1 column
				"Heat 2",             // Heat 2 column
				"SWE-201",            // Personal number 1
				"SWE-202",            // Personal number 2
				"Test Sailor 1",      // Sailor name 1
				"Test Sailor 2",      // Sailor name 2
				"Använd detta formulär för att registrera placeringar", // Instructions
				"Båttyp:</strong> Laser",                               // Boat type info
			},
			notExpectedInHTML: []string{
				"Startbåtsinformation",                 // Should not show starter boat title
				"Start #",                              // Should not show start number
				"Starttid",                             // Should not show start time
				"<th class=\"col-battyp\">Båttyp</th>", // Should not show boat type column header
				"<th class=\"col-srs\">SRS</th>",       // Should not show SRS column header
				"Målgång",                              // Should not show finish time column
				"S/H = short hand",                     // Should not show SRS notes
			},
			description: "Entypsegling event should show placement form with heat columns",
		},
		{
			name:    "Regular Sailing Starter Boat Print",
			eventID: regularEventID,
			expectedInHTML: []string{
				"Startbåtsinformation", // Title should be starter boat info
				"Start #",              // Start number column
				"Starttid",             // Start time column
				"Seglare",              // Sailor column
				"Klubb",                // Club column
				"Båttyp",               // Boat type column
				"SRS",                  // SRS column
				"Målgång",              // Finish time column
				"Test Sailor 1",        // Sailor name 1
				"Test Sailor 2",        // Sailor name 2
				"S/H = short hand",     // SRS notes
			},
			notExpectedInHTML: []string{
				"Placeringsformulär", // Should not show placement form title
				"Heat 1",             // Should not show heat columns
				"Heat 2",             // Should not show heat columns
				"SWE-201",            // Should not show personal numbers
				"SWE-202",            // Should not show personal numbers
				"Använd detta formulär för att registrera placeringar", // Should not show placement instructions
			},
			description: "Regular sailing event should show traditional starter boat format",
		},
	}

	// Run test cases
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup Gin router
			gin.SetMode(gin.TestMode)
			router := gin.New()

			// Add template functions
			router.SetFuncMap(template.FuncMap{
				"add": func(a, b int) int {
					return a + b
				},
				"now": func() time.Time {
					return time.Now()
				},
				"split": strings.Split,
				"printf": func(format string, args ...interface{}) string {
					return fmt.Sprintf(format, args...)
				},
				"isDiscardEnabled": func(event interface{}) bool {
					if e, ok := event.(map[string]interface{}); ok {
						if discardAfterHeats, exists := e["DiscardAfterHeats"]; exists {
							if val, ok := discardAfterHeats.(int); ok {
								return val > 0
							}
						}
					}
					return false
				},
				"getDiscardCount": func(event interface{}, totalHeats int) int {
					if e, ok := event.(map[string]interface{}); ok {
						if discardAfterHeats, exists := e["DiscardAfterHeats"]; exists {
							if val, ok := discardAfterHeats.(int); ok && val > 0 {
								if totalHeats >= val {
									return totalHeats / val
								}
							}
						}
					}
					return 0
				},
			})

			// Load templates
			router.LoadHTMLGlob("../templates/*")

			// Register route
			router.GET("/events/:id/starter-boat/print", handler.GetStarterBoatPrint)

			// Create request
			req, err := http.NewRequest("GET", fmt.Sprintf("/events/%d/starter-boat/print", tc.eventID), nil)
			require.NoError(t, err)

			// Create response recorder
			w := httptest.NewRecorder()

			// Perform request
			router.ServeHTTP(w, req)

			// Check status code
			assert.Equal(t, http.StatusOK, w.Code, "Expected status 200 for %s", tc.description)

			// Get response body
			body := w.Body.String()

			// Check expected content is present
			for _, expected := range tc.expectedInHTML {
				assert.Contains(t, body, expected,
					"Expected '%s' to be present in %s", expected, tc.description)
			}

			// Check that unwanted content is not present
			for _, notExpected := range tc.notExpectedInHTML {
				assert.NotContains(t, body, notExpected,
					"Expected '%s' to NOT be present in %s", notExpected, tc.description)
			}

			// Debug: Print part of the response for manual verification
			t.Logf("Response snippet for %s:\n%s", tc.description, body[:minInt(800, len(body))])
		})
	}
}

// Helper function for min (if not already defined)
func minInt(a, b int) int {
	if a < b {
		return a
	}
	return b
}
