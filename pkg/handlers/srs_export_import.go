package handlers

import (
	"encoding/csv"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// csvSeparator is the separator used for CSV files
// Using comma as default since it's more widely supported
const csvSeparator = ','

// ExportSRSBoatTypesCSV exports all SRS boat types as a CSV file
func (h *Handler) ExportSRSBoatTypesCSV(c *gin.Context) {
	// Get all SRS boat types
	boatTypes, err := h.DB.GetSRSBoatTypes()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Set headers for CSV download
	filename := fmt.Sprintf("srs_tabell_%s.csv", time.Now().Format("2006-01-02"))
	c.<PERSON>("Content-Type", "text/csv")
	c.<PERSON>("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

	// Create CSV writer
	writer := csv.NewWriter(c.Writer)
	// Set the separator
	writer.Comma = csvSeparator
	defer writer.Flush()

	// Write header row
	headers := []string{
		"Båttyp",
		"SRS",
		"SRS utan undanvindsegel",
		"SRS S/H",
		"SRS S/H utan undanvindsegel",
	}
	if err := writer.Write(headers); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Kunde inte skapa CSV-fil: " + err.Error()})
		return
	}

	// Write data rows
	for _, bt := range boatTypes {
		row := []string{
			bt.Battyp,
			fmt.Sprintf("%.3f", bt.SRS),
			fmt.Sprintf("%.3f", bt.SRSUtanUndanvindsegel),
			fmt.Sprintf("%.3f", bt.SRSShorthanded),
			fmt.Sprintf("%.3f", bt.SRSShorthandedUtanUndanvindsegel),
		}
		if err := writer.Write(row); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Kunde inte skriva till CSV-fil: " + err.Error()})
			return
		}
	}
}

// ExportSRSMatbrevCSV exports all SRS mätbrev as a CSV file
func (h *Handler) ExportSRSMatbrevCSV(c *gin.Context) {
	// Get all SRS mätbrev
	matbrevs, err := h.DB.GetSRSMatbrev()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Set headers for CSV download
	filename := fmt.Sprintf("matbrev_%s.csv", time.Now().Format("2006-01-02"))
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

	// Create CSV writer
	writer := csv.NewWriter(c.Writer)
	// Set the separator
	writer.Comma = csvSeparator
	defer writer.Flush()

	// Write header row
	headers := []string{
		"Mätbrevs nr",
		"Båttyp",
		"Båtnamn",
		"Ägare",
		"Segelnr",
		"Nat",
		"SRS",
		"SRS utan undanvindsegel",
		"SRS S/H",
		"SRS S/H utan undanvindsegel",
	}
	if err := writer.Write(headers); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Kunde inte skapa CSV-fil: " + err.Error()})
		return
	}

	// Write data rows
	for _, mb := range matbrevs {
		row := []string{
			mb.MatbrevsNummer,
			mb.Battyp,
			mb.BatNamn,
			mb.Agare,
			mb.Segelnummer,
			mb.Nationality,
			fmt.Sprintf("%.3f", mb.SRS),
			fmt.Sprintf("%.3f", mb.SRSUtanUndanvindsegel),
			fmt.Sprintf("%.3f", mb.SRSShorthanded),
			fmt.Sprintf("%.3f", mb.SRSShorthandedUtanUndanvindsegel),
		}
		if err := writer.Write(row); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Kunde inte skriva till CSV-fil: " + err.Error()})
			return
		}
	}
}

// ImportSRSBoatTypesCSV imports SRS boat types from a CSV file
func (h *Handler) ImportSRSBoatTypesCSV(c *gin.Context) {
	// Get the file from the form
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		// Redirect back to the SRS management page with an error message
		c.Redirect(http.StatusSeeOther, "/srs?import_error=true&message=Ingen+fil+vald")
		return
	}
	defer file.Close()

	// Create CSV reader
	reader := csv.NewReader(file)

	// Detect the separator (comma or semicolon)
	// First, read a small sample to detect the separator
	sample := make([]byte, 1024)
	n, _ := file.Read(sample)
	if n > 0 {
		// Reset the file position
		if _, err := file.Seek(0, 0); err != nil {
			log.Printf("Error resetting file position: %v", err)
			// Continue anyway, we'll try to read from the current position
		}

		// Check if the sample contains more semicolons or commas
		semicolons := strings.Count(string(sample[:n]), ";")
		commas := strings.Count(string(sample[:n]), ",")

		if semicolons > commas {
			reader.Comma = ';'
		} else {
			reader.Comma = ','
		}
	} else {
		// Default to semicolon if we can't read a sample
		reader.Comma = ';'
	}

	// Read header row
	header, err := reader.Read()
	if err != nil {
		// Redirect back to the SRS management page with an error message
		c.Redirect(http.StatusSeeOther, "/srs?import_error=true&message=Kunde+inte+läsa+CSV-fil")
		return
	}

	// Sanity check: Verify the CSV has the expected number of columns (at least 5 for boat types)
	if len(header) < 5 {
		// Check if the header might be a single string with commas or semicolons
		if len(header) == 1 {
			// Try to split by comma
			if strings.Contains(header[0], ",") {
				parts := strings.Split(header[0], ",")
				if len(parts) >= 5 {
					// The header is valid, continue
					goto HeaderValid
				}
			}

			// Try to split by semicolon
			if strings.Contains(header[0], ";") {
				parts := strings.Split(header[0], ";")
				if len(parts) >= 5 {
					// The header is valid, continue
					goto HeaderValid
				}
			}
		}

		// If we get here, the header is invalid
		c.Redirect(http.StatusSeeOther, "/srs?import_error=true&message=CSV-filen+har+fel+format.+Förväntar+minst+5+kolumner")
		return
	}

HeaderValid:

	// Clear existing SRS boat types if requested
	clearExisting := c.PostForm("clear_existing") == "on"
	if clearExisting {
		err = h.DB.ClearSRSBoatTypes()
		if err != nil {
			// Redirect back to the SRS management page with an error message
			c.Redirect(http.StatusSeeOther, "/srs?import_error=true&message=Kunde+inte+rensa+befintliga+båttyper")
			return
		}
	}

	// Read and process data rows
	var importCount int
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Printf("Error reading CSV record: %v", err)
			continue
		}

		// Skip empty rows
		if len(record) == 0 {
			continue
		}

		// Special handling for the format "X-43,1.133,1.095,1.125,1.091"
		// This is a common format in the boat types CSV file
		if len(record) == 1 && strings.Contains(record[0], ",") {
			parts := strings.Split(record[0], ",")
			if len(parts) >= 5 {
				record = parts
			}
		}

		// Special handling for the format "X-43[1.133,1.095,1.125,1.091]"
		// This is another format we've seen in the logs
		if len(record) == 1 && strings.Contains(record[0], "[") && strings.Contains(record[0], "]") {
			parts := strings.Split(record[0], "[")
			if len(parts) == 2 {
				battyp := strings.TrimSpace(parts[0])
				// Remove the closing bracket and split by comma
				srsValues := strings.TrimRight(parts[1], "]")
				srsFields := strings.Split(srsValues, ",")

				if len(srsFields) >= 4 {
					// Reassign the record with the parsed values
					record = append([]string{battyp}, srsFields...)
				}
			}
		}

		// Clean up the record - remove any quotes and trim whitespace
		for i := range record {
			record[i] = strings.TrimSpace(record[i])
			record[i] = strings.Trim(record[i], "\"")
		}

		// Skip header rows that might be in the middle of the file
		if strings.Contains(strings.ToLower(record[0]), "båttyp") ||
			strings.Contains(strings.ToLower(record[0]), "battyp") {
			continue
		}

		// Ensure we have enough columns
		if len(record) < 5 {
			// Try to parse the first field to see if it contains multiple fields
			if len(record) > 0 {
				// Try semicolon first
				if strings.Contains(record[0], ";") {
					newRecord := strings.Split(record[0], ";")
					if len(newRecord) >= 5 {
						record = newRecord
					} else {
						log.Printf("Skipping record with insufficient columns after splitting by semicolon: %v", record)
						continue
					}
					// Then try comma
				} else if strings.Contains(record[0], ",") {
					newRecord := strings.Split(record[0], ",")
					if len(newRecord) >= 5 {
						record = newRecord
					} else {
						log.Printf("Skipping record with insufficient columns after splitting by comma: %v", record)
						continue
					}
				} else {
					log.Printf("Skipping record with insufficient columns: %v", record)
					continue
				}
			} else {
				log.Printf("Skipping record with insufficient columns: %v", record)
				continue
			}
		}

		// Parse the data
		battyp := record[0]

		// Handle potential formatting issues in numeric fields
		srs, srsErr := strconv.ParseFloat(strings.Replace(record[1], ",", ".", -1), 64)
		if srsErr != nil {
			srs = 0.0
		}

		srsUU, srsUUErr := strconv.ParseFloat(strings.Replace(record[2], ",", ".", -1), 64)
		if srsUUErr != nil {
			srsUU = 0.0
		}

		srsSH, srsSHErr := strconv.ParseFloat(strings.Replace(record[3], ",", ".", -1), 64)
		if srsSHErr != nil {
			srsSH = 0.0
		}

		srsSHUU, srsSHUUErr := strconv.ParseFloat(strings.Replace(record[4], ",", ".", -1), 64)
		if srsSHUUErr != nil {
			srsSHUU = 0.0
		}

		// Save to database
		err = h.DB.SaveSRSBoatType(battyp, srs, srsUU, srsSH, srsSHUU)
		if err != nil {
			log.Printf("Error saving boat type %s: %v", battyp, err)
			continue
		}

		importCount++
	}

	// Log the import
	if err := h.DB.LogSRSSync("srs_tabell", "success", fmt.Sprintf("Imported %d boat types from CSV", importCount)); err != nil {
		log.Printf("Error logging SRS sync: %v", err)
	}

	// Update all boats with the new SRS data
	log.Printf("ImportSRSBoatTypesCSV: Updating all boats with new SRS data")
	err = h.DB.UpdateAllBoatsFromSRSData()
	if err != nil {
		log.Printf("ImportSRSBoatTypesCSV: Error updating boats: %v", err)
		if err := h.DB.LogSRSSync("boats", "error", "Failed to update boats with new SRS data: "+err.Error()); err != nil {
			log.Printf("Error logging SRS sync: %v", err)
		}
	} else {
		if err := h.DB.LogSRSSync("boats", "success", "Successfully updated all boats with new SRS data"); err != nil {
			log.Printf("Error logging SRS sync: %v", err)
		}
	}

	// Redirect back to the SRS management page
	c.Redirect(http.StatusSeeOther, "/srs?import_success=true&count="+strconv.Itoa(importCount))
}

// ImportSRSDataCSV handles importing both SRS boat types and mätbrev from CSV files
func (h *Handler) ImportSRSDataCSV(c *gin.Context) {
	// Get the import type from the form
	importType := c.PostForm("import_type")

	// Validate import type
	if importType != "srs_tabell" && importType != "matbrev" {
		c.Redirect(http.StatusSeeOther, "/srs?import_error=true&message=Ogiltig+datatyp")
		return
	}

	// Call the appropriate import function based on the import type
	if importType == "srs_tabell" {
		h.ImportSRSBoatTypesCSV(c)
	} else {
		h.ImportSRSMatbrevCSV(c)
	}
}

// ImportSRSMatbrevCSV imports SRS mätbrev from a CSV file
func (h *Handler) ImportSRSMatbrevCSV(c *gin.Context) {
	// Get the file from the form
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		// Redirect back to the SRS management page with an error message
		c.Redirect(http.StatusSeeOther, "/srs?import_error=true&message=Ingen+fil+vald")
		return
	}
	defer file.Close()

	// Create CSV reader
	reader := csv.NewReader(file)

	// Detect the separator (comma or semicolon)
	// First, read a small sample to detect the separator
	sample := make([]byte, 1024)
	n, _ := file.Read(sample)
	if n > 0 {
		// Reset the file position
		if _, err := file.Seek(0, 0); err != nil {
			log.Printf("Error resetting file position: %v", err)
			// Continue anyway, we'll try to read from the current position
		}

		// Check if the sample contains more semicolons or commas
		semicolons := strings.Count(string(sample[:n]), ";")
		commas := strings.Count(string(sample[:n]), ",")

		if semicolons > commas {
			reader.Comma = ';'
		} else {
			reader.Comma = ','
		}
	} else {
		// Default to semicolon if we can't read a sample
		reader.Comma = ';'
	}

	// Read header row
	header, err := reader.Read()
	if err != nil {
		// Redirect back to the SRS management page with an error message
		c.Redirect(http.StatusSeeOther, "/srs?import_error=true&message=Kunde+inte+läsa+CSV-fil")
		return
	}

	// Sanity check: Verify the CSV has the expected number of columns (at least 10 for mätbrev)
	if len(header) < 10 {
		// Check if the header might be a single string with commas or semicolons
		if len(header) == 1 {
			// Try to split by comma
			if strings.Contains(header[0], ",") {
				parts := strings.Split(header[0], ",")
				if len(parts) >= 10 {
					// The header is valid, continue
					goto HeaderValid
				}
			}

			// Try to split by semicolon
			if strings.Contains(header[0], ";") {
				parts := strings.Split(header[0], ";")
				if len(parts) >= 10 {
					// The header is valid, continue
					goto HeaderValid
				}
			}
		}

		// If we get here, the header is invalid
		c.Redirect(http.StatusSeeOther, "/srs?import_error=true&message=CSV-filen+har+fel+format.+Förväntar+minst+10+kolumner")
		return
	}

HeaderValid:

	// Clear existing SRS mätbrev if requested
	clearExisting := c.PostForm("clear_existing") == "on"
	if clearExisting {
		err = h.DB.ClearSRSMatbrev()
		if err != nil {
			// Redirect back to the SRS management page with an error message
			c.Redirect(http.StatusSeeOther, "/srs?import_error=true&message=Kunde+inte+rensa+befintliga+mätbrev")
			return
		}
	}

	// Read and process data rows
	var importCount int
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Printf("Error reading CSV record: %v", err)
			continue
		}

		// Skip empty rows
		if len(record) == 0 {
			continue
		}

		// Clean up the record - remove any quotes and trim whitespace
		for i := range record {
			record[i] = strings.TrimSpace(record[i])
			record[i] = strings.Trim(record[i], "\"")
		}

		// Skip header rows that might be in the middle of the file
		if strings.Contains(strings.ToLower(record[0]), "mätbrev") ||
			strings.Contains(strings.ToLower(record[0]), "matbrev") {
			continue
		}

		// Ensure we have enough columns
		if len(record) < 10 {
			// Try to parse the first field to see if it contains multiple fields
			if len(record) > 0 {
				// Try semicolon first
				if strings.Contains(record[0], ";") {
					newRecord := strings.Split(record[0], ";")
					if len(newRecord) >= 10 {
						record = newRecord
					} else {
						log.Printf("Skipping record with insufficient columns after splitting by semicolon: %v", record)
						continue
					}
					// Then try comma
				} else if strings.Contains(record[0], ",") {
					newRecord := strings.Split(record[0], ",")
					if len(newRecord) >= 10 {
						record = newRecord
					} else {
						log.Printf("Skipping record with insufficient columns after splitting by comma: %v", record)
						continue
					}
				} else {
					log.Printf("Skipping record with insufficient columns: %v", record)
					continue
				}
			} else {
				log.Printf("Skipping record with insufficient columns: %v", record)
				continue
			}
		}

		// Parse the data
		matbrevsNummer := record[0]
		battyp := record[1]
		batNamn := record[2]
		agare := record[3]
		segelnummer := record[4]
		nationality := record[5]

		// Handle potential formatting issues in numeric fields
		srs, srsErr := strconv.ParseFloat(strings.Replace(record[6], ",", ".", -1), 64)
		if srsErr != nil {
			srs = 0.0
		}

		srsUU, srsUUErr := strconv.ParseFloat(strings.Replace(record[7], ",", ".", -1), 64)
		if srsUUErr != nil {
			srsUU = 0.0
		}

		srsSH, srsSHErr := strconv.ParseFloat(strings.Replace(record[8], ",", ".", -1), 64)
		if srsSHErr != nil {
			srsSH = 0.0
		}

		srsSHUU, srsSHUUErr := strconv.ParseFloat(strings.Replace(record[9], ",", ".", -1), 64)
		if srsSHUUErr != nil {
			srsSHUU = 0.0
		}

		// Save to database
		err = h.DB.SaveSRSMatbrev(matbrevsNummer, battyp, batNamn, agare, segelnummer, nationality, srs, srsUU, srsSH, srsSHUU)
		if err != nil {
			log.Printf("Error saving mätbrev %s: %v", matbrevsNummer, err)
			continue
		}

		importCount++
	}

	// Log the import
	if err := h.DB.LogSRSSync("matbrev", "success", fmt.Sprintf("Imported %d mätbrev from CSV", importCount)); err != nil {
		log.Printf("Error logging SRS sync: %v", err)
	}

	// Update all boats with the new SRS data
	log.Printf("ImportSRSMatbrevCSV: Updating all boats with new SRS data")
	err = h.DB.UpdateAllBoatsFromSRSData()
	if err != nil {
		log.Printf("ImportSRSMatbrevCSV: Error updating boats: %v", err)
		if err := h.DB.LogSRSSync("boats", "error", "Failed to update boats with new SRS data: "+err.Error()); err != nil {
			log.Printf("Error logging SRS sync: %v", err)
		}
	} else {
		if err := h.DB.LogSRSSync("boats", "success", "Successfully updated all boats with new SRS data"); err != nil {
			log.Printf("Error logging SRS sync: %v", err)
		}
	}

	// Redirect back to the SRS management page
	c.Redirect(http.StatusSeeOther, "/srs?import_success=true&count="+strconv.Itoa(importCount))
}
