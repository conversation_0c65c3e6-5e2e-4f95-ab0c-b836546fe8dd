package handlers

import (
	"fmt"
	"html/template"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rbjoregren/segling/pkg/database"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupResultsTestDB creates a clean test database with sample sailing competition data.
//
// This function:
// - Creates a new SQLite database file for testing (test_results.db)
// - Initializes all required tables and migrations
// - Populates the database with realistic test data via populateTestData()
// - Returns a database connection ready for testing
//
// The test database includes:
// - Sample sailors (<PERSON>, <PERSON><PERSON><PERSON><PERSON>) with different clubs
// - Sample boats (different types with SRS values)
// - One regular sailing event with boat-based participants
// - One entypsegling event with personal numbers and multiple heats
// - Heat results with placements for points-based scoring
//
// Note: The database file is automatically cleaned up by the calling test functions.
func setupResultsTestDB(t *testing.T) *database.DB {
	// Remove any existing test database
	os.Remove("test_results.db")

	// Create a new test database
	db, err := database.New("test_results.db")
	require.NoError(t, err)

	// Initialize the database
	err = db.Initialize()
	require.NoError(t, err)

	// Populate with test data
	_, _, _ = populateTestData(t, db)

	return db
}

// populateTestData creates comprehensive test data for sailing competition testing.
//
// This function creates two complete sailing competition scenarios:
//
// 1. Regular Sailing Event:
//   - Traditional sailing competition with boats and SRS handicapping
//   - Participants are associated with specific boats
//   - Uses SRS values for handicap calculations
//   - Single heat (default heat) for time-based results
//
// 2. Entypsegling Event:
//   - One-design sailing competition (all boats same type)
//   - Participants have personal sail numbers instead of boat assignments
//   - No SRS handicapping (placement-based scoring only)
//   - Multiple heats for points-based competition format
//   - Heat results with placements for realistic points calculation
//
// Test data includes:
// - 2 sailors from different clubs (LSS, KSSS)
// - 2 boats with different SRS values and types
// - Realistic event dates, start times, wind conditions
// - Heat structure with start times for entypsegling event
// - Sample heat placements to generate meaningful results
//
// Returns:
// - regularEventID: ID of the created regular sailing event (single heat)
// - entypEventID: ID of the created entypsegling event (multiple heats)
// - multiHeatEventID: ID of the created multi-heat regular sailing event
//
// This data structure allows comprehensive testing of:
// - Different event type handling in templates
// - Conditional column display logic
// - Points vs. time-based result calculations
// - Export format differences between event types
// - Multi-heat regular event handling
func populateTestData(t *testing.T, db *database.DB) (int64, int64, int64) {
	// Create test sailors
	sailor1ID, err := db.CreateSailor("Anna Svensson", "123456789", "LSS")
	require.NoError(t, err)

	sailor2ID, err := db.CreateSailor("Björn Karlsson", "987654321", "KSSS")
	require.NoError(t, err)

	// Create test boats
	boat1ID, err := db.CreateBoat("Vindkraft", "Laser", "MAT123", "SWE-101", "SWE", 1.05, 1.0, 0.95, 0.9)
	require.NoError(t, err)

	boat2ID, err := db.CreateBoat("Havsbris", "420", "MAT456", "SWE-102", "SWE", 1.1, 1.05, 1.0, 0.95)
	require.NoError(t, err)

	// Create regular sailing event (single heat)
	eventDate := time.Now().AddDate(0, 0, 7)
	regularEventID, err := db.CreateEvent("Test Regular Event", eventDate, "14:00", 5, 10, false, "Regatta", "Test regular sailing event")
	require.NoError(t, err)

	// Add participants to regular event
	regularParticipant1ID, err := db.AddParticipantToEvent(regularEventID, sailor1ID, boat1ID, "srs", 1.05, 0, false)
	require.NoError(t, err)

	regularParticipant2ID, err := db.AddParticipantToEvent(regularEventID, sailor2ID, boat2ID, "srs", 1.1, 0, false)
	require.NoError(t, err)

	// Add finish times for regular event participants (single heat uses default heat)
	// Get the default heat ID for the regular event
	var defaultHeatID int64
	err = db.QueryRow("SELECT id FROM heats WHERE event_id = ? AND heat_number = 1 LIMIT 1", regularEventID).Scan(&defaultHeatID)
	require.NoError(t, err)

	// Add finish times to the default heat for regular event (single-heat events need finish times, not just placements)
	// Participant 1 finishes first at 14:30:00
	_, err = db.Exec("INSERT INTO heat_finish_times (heat_id, participant_id, finish_time, created_at, updated_at) VALUES (?, ?, ?, datetime('now'), datetime('now'))",
		defaultHeatID, regularParticipant1ID, "14:30:00")
	require.NoError(t, err)

	// Participant 2 finishes second at 14:35:00
	_, err = db.Exec("INSERT INTO heat_finish_times (heat_id, participant_id, finish_time, created_at, updated_at) VALUES (?, ?, ?, datetime('now'), datetime('now'))",
		defaultHeatID, regularParticipant2ID, "14:35:00")
	require.NoError(t, err)

	// Create multi-heat regular sailing event
	multiHeatEventID, err := db.CreateEvent("Test Multi-Heat Regular Event", eventDate, "10:00", 5, 10, false, "Regatta", "Test multi-heat regular sailing event")
	require.NoError(t, err)

	// Add participants to multi-heat regular event
	_, err = db.AddParticipantToEvent(multiHeatEventID, sailor1ID, boat1ID, "srs", 1.05, 0, false)
	require.NoError(t, err)

	_, err = db.AddParticipantToEvent(multiHeatEventID, sailor2ID, boat2ID, "srs", 1.1, 0, false)
	require.NoError(t, err)

	// Create multiple heats for regular event
	regularHeat1ID, err := db.CreateHeat(multiHeatEventID, 1, "Heat 1", "10:00")
	require.NoError(t, err)

	regularHeat2ID, err := db.CreateHeat(multiHeatEventID, 2, "Heat 2", "11:30")
	require.NoError(t, err)

	// Add some placements to regular heats (for realistic multi-heat results)
	multiHeatParticipant1ID := int64(3) // Third participant (in multi-heat event)
	multiHeatParticipant2ID := int64(4) // Fourth participant (in multi-heat event)

	// Regular Heat 1 results
	err = db.UpdateHeatPlacement(regularHeat1ID, multiHeatParticipant1ID, 1, false, false)
	require.NoError(t, err)

	err = db.UpdateHeatPlacement(regularHeat1ID, multiHeatParticipant2ID, 2, false, false)
	require.NoError(t, err)

	// Regular Heat 2 results
	err = db.UpdateHeatPlacement(regularHeat2ID, multiHeatParticipant1ID, 2, false, false)
	require.NoError(t, err)

	err = db.UpdateHeatPlacement(regularHeat2ID, multiHeatParticipant2ID, 1, false, false)
	require.NoError(t, err)

	// Create entypsegling event
	entypEventID, err := db.CreateEvent("Test Entypsegling Event", eventDate, "15:00", 5, 10, false, "Test", "Test entypsegling event")
	require.NoError(t, err)

	// Set entypsegling properties
	_, err = db.Exec("UPDATE events SET entypsegling = 1, boat_type = 'Laser' WHERE id = ?", entypEventID)
	require.NoError(t, err)

	// Add participants to entypsegling event (no boats, personal numbers)
	_, err = db.AddParticipantToEvent(entypEventID, sailor1ID, 0, "entypsegling", 0, 0, false)
	require.NoError(t, err)

	// Set personal numbers for entypsegling participants
	_, err = db.Exec("UPDATE event_participants SET personal_number = 'SWE-201' WHERE event_id = ? AND sailor_id = ?", entypEventID, sailor1ID)
	require.NoError(t, err)

	_, err = db.AddParticipantToEvent(entypEventID, sailor2ID, 0, "entypsegling", 0, 0, false)
	require.NoError(t, err)

	_, err = db.Exec("UPDATE event_participants SET personal_number = 'SWE-202' WHERE event_id = ? AND sailor_id = ?", entypEventID, sailor2ID)
	require.NoError(t, err)

	// Create heats for entypsegling event
	heat1ID, err := db.CreateHeat(entypEventID, 1, "Heat 1", "15:00")
	require.NoError(t, err)

	heat2ID, err := db.CreateHeat(entypEventID, 2, "Heat 2", "16:00")
	require.NoError(t, err)

	// Add some placements to create meaningful results
	participant1ID := int64(1) // First participant
	participant2ID := int64(2) // Second participant

	// Heat 1 results
	err = db.UpdateHeatPlacement(heat1ID, participant1ID, 1, false, false)
	require.NoError(t, err)

	err = db.UpdateHeatPlacement(heat1ID, participant2ID, 2, false, false)
	require.NoError(t, err)

	// Heat 2 results
	err = db.UpdateHeatPlacement(heat2ID, participant1ID, 2, false, false)
	require.NoError(t, err)

	err = db.UpdateHeatPlacement(heat2ID, participant2ID, 1, false, false)
	require.NoError(t, err)

	return regularEventID, entypEventID, multiHeatEventID
}

// getTestEventIDs retrieves the database IDs of the created test events.
//
// This helper function queries the test database to find the IDs of the events
// created by populateTestData(). Since event IDs are auto-generated by the database,
// this function provides a way to dynamically discover the actual IDs for use in tests.
//
// The function distinguishes between event types using the 'entypsegling' column:
// - Regular event: entypsegling = 0 (traditional sailing with boats and SRS)
// - Entypsegling event: entypsegling = 1 (one-design with personal numbers)
//
// Returns:
// - regularEventID: ID of the regular sailing event (single heat)
// - entypEventID: ID of the entypsegling event
// - multiHeatEventID: ID of the multi-heat regular sailing event
//
// This approach allows tests to work regardless of the actual database-assigned IDs,
// making the tests more robust and independent of database state.
func getTestEventIDs(t *testing.T, db *database.DB) (int64, int64, int64) {
	// Get regular event ID (first single-heat event)
	var regularEventID int64
	err := db.QueryRow("SELECT id FROM events WHERE entypsegling = 0 AND namn = ? LIMIT 1", "Test Regular Event").Scan(&regularEventID)
	require.NoError(t, err)

	// Get entypsegling event ID
	var entypEventID int64
	err = db.QueryRow("SELECT id FROM events WHERE entypsegling = 1 LIMIT 1").Scan(&entypEventID)
	require.NoError(t, err)

	// Get multi-heat regular event ID
	var multiHeatEventID int64
	err = db.QueryRow("SELECT id FROM events WHERE entypsegling = 0 AND namn = ? LIMIT 1", "Test Multi-Heat Regular Event").Scan(&multiHeatEventID)
	require.NoError(t, err)

	return regularEventID, entypEventID, multiHeatEventID
}

// TestResultsDisplayContent verifies that result pages display the correct content for different event types.
//
// This test ensures that:
// - Regular sailing events show boat information, SRS values, and time columns
// - Entypsegling events show personal numbers and points, but hide boat type columns
// - Print versions maintain the same content rules as main results pages
// - Templates properly implement conditional column display logic
//
// The test creates realistic test data with:
// - Regular sailing event (ID dynamically assigned) with boat-based participants
// - Entypsegling event (ID dynamically assigned) with personal number participants
// - Multiple heats for entypsegling events to test points-based scoring
//
// Test scenarios cover:
// 1. Main results pages for both event types
// 2. Print versions for both event types
// 3. Verification of expected column headers and content
// 4. Verification that unwanted columns are properly hidden
//
// This test will FAIL until all templates properly implement entypsegling column hiding.
func TestResultsDisplayContent(t *testing.T) {
	// Initialize test database
	db := setupResultsTestDB(t)
	defer func() {
		db.Close()
		os.Remove("test_results.db")
	}()

	// Get test event IDs
	regularEventID, entypEventID, multiHeatEventID := getTestEventIDs(t, db)

	// Create handler with test database
	handler := &Handler{DB: db}

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name              string
		eventID           int64
		endpoint          string
		expectedInHTML    []string
		notExpectedInHTML []string
		description       string
	}{
		{
			// Test Case 1: Regular Sailing Event - Main Results Page
			// Verifies that traditional sailing competitions display boat-centric information
			// Expected: Boat names, SRS handicap values, and time-based results
			// Not Expected: Personal sail numbers (used only in entypsegling)
			name:     "Regular Sailing Event - Main Results",
			eventID:  regularEventID,
			endpoint: fmt.Sprintf("/events/%d/results", regularEventID),
			expectedInHTML: []string{
				"Båt",            // Boat column header - essential for regular events
				"SRS-värde",      // SRS value column header - handicap system
				"Måltid",         // Finish time column header - time-based scoring
				"Seglad tid",     // Elapsed time column header - race duration
				"Korrigerad tid", // Corrected time column header - handicap-adjusted time
			},
			notExpectedInHTML: []string{
				"Segelnummer", // Personal sail numbers not used in regular events
			},
			description: "Regular sailing event should show boat info, SRS values, and times",
		},
		{
			// Test Case 2: Entypsegling Event - Main Results Page
			// Verifies that one-design competitions display sailor-centric information
			// Expected: Personal sail numbers, crew info, and points-based results
			// Not Expected: Boat types (redundant), SRS values, or time columns
			name:     "Entypsegling Event - Main Results",
			eventID:  entypEventID,
			endpoint: fmt.Sprintf("/events/%d/results", entypEventID),
			expectedInHTML: []string{
				"Segelnummer",  // Personal sail number - unique identifier for entypsegling
				"Besättning",   // Crew column header - team composition
				"Totala poäng", // Total points column header - points-based scoring
			},
			notExpectedInHTML: []string{
				"Båttyp",         // Boat type redundant (all same type in one-design)
				"SRS-värde",      // SRS not used in one-design competitions
				"Måltid",         // Finish times not relevant for placement-based scoring
				"Seglad tid",     // Elapsed times not used in entypsegling
				"Korrigerad tid", // Corrected times not applicable without handicapping
			},
			description: "Entypsegling event should show personal numbers and points, not boat types or times",
		},
		{
			// Test Case 3: Regular Sailing Event - Print Version
			// Verifies that print templates maintain same content as main results
			// Expected: Same boat and time information as main results page
			// Not Expected: Personal sail numbers (not used in regular events)
			name:     "Regular Sailing Event - Print Version",
			eventID:  regularEventID,
			endpoint: fmt.Sprintf("/events/%d/results/print", regularEventID),
			expectedInHTML: []string{
				"Båt",        // Boat info essential for print version
				"SRS",        // SRS values needed for understanding results (abbreviated in print)
				"Måltid",     // Finish times for official records
				"Seglad tid", // Elapsed times for analysis
				"Korr. tid",  // Corrected times for final standings (abbreviated in print)
			},
			notExpectedInHTML: []string{
				"Segelnummer", // Personal numbers not applicable to regular events
			},
			description: "Print version of regular sailing event should show boat info and times",
		},
		{
			// Test Case 4: Entypsegling Event - Print Version
			// Verifies that print templates properly hide boat columns for entypsegling
			// Expected: Personal numbers and points (same as main results)
			// Not Expected: Boat types, SRS values, or any time columns
			// This test specifically targets the print template boat column hiding logic
			name:     "Entypsegling Event - Print Version",
			eventID:  entypEventID,
			endpoint: fmt.Sprintf("/events/%d/results/print?heat=overall", entypEventID),
			expectedInHTML: []string{
				"Segelnummer",  // Personal sail numbers for identification
				"Besättning",   // Crew information for team composition
				"Totala poäng", // Points totals for final standings
			},
			notExpectedInHTML: []string{
				"Båttyp",         // Boat type should be hidden (redundant in one-design)
				"SRS-värde",      // SRS not applicable to entypsegling
				"Måltid",         // Times not used in placement-based scoring
				"Seglad tid",     // Elapsed times irrelevant for entypsegling
				"Korrigerad tid", // Corrected times not used without handicapping
			},
			description: "Print version of entypsegling event should show personal numbers and points only",
		},
		{
			// Test Case 5: Multi-Heat Regular Sailing Event - Main Results Page
			// Verifies that multi-heat regular events display boat info with points columns
			// Expected: Boat information, SRS values, and points columns for each heat
			// Not Expected: Personal sail numbers (not used in regular events)
			// This tests the hybrid approach: boat-based with points scoring across heats
			name:     "Multi-Heat Regular Sailing Event - Main Results",
			eventID:  multiHeatEventID,
			endpoint: fmt.Sprintf("/events/%d/results", multiHeatEventID),
			expectedInHTML: []string{
				"Båt",          // Boat column header - essential for regular events
				"SRS-värde",    // SRS value column header - handicap system
				"Totala poäng", // Total points column header - multi-heat scoring
				"Heat 1",       // Heat 1 column header - individual heat results
				"Heat 2",       // Heat 2 column header - individual heat results
			},
			notExpectedInHTML: []string{
				"Segelnummer", // Personal sail numbers not used in regular events
			},
			description: "Multi-heat regular sailing event should show boat info, SRS values, and points",
		},
		{
			// Test Case 6: Multi-Heat Regular Sailing Event - Print Version
			// Verifies that print templates handle multi-heat regular events correctly
			// Expected: Same boat and points information as main results page
			// Not Expected: Personal sail numbers (not used in regular events)
			name:     "Multi-Heat Regular Sailing Event - Print Version",
			eventID:  multiHeatEventID,
			endpoint: fmt.Sprintf("/events/%d/results/print?heat=overall", multiHeatEventID),
			expectedInHTML: []string{
				"Båt",          // Boat info essential for print version
				"SRS",          // SRS values needed for understanding results (abbreviated in print)
				"Totala poäng", // Total points for final standings
				"Heat 1",       // Heat 1 results for detailed analysis
				"Heat 2",       // Heat 2 results for detailed analysis
			},
			notExpectedInHTML: []string{
				"Segelnummer", // Personal numbers not applicable to regular events
			},
			description: "Print version of multi-heat regular event should show boat info and points",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create request
			req, err := http.NewRequest("GET", tt.endpoint, nil)
			require.NoError(t, err)

			// Create response recorder
			w := httptest.NewRecorder()

			// Create Gin context
			router := gin.New()

			// Add template functions
			router.SetFuncMap(template.FuncMap{
				"add": func(a, b int) int {
					return a + b
				},
				"now": func() time.Time {
					return time.Now()
				},
				"split": strings.Split,
				"printf": func(format string, args ...interface{}) string {
					return fmt.Sprintf(format, args...)
				},
				"isDiscardEnabled": func(event interface{}) bool {
					if e, ok := event.(map[string]interface{}); ok {
						if discardAfterHeats, exists := e["DiscardAfterHeats"]; exists {
							if val, ok := discardAfterHeats.(int); ok {
								return val > 0
							}
						}
					}
					return false
				},
				"getDiscardCount": func(event interface{}, totalHeats int) int {
					if e, ok := event.(map[string]interface{}); ok {
						if discardAfterHeats, exists := e["DiscardAfterHeats"]; exists {
							if val, ok := discardAfterHeats.(int); ok && val > 0 {
								if totalHeats >= val {
									return totalHeats / val
								}
							}
						}
					}
					return 0
				},
			})

			router.LoadHTMLGlob("../templates/*")

			// Register the appropriate route
			if strings.Contains(tt.endpoint, "/print") {
				router.GET("/events/:id/results/print", handler.GetResultsPrint)
			} else {
				router.GET("/events/:id/results", handler.GetResults)
			}

			// Perform request
			router.ServeHTTP(w, req)

			// Check status code
			assert.Equal(t, http.StatusOK, w.Code, "Expected status 200 for %s", tt.description)

			// Get response body
			body := w.Body.String()

			// Check expected content is present
			for _, expected := range tt.expectedInHTML {
				assert.Contains(t, body, expected,
					"Expected '%s' to be present in %s", expected, tt.description)
			}

			// Check unwanted content is not present
			for _, notExpected := range tt.notExpectedInHTML {
				assert.NotContains(t, body, notExpected,
					"Expected '%s' to NOT be present in %s", notExpected, tt.description)
			}

			t.Logf("✅ %s: All content checks passed", tt.description)
		})
	}
}

// TestCSVExportContent verifies that CSV exports contain the correct columns for different event types.
//
// This test ensures that:
// - Regular sailing events include boat information, SRS values, and time columns in CSV
// - Entypsegling events include personal numbers and points, but exclude boat type columns
// - CSV headers match the expected format for each event type
// - CSV data rows contain the appropriate information based on event type
//
// The test creates the same realistic test data as TestResultsDisplayContent:
// - Regular sailing event with boat-based participants
// - Entypsegling event with personal number participants and multiple heats
//
// Test scenarios cover:
// 1. CSV export for regular sailing events (single heat)
// 2. CSV export for entypsegling events (overall results from multiple heats)
// 3. Verification of CSV headers and content format
// 4. Verification that unwanted columns are excluded from CSV
//
// CSV format expectations:
// - Regular events: Include "Båt", "SRS", time columns, exclude "Segelnummer"
// - Entypsegling events: Include "Segelnummer", "Totala poäng", exclude "Båttyp", "SRS"
//
// This test will FAIL until CSV export properly implements entypsegling column filtering.
func TestCSVExportContent(t *testing.T) {
	// Initialize test database
	db := setupResultsTestDB(t)
	defer func() {
		db.Close()
		os.Remove("test_results.db")
	}()

	// Get test event IDs
	regularEventID, entypEventID, multiHeatEventID := getTestEventIDs(t, db)

	// Create handler with test database
	handler := &Handler{DB: db}

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name             string
		eventID          int64
		endpoint         string
		expectedInCSV    []string
		notExpectedInCSV []string
		description      string
	}{
		{
			// CSV Test Case 1: Regular Sailing Event - CSV Export
			// Verifies that CSV exports include appropriate columns for regular events
			// Expected: Boat information and time columns for traditional sailing analysis
			// Not Expected: Personal sail numbers (not used in boat-based competitions)
			// CSV format should be suitable for spreadsheet analysis of race results
			name:     "Regular Sailing Event - CSV Export",
			eventID:  regularEventID,
			endpoint: fmt.Sprintf("/events/%d/results/csv", regularEventID),
			expectedInCSV: []string{
				"Båt",            // Boat names for identification in CSV
				"SRS",            // SRS values for handicap analysis
				"Måltid",         // Finish times for time-based calculations
				"Seglad tid",     // Elapsed times for performance analysis
				"Korrigerad tid", // Corrected times for final standings
			},
			notExpectedInCSV: []string{
				"Segelnummer", // Personal numbers not relevant for regular events
			},
			description: "CSV export of regular sailing event should include boat and time columns",
		},
		{
			// CSV Test Case 2: Entypsegling Event - CSV Export
			// Verifies that CSV exports properly exclude boat columns for entypsegling
			// Expected: Personal numbers and points for one-design competition analysis
			// Not Expected: Boat types, SRS values, or time columns (not relevant)
			// CSV should focus on placement-based results suitable for points analysis
			name:     "Entypsegling Event - CSV Export",
			eventID:  entypEventID,
			endpoint: fmt.Sprintf("/events/%d/results/csv?heat=overall", entypEventID),
			expectedInCSV: []string{
				"Segelnummer",  // Personal sail numbers for competitor identification
				"Besättning",   // Crew information for team analysis
				"Totala poäng", // Total points for final standings analysis
			},
			notExpectedInCSV: []string{
				"Båttyp",         // Boat type redundant (all same in one-design)
				"SRS",            // SRS values not applicable to entypsegling
				"Måltid",         // Finish times not used in placement scoring
				"Seglad tid",     // Elapsed times irrelevant for entypsegling
				"Korrigerad tid", // Corrected times not used without handicapping
			},
			description: "CSV export of entypsegling event should include personal numbers and points only",
		},
		{
			// CSV Test Case 3: Multi-Heat Regular Sailing Event - CSV Export
			// Verifies that CSV exports include boat info and points for multi-heat regular events
			// Expected: Boat information, SRS values, and points columns for analysis
			// Not Expected: Personal sail numbers (not used in boat-based competitions)
			// CSV should include both boat details and heat-by-heat points breakdown
			name:     "Multi-Heat Regular Sailing Event - CSV Export",
			eventID:  multiHeatEventID,
			endpoint: fmt.Sprintf("/events/%d/results/csv?heat=overall", multiHeatEventID),
			expectedInCSV: []string{
				"Båt",          // Boat names for identification in CSV
				"SRS",          // SRS values for handicap analysis
				"Totala poäng", // Total points for final standings analysis
				"Heat 1",       // Heat 1 points for detailed analysis
				"Heat 2",       // Heat 2 points for detailed analysis
			},
			notExpectedInCSV: []string{
				"Segelnummer", // Personal numbers not relevant for regular events
			},
			description: "CSV export of multi-heat regular event should include boat info and points",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create request
			req, err := http.NewRequest("GET", tt.endpoint, nil)
			require.NoError(t, err)

			// Create response recorder
			w := httptest.NewRecorder()

			// Create Gin context
			router := gin.New()

			// Add template functions (needed for some CSV operations)
			router.SetFuncMap(template.FuncMap{
				"add": func(a, b int) int {
					return a + b
				},
				"now": func() time.Time {
					return time.Now()
				},
				"split": strings.Split,
				"printf": func(format string, args ...interface{}) string {
					return fmt.Sprintf(format, args...)
				},
			})

			router.GET("/events/:id/results/csv", handler.ExportResultsCSV)

			// Perform request
			router.ServeHTTP(w, req)

			// Check status code
			assert.Equal(t, http.StatusOK, w.Code, "Expected status 200 for %s", tt.description)

			// Check content type
			assert.Equal(t, "text/csv", w.Header().Get("Content-Type"),
				"Expected CSV content type for %s", tt.description)

			// Get response body
			body := w.Body.String()

			// Check expected content is present
			for _, expected := range tt.expectedInCSV {
				assert.Contains(t, body, expected,
					"Expected '%s' to be present in CSV for %s", expected, tt.description)
			}

			// Check unwanted content is not present
			for _, notExpected := range tt.notExpectedInCSV {
				assert.NotContains(t, body, notExpected,
					"Expected '%s' to NOT be present in CSV for %s", notExpected, tt.description)
			}

			t.Logf("✅ %s: All CSV content checks passed", tt.description)
		})
	}
}
