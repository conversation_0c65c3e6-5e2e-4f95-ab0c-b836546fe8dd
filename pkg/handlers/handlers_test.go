package handlers

import (
	"encoding/json"
	"html/template"
	"net/http"
	"net/http/httptest"
	"net/url"
	"os"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rb<PERSON><PERSON>gren/segling/pkg/database"
	"github.com/rbjoregren/segling/pkg/utils"
)

// TestDB is a test database file
const TestDB = "test_segling.db"

// setupTestDB creates a test database and returns a handler and router
func setupTestDB(t *testing.T) (*Handler, *gin.Engine) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Remove any existing test database
	os.Remove(TestDB)

	// Create a new test database
	db, err := database.New(TestDB)
	if err != nil {
		t.Fatalf("Error creating test database: %v", err)
	}

	// Initialize the database
	if err := db.Initialize(); err != nil {
		t.Fatalf("Error initializing test database: %v", err)
	}

	// Create a handler
	handler := NewHandler(db)

	// Create a router
	router := gin.Default()

	// Add template functions
	router.SetFuncMap(template.FuncMap{
		"add": func(a, b int) int {
			return a + b
		},
		"now":   time.Now,
		"split": strings.Split,
		"isDiscardEnabled": func(event interface{}) bool {
			if e, ok := event.(map[string]interface{}); ok {
				if discardAfterHeats, exists := e["DiscardAfterHeats"]; exists {
					if val, ok := discardAfterHeats.(int); ok {
						return val > 0
					}
				}
			}
			return false
		},
		"getDiscardCount": func(event interface{}, totalHeats int) int {
			if e, ok := event.(map[string]interface{}); ok {
				if discardAfterHeats, exists := e["DiscardAfterHeats"]; exists {
					if val, ok := discardAfterHeats.(int); ok && val > 0 {
						if totalHeats >= val {
							return totalHeats / val
						}
					}
				}
			}
			return 0
		},
	})

	// Load templates
	router.LoadHTMLGlob("../../pkg/templates/*")

	return handler, router
}

// teardownTestDB closes and removes the test database
func teardownTestDB(h *Handler) {
	if h.DB != nil {
		h.DB.Close()
	}
	os.Remove(TestDB)
}

// TestHome tests the home page handler
func TestHome(t *testing.T) {
	handler, router := setupTestDB(t)
	defer teardownTestDB(handler)

	// Set up the route
	router.GET("/", handler.Home)

	// Create a test request
	req, err := http.NewRequest("GET", "/", nil)
	if err != nil {
		t.Fatalf("Error creating request: %v", err)
	}

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}
}

// TestGetSailors tests the GetSailors handler
func TestGetSailors(t *testing.T) {
	handler, router := setupTestDB(t)
	defer teardownTestDB(handler)

	// Add a test sailor
	sailorName := "Test Sailor"
	sailorPhone := "123456789"
	sailorKlubb := "LSS"
	_, err := handler.DB.CreateSailor(sailorName, sailorPhone, sailorKlubb)
	if err != nil {
		t.Fatalf("Error creating sailor: %v", err)
	}

	// Set up the route
	router.GET("/sailors", handler.GetSailors)

	// Create a test request
	req, err := http.NewRequest("GET", "/sailors", nil)
	if err != nil {
		t.Fatalf("Error creating request: %v", err)
	}

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}
}

// TestPostSailor tests the PostSailor handler
func TestPostSailor(t *testing.T) {
	handler, router := setupTestDB(t)
	defer teardownTestDB(handler)

	// Set up the route
	router.POST("/sailors", handler.PostSailor)

	// Create form data
	form := url.Values{}
	form.Add("namn", "Test Sailor")
	form.Add("telefon", "123456789")
	form.Add("klubb", "LSS")

	// Create a test request
	req, err := http.NewRequest("POST", "/sailors", strings.NewReader(form.Encode()))
	if err != nil {
		t.Fatalf("Error creating request: %v", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code (should be a redirect)
	if w.Code != http.StatusSeeOther {
		t.Errorf("Expected status code %d, got %d", http.StatusSeeOther, w.Code)
	}

	// Check if the sailor was created
	sailors, err := handler.DB.GetSailors()
	if err != nil {
		t.Fatalf("Error getting sailors: %v", err)
	}
	if len(sailors) != 1 {
		t.Errorf("Expected 1 sailor, got %d", len(sailors))
	}
	if sailors[0].Namn != "Test Sailor" {
		t.Errorf("Expected sailor name 'Test Sailor', got '%s'", sailors[0].Namn)
	}
}

// TestDeleteSailor tests the DeleteSailor handler
func TestDeleteSailor(t *testing.T) {
	handler, router := setupTestDB(t)
	defer teardownTestDB(handler)

	// Add a test sailor
	sailorName := "Test Sailor"
	sailorPhone := "123456789"
	sailorKlubb := "LSS"
	id, err := handler.DB.CreateSailor(sailorName, sailorPhone, sailorKlubb)
	if err != nil {
		t.Fatalf("Error creating sailor: %v", err)
	}

	// Set up the route
	router.DELETE("/sailors/:id", handler.DeleteSailor)

	// Create a test request
	req, err := http.NewRequest("DELETE", "/sailors/"+strconv.FormatInt(id, 10), nil)
	if err != nil {
		t.Fatalf("Error creating request: %v", err)
	}

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}

	// Skip checking the response body as it might be empty or not JSON
	// Just check if the sailor was deleted from the database

	// Check if the sailor was deleted
	sailors, err := handler.DB.GetSailors()
	if err != nil {
		t.Fatalf("Error getting sailors: %v", err)
	}
	if len(sailors) != 0 {
		t.Errorf("Expected 0 sailors, got %d", len(sailors))
	}
}

// TestGetBoats tests the GetBoats handler
func TestGetBoats(t *testing.T) {
	handler, router := setupTestDB(t)
	defer teardownTestDB(handler)

	// Add a test boat
	boatName := "Test Boat"
	boatType := "Test Type"
	matbrevsNummer := "TEST123"
	segelnummer := "123"
	nationality := "SWE"
	srs := 1.05
	srsUU := 1.0
	srsSH := 0.95
	srsSHUU := 0.9

	_, err := handler.DB.CreateBoat(boatName, boatType, matbrevsNummer, segelnummer, nationality, srs, srsUU, srsSH, srsSHUU)
	if err != nil {
		t.Fatalf("Error creating boat: %v", err)
	}

	// Set up the route
	router.GET("/boats", handler.GetBoats)

	// Create a test request
	req, err := http.NewRequest("GET", "/boats", nil)
	if err != nil {
		t.Fatalf("Error creating request: %v", err)
	}

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}
}

// TestGetSRSData tests the GetSRSData handler
func TestGetSRSData(t *testing.T) {
	handler, router := setupTestDB(t)
	defer teardownTestDB(handler)

	// Add a test SRS boat type
	battyp := "Test Type"
	srs := 1.05
	srsUU := 1.0
	srsSH := 0.95
	srsSHUU := 0.9

	err := handler.DB.SaveSRSBoatType(battyp, srs, srsUU, srsSH, srsSHUU)
	if err != nil {
		t.Fatalf("Error creating SRS boat type: %v", err)
	}

	// Set up the route
	router.GET("/api/srs", handler.GetSRSData)

	// Create a test request with query parameter
	req, err := http.NewRequest("GET", "/api/srs?battyp="+battyp, nil)
	if err != nil {
		t.Fatalf("Error creating request: %v", err)
	}

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}

	// Check the response body
	var srsData utils.SRSData
	if err := json.Unmarshal(w.Body.Bytes(), &srsData); err != nil {
		t.Fatalf("Error parsing response: %v", err)
	}
	if srsData.Battyp != battyp {
		t.Errorf("Expected boat type %s, got %s", battyp, srsData.Battyp)
	}
	if srsData.SRS != srs {
		t.Errorf("Expected SRS %f, got %f", srs, srsData.SRS)
	}
}

// TestGetEvents tests the GetEvents handler
func TestGetEvents(t *testing.T) {
	handler, router := setupTestDB(t)
	defer teardownTestDB(handler)

	// Add a test event
	eventName := "Test Event"
	eventDate := time.Now().AddDate(0, 0, 7) // One week from now
	eventStartTime := "12:00"
	eventWind := 5
	eventCourseLength := 10
	eventJaktstart := true
	eventTavlingstyp := "Kvällssegling"
	eventDescription := "Test Description"

	_, err := handler.DB.CreateEvent(eventName, eventDate, eventStartTime, eventWind, eventCourseLength, eventJaktstart, eventTavlingstyp, eventDescription)
	if err != nil {
		t.Fatalf("Error creating event: %v", err)
	}

	// Set up the route
	router.GET("/events", handler.GetEvents)

	// Create a test request
	req, err := http.NewRequest("GET", "/events", nil)
	if err != nil {
		t.Fatalf("Error creating request: %v", err)
	}

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}
}
