package handlers

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rbjoregren/segling/pkg/models"
	"github.com/rbjoregren/segling/pkg/services"
)

// GetGoogleDriveAuth handles the Google Drive authentication flow
func (h *Handler) GetGoogleDriveAuth(c *gin.Context) {
	// Check if client secrets file exists
	credentialsFile := "client_secrets.json"
	if _, err := os.Stat(credentialsFile); os.IsNotExist(err) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Client secrets file not found. Please upload client_secrets.json in settings.",
		})
		return
	}

	// Create Google Drive service
	gds, err := services.NewGoogleDriveService(credentialsFile, h.ServerHost, h.ServerPort, h.EnableTLS)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to create Google Drive service: %v", err),
		})
		return
	}

	// Check if already authenticated
	if gds.IsAuthenticated() {
		c.J<PERSON>(http.StatusOK, gin.H{
			"authenticated": true,
			"message":       "Already authenticated with Google Drive",
		})
		return
	}

	// Get authorization URL
	authURL := gds.GetAuthURL()
	c.JSON(http.StatusOK, gin.H{
		"authenticated": false,
		"auth_url":      authURL,
	})
}

// GetGoogleDriveCallback handles the OAuth callback
func (h *Handler) GetGoogleDriveCallback(c *gin.Context) {
	code := c.Query("code")
	if code == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Authorization code not found in callback",
		})
		return
	}

	// Create Google Drive service
	credentialsFile := "client_secrets.json"
	gds, err := services.NewGoogleDriveService(credentialsFile, h.ServerHost, h.ServerPort, h.EnableTLS)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Failed to create Google Drive service: %v", err),
		})
		return
	}

	// Exchange code for token
	err = gds.ExchangeCodeForToken(code)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Failed to exchange code for token: %v", err),
		})
		return
	}

	// Create a simple success page that redirects back to settings
	successHTML := `
<!DOCTYPE html>
<html>
<head>
    <title>Google Drive Authentication Success</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="text-success mb-3">
                            <i class="bi bi-check-circle" style="font-size: 3rem;"></i>
                        </div>
                        <h4 class="card-title text-success">Autentisering lyckades!</h4>
                        <p class="card-text">Google Drive integration är nu aktiverad. Du kan nu exportera tävlingsresultat till Google Drive.</p>
                        <p class="text-muted">Du kommer att omdirigeras till inställningssidan om 3 sekunder...</p>
                        <a href="/settings" class="btn btn-primary">Gå till inställningar</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        setTimeout(function() {
            window.location.href = '/settings';
        }, 3000);
    </script>
</body>
</html>`

	c.Data(http.StatusOK, "text/html; charset=utf-8", []byte(successHTML))
}

// PostGoogleDriveAuth handles the OAuth callback
func (h *Handler) PostGoogleDriveAuth(c *gin.Context) {
	code := c.PostForm("code")
	if code == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Authorization code is required",
		})
		return
	}

	// Create Google Drive service
	credentialsFile := "client_secrets.json"
	gds, err := services.NewGoogleDriveService(credentialsFile, h.ServerHost, h.ServerPort, h.EnableTLS)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to create Google Drive service: %v", err),
		})
		return
	}

	// Exchange code for token
	err = gds.ExchangeCodeForToken(code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to exchange code for token: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Successfully authenticated with Google Drive",
	})
}

// ExportResultsToGoogleDrive exports competition results to Google Drive
func (h *Handler) ExportResultsToGoogleDrive(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid event ID",
		})
		return
	}

	// Get the event
	event, err := h.DB.GetEvent(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Event not found",
		})
		return
	}

	// Check if Google Drive is enabled
	googleDriveEnabled, err := h.DB.GetSetting("google_drive_enabled")
	if err != nil || googleDriveEnabled != "true" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Google Drive integration is not enabled",
		})
		return
	}

	// Create Google Drive service
	credentialsFile := "client_secrets.json"
	gds, err := services.NewGoogleDriveService(credentialsFile, h.ServerHost, h.ServerPort, h.EnableTLS)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to create Google Drive service: %v", err),
		})
		return
	}

	// Check authentication
	if !gds.IsAuthenticated() {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Not authenticated with Google Drive. Please authenticate first.",
		})
		return
	}

	// Get heats for this event
	heats, err := h.DB.GetEventHeats(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to get heats: %v", err),
		})
		return
	}

	// Get selected heat from query parameter
	selectedHeatIDStr := c.Query("heat")
	var selectedHeat models.Heat
	var showOverall bool = false
	var isMultiHeat bool = len(heats) > 1

	if selectedHeatIDStr != "" {
		if selectedHeatIDStr == "overall" {
			showOverall = true
		} else {
			selectedHeatID, err := strconv.ParseInt(selectedHeatIDStr, 10, 64)
			if err == nil {
				for _, heat := range heats {
					if heat.ID == selectedHeatID {
						selectedHeat = heat
						break
					}
				}
			}
		}
	}

	// Determine what to show based on event type and selection
	if isMultiHeat {
		// For multi-heat events, default to overall if no specific heat selected
		if selectedHeat.ID == 0 && !showOverall {
			showOverall = true
		}
	} else {
		// For single-heat events, always show the single heat
		if len(heats) > 0 {
			selectedHeat = heats[0]
		}
		showOverall = false
	}

	// Get results data
	var savedResults []models.SavedResult
	var usingSavedResults bool
	var totalResults []models.TotalResult
	var heatResults []models.HeatResult

	// Get the appropriate results based on selection
	if event.Locked {
		savedResults, err = h.DB.GetSavedResults(id)
		if err != nil {
			// Fall back to calculated results
			event.Locked = false // Temporarily treat as unlocked for fallback
		} else {
			usingSavedResults = true
		}
	}

	if !event.Locked {
		// Calculate results based on selection
		if showOverall && isMultiHeat {
			totalResults, err = h.DB.GetEventTotalResults(id)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"error": fmt.Sprintf("Failed to get results: %v", err),
				})
				return
			}
		} else if selectedHeat.ID != 0 {
			heatResults, err = h.DB.GetHeatResultsComplete(selectedHeat.ID)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"error": fmt.Sprintf("Failed to get results: %v", err),
				})
				return
			}
		} else {
			heatResults = []models.HeatResult{}
		}
	}

	// Get folder ID from settings
	folderID, err := h.DB.GetSetting("google_drive_folder_id")
	if err != nil {
		folderID = "" // Use root folder if not specified
	}

	// Get naming convention from settings
	namingConvention, err := h.DB.GetSetting("google_drive_naming_convention")
	if err != nil {
		namingConvention = "{{event_name}}_{{date}}" // Default naming
	}

	// Generate filename based on naming convention and heat selection
	filename := h.generateGoogleDriveFilename(namingConvention, event, showOverall, selectedHeat, isMultiHeat)

	// Create spreadsheet
	spreadsheetID, err := gds.CreateSpreadsheet(filename, folderID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to create spreadsheet: %v", err),
		})
		return
	}

	// Prepare data for spreadsheet
	var data [][]interface{}
	if usingSavedResults {
		data = h.formatSavedResultsForSpreadsheet(savedResults, event)
	} else if showOverall && isMultiHeat {
		data = h.formatTotalResultsForSpreadsheet(totalResults, event, heats)
	} else if selectedHeat.ID != 0 {
		// Convert heat results to regular results format for export
		results := h.convertHeatResultsToResultsForExport(heatResults)
		data = h.formatResultsForSpreadsheet(results, event, selectedHeat)
	} else {
		// No valid data to export
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "No valid heat or overall selection for export",
		})
		return
	}

	// Write data to spreadsheet
	err = gds.WriteToSpreadsheet(spreadsheetID, "Sheet1", data)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to write data to spreadsheet: %v", err),
		})
		return
	}

	// Generate the Google Sheets URL
	spreadsheetURL := fmt.Sprintf("https://docs.google.com/spreadsheets/d/%s/edit", spreadsheetID)

	c.JSON(http.StatusOK, gin.H{
		"success":         true,
		"message":         "Results exported to Google Drive successfully",
		"spreadsheet_id":  spreadsheetID,
		"spreadsheet_url": spreadsheetURL,
		"filename":        filename,
	})
}

// generateGoogleDriveFilename generates a filename based on the naming convention
func (h *Handler) generateGoogleDriveFilename(convention string, event models.Event, showOverall bool, selectedHeat models.Heat, isMultiHeat bool) string {
	filename := convention

	// Replace placeholders
	filename = strings.ReplaceAll(filename, "{{event_name}}", event.Namn)
	filename = strings.ReplaceAll(filename, "{{date}}", event.Datum.Format("2006-01-02"))
	filename = strings.ReplaceAll(filename, "{{competition_type}}", event.Tavlingstyp)
	filename = strings.ReplaceAll(filename, "{{year}}", event.Datum.Format("2006"))
	filename = strings.ReplaceAll(filename, "{{month}}", event.Datum.Format("01"))
	filename = strings.ReplaceAll(filename, "{{day}}", event.Datum.Format("02"))

	// If no placeholders were replaced, use default format with heat information
	if filename == convention {
		if showOverall && isMultiHeat {
			filename = fmt.Sprintf("%s_%s_totala", event.Namn, event.Datum.Format("2006-01-02"))
		} else if selectedHeat.ID != 0 && isMultiHeat {
			// Only include heat name for multi-heat events
			cleanHeatName := strings.ReplaceAll(selectedHeat.Name, " ", "_")
			cleanHeatName = strings.ReplaceAll(cleanHeatName, "/", "_")
			cleanHeatName = strings.ReplaceAll(cleanHeatName, "\\", "_")
			filename = fmt.Sprintf("%s_%s_%s", event.Namn, event.Datum.Format("2006-01-02"), cleanHeatName)
		} else {
			// Single heat events or no specific heat selected
			filename = fmt.Sprintf("%s_%s", event.Namn, event.Datum.Format("2006-01-02"))
		}
	} else {
		// Add heat suffix to custom naming convention
		if showOverall && isMultiHeat {
			filename = filename + "_totala"
		} else if selectedHeat.ID != 0 && isMultiHeat {
			// Only include heat name for multi-heat events
			cleanHeatName := strings.ReplaceAll(selectedHeat.Name, " ", "_")
			cleanHeatName = strings.ReplaceAll(cleanHeatName, "/", "_")
			cleanHeatName = strings.ReplaceAll(cleanHeatName, "\\", "_")
			filename = filename + "_" + cleanHeatName
		}
		// For single heat events, don't add any suffix
	}

	return filename
}

// formatResultsForSpreadsheet formats regular results for Google Sheets
func (h *Handler) formatResultsForSpreadsheet(results []models.Result, event models.Event, selectedHeat models.Heat) [][]interface{} {
	var data [][]interface{}

	// Add title and event info
	var title string
	if selectedHeat.ID != 0 {
		title = fmt.Sprintf("Resultat - %s - %s", event.Namn, selectedHeat.Name)
	} else {
		title = fmt.Sprintf("Resultat - %s", event.Namn)
	}
	data = append(data, []interface{}{title})
	data = append(data, []interface{}{fmt.Sprintf("Datum: %s", event.Datum.Format("2006-01-02"))})
	data = append(data, []interface{}{fmt.Sprintf("Starttid: %s", event.Starttid)})
	data = append(data, []interface{}{fmt.Sprintf("Vind: %d m/s", event.Vind)})
	data = append(data, []interface{}{fmt.Sprintf("Banlängd: %d nm", event.Banlangd)})
	data = append(data, []interface{}{}) // Empty row

	// Add headers
	headers := []interface{}{
		"Placering", "Seglare", "Klubb", "Båt", "Båttyp", "Segelnummer",
		"Mätbrevsnummer", "SRS-värde", "SRS-typ", "Besättning", "Starttid",
		"Måltid", "Seglad tid", "Korrigerad tid", "Efter föregående", "Efter vinnare",
	}
	data = append(data, headers)

	// Add result rows
	for i, result := range results {
		placement := strconv.Itoa(i + 1)
		if result.DNS {
			placement = "DNS"
		} else if result.DNF {
			placement = "DNF"
		}

		// Format sail number with nationality
		sailNumber := fmt.Sprintf("%s-%s", result.Boat.Nationality, result.Boat.Segelnummer)
		if result.Boat.Nationality == "" || result.Boat.Segelnummer == "" {
			sailNumber = result.Boat.Segelnummer
		}

		// Format SRS value and type for display (same logic as results page)
		var srsValue string
		var srsTypeDisplay string
		if result.UseCustomSRSValue {
			srsValue = fmt.Sprintf("%.3f", result.CustomSRSValue)
			srsTypeDisplay = "anpassad"
		} else {
			srsValue = fmt.Sprintf("%.3f", result.SelectedSRSValue)
			switch result.SRSType {
			case "srs":
				srsTypeDisplay = "SRS"
			case "srs_utan_undanvindsegel":
				srsTypeDisplay = "SRS utan undanvindsegel"
			case "srs_shorthanded":
				srsTypeDisplay = "SRS S/H"
			case "srs_shorthanded_utan_undanvindsegel":
				srsTypeDisplay = "SRS S/H utan undanvindsegel"
			default:
				srsTypeDisplay = "Standard"
			}
		}

		row := []interface{}{
			placement,
			result.Sailor.Namn,
			result.Sailor.Klubb,
			result.Boat.Namn,
			result.Boat.Battyp,
			sailNumber,
			result.Boat.MatbrevsNummer,
			srsValue,
			srsTypeDisplay,
			result.TotalPersons,
			result.StartTime,
			result.FinishTime,
			result.ElapsedTime,
			result.CorrectedTime,
			result.TimeToPrevious,
			result.TimeToWinner,
		}
		data = append(data, row)
	}

	return data
}

// formatSavedResultsForSpreadsheet formats saved results for Google Sheets
func (h *Handler) formatSavedResultsForSpreadsheet(savedResults []models.SavedResult, event models.Event) [][]interface{} {
	var data [][]interface{}

	// Add title and event info
	data = append(data, []interface{}{fmt.Sprintf("Resultat - %s", event.Namn)})
	data = append(data, []interface{}{fmt.Sprintf("Datum: %s", event.Datum.Format("2006-01-02"))})
	data = append(data, []interface{}{fmt.Sprintf("Starttid: %s", event.Starttid)})
	data = append(data, []interface{}{fmt.Sprintf("Vind: %d m/s", event.Vind)})
	data = append(data, []interface{}{fmt.Sprintf("Banlängd: %d nm", event.Banlangd)})
	data = append(data, []interface{}{}) // Empty row

	// Add headers
	headers := []interface{}{
		"Placering", "Seglare", "Klubb", "Båt", "Båttyp", "Segelnummer",
		"Mätbrevsnummer", "SRS-värde", "SRS-typ", "Besättning", "Starttid",
		"Måltid", "Seglad tid", "Korrigerad tid", "Efter föregående", "Efter vinnare",
	}
	data = append(data, headers)

	// Add result rows
	for i, result := range savedResults {
		placement := strconv.Itoa(i + 1)
		if result.DNS {
			placement = "DNS"
		} else if result.DNF {
			placement = "DNF"
		}

		// Format sail number with nationality
		sailNumber := fmt.Sprintf("%s-%s", result.Nationality, result.Segelnummer)
		if result.Nationality == "" || result.Segelnummer == "" {
			sailNumber = result.Segelnummer
		}

		// Format SRS value and type for display (same logic as results page)
		var srsValue string
		var srsTypeDisplay string
		if result.UseCustomSRSValue {
			srsValue = fmt.Sprintf("%.3f", result.SRSValue)
			srsTypeDisplay = "anpassad"
		} else {
			srsValue = fmt.Sprintf("%.3f", result.SRSValue)
			switch result.SRSType {
			case "srs":
				srsTypeDisplay = "SRS"
			case "srs_utan_undanvindsegel":
				srsTypeDisplay = "SRS utan undanvindsegel"
			case "srs_shorthanded":
				srsTypeDisplay = "SRS S/H"
			case "srs_shorthanded_utan_undanvindsegel":
				srsTypeDisplay = "SRS S/H utan undanvindsegel"
			default:
				srsTypeDisplay = "Standard"
			}
		}

		row := []interface{}{
			placement,
			result.SailorName,
			result.SailorClub,
			result.BoatName,
			result.BoatType,
			sailNumber,
			result.MatbrevsNummer,
			srsValue,
			srsTypeDisplay,
			result.CrewCount + 1, // Add 1 for the sailor
			result.StartTime,
			result.FinishTime,
			result.ElapsedTime,
			result.CorrectedTime,
			result.TimeToPrevious,
			result.TimeToWinner,
		}
		data = append(data, row)
	}

	return data
}

// convertHeatResultsToResultsForExport converts HeatResult to Result format for exporting
func (h *Handler) convertHeatResultsToResultsForExport(heatResults []models.HeatResult) []models.Result {
	var results []models.Result

	for _, hr := range heatResults {
		// Create a copy of the event participant and set the finish time from the heat result
		eventParticipant := hr.EventParticipant
		eventParticipant.FinishTime = hr.FinishTime
		eventParticipant.DNS = hr.DNS
		eventParticipant.DNF = hr.DNF

		result := models.Result{
			EventParticipant:      eventParticipant,
			Sailor:                hr.Sailor,
			Boat:                  hr.Boat,
			StartTime:             hr.StartTime,
			ElapsedTime:           hr.ElapsedTime,
			CorrectedTime:         hr.CorrectedTime,
			ElapsedSeconds:        hr.ElapsedSeconds,
			CorrectedSeconds:      hr.CorrectedSeconds,
			CorrectedSecondsFloat: hr.CorrectedSecondsFloat,
			TimeToPrevious:        hr.TimeToPrevious,
			TimeToWinner:          hr.TimeToWinner,
			TotalPersons:          hr.TotalPersons,
			DNS:                   hr.DNS,
			DNF:                   hr.DNF,
		}
		results = append(results, result)
	}

	return results
}

// formatTotalResultsForSpreadsheet formats total results for Google Sheets
func (h *Handler) formatTotalResultsForSpreadsheet(totalResults []models.TotalResult, event models.Event, heats []models.Heat) [][]interface{} {
	var data [][]interface{}

	// Add title and event info
	data = append(data, []interface{}{fmt.Sprintf("Resultat - %s - Totala resultat", event.Namn)})
	data = append(data, []interface{}{fmt.Sprintf("Datum: %s", event.Datum.Format("2006-01-02"))})
	data = append(data, []interface{}{fmt.Sprintf("Starttid: %s", event.Starttid)})
	data = append(data, []interface{}{fmt.Sprintf("Vind: %d m/s", event.Vind)})
	data = append(data, []interface{}{fmt.Sprintf("Banlängd: %d nm", event.Banlangd)})

	// Add discard information if enabled
	if event.DiscardAfterHeats > 0 {
		discardCount := 0
		if len(heats) >= event.DiscardAfterHeats {
			discardCount = len(heats) / event.DiscardAfterHeats
		}
		if discardCount > 0 {
			data = append(data, []interface{}{fmt.Sprintf("Borträkning: %d sämsta resultat räknas bort per deltagare. (B) = Borträknad", discardCount)})
		}
	}

	data = append(data, []interface{}{}) // Empty row

	// Add headers for multi-heat results
	headers := []interface{}{
		"Placering", "Seglare", "Klubb", "Båt", "Båttyp", "Segelnummer",
		"Mätbrevsnummer", "SRS-värde", "SRS-typ", "Besättning", "Totala poäng",
	}

	// Add heat names as additional columns
	for _, heat := range heats {
		headers = append(headers, heat.Name)
	}

	data = append(data, headers)

	// Add result rows
	for _, result := range totalResults {
		// Format sail number with nationality
		sailNumber := fmt.Sprintf("%s-%s", result.Boat.Nationality, result.Boat.Segelnummer)
		if result.Boat.Nationality == "" || result.Boat.Segelnummer == "" {
			sailNumber = result.Boat.Segelnummer
		}

		// Format SRS value and type for display (same logic as results page)
		var srsValue string
		var srsTypeDisplay string
		if result.EventParticipant.UseCustomSRSValue {
			srsValue = fmt.Sprintf("%.3f", result.EventParticipant.CustomSRSValue)
			srsTypeDisplay = "anpassad"
		} else {
			srsValue = fmt.Sprintf("%.3f", result.EventParticipant.SelectedSRSValue)
			switch result.EventParticipant.SRSType {
			case "srs":
				srsTypeDisplay = "SRS"
			case "srs_utan_undanvindsegel":
				srsTypeDisplay = "SRS utan undanvindsegel"
			case "srs_shorthanded":
				srsTypeDisplay = "SRS S/H"
			case "srs_shorthanded_utan_undanvindsegel":
				srsTypeDisplay = "SRS S/H utan undanvindsegel"
			default:
				srsTypeDisplay = "Standard"
			}
		}

		row := []interface{}{
			result.TotalPosition,
			result.Sailor.Namn,
			result.Sailor.Klubb,
			result.Boat.Namn,
			result.Boat.Battyp,
			sailNumber,
			result.Boat.MatbrevsNummer,
			srsValue,
			srsTypeDisplay,
			result.EventParticipant.CrewCount + 1, // Add 1 for the sailor
			result.TotalPoints,
		}

		// Add individual heat results
		for _, heat := range heats {
			found := false
			for _, heatResult := range result.HeatResults {
				if heatResult.Heat.ID == heat.ID {
					found = true
					// Check if this heat is discarded
					isDiscarded := false
					for _, discardedHeatID := range result.DiscardedHeats {
						if discardedHeatID == heat.ID {
							isDiscarded = true
							break
						}
					}

					var cellValue string
					if heatResult.DNS {
						cellValue = fmt.Sprintf("DNS (%.1fp)", heatResult.Points)
					} else if heatResult.DNF {
						cellValue = fmt.Sprintf("DNF (%.1fp)", heatResult.Points)
					} else {
						cellValue = fmt.Sprintf("%d (%.1fp)", heatResult.Position, heatResult.Points)
					}

					// Mark discarded heats with suffix
					if isDiscarded {
						cellValue = cellValue + " (B)"
					}

					row = append(row, cellValue)
					break
				}
			}
			if !found {
				row = append(row, "-")
			}
		}

		data = append(data, row)
	}

	return data
}
