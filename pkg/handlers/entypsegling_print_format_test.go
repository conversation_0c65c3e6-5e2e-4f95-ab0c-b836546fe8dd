package handlers

import (
	"fmt"
	"html/template"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestEntypSeglingPrintFormatting tests that entypsegling print templates show:
// 1. All points with one decimal precision (e.g., "1.0", "2.0", "5.0")
// 2. DNS/DNF points in parentheses beside the status (e.g., "DNS (7.0)", "DNF (5.0)")
func TestEntypSeglingPrintFormatting(t *testing.T) {
	// Setup test database
	db := setupResultsTestDB(t)
	defer func() {
		db.Close()
		os.Remove("test_entypsegling_print.db")
	}()

	// Create handler
	handler := &Handler{DB: db}

	// Create test entypsegling event
	eventID, err := db.CreateEventWithEntypsegling(
		"Test Entypsegling Print Format", time.Now(), "12:00", 5, 10, false,
		"Test", "Test Description", true, "Test Boat Type")
	require.NoError(t, err)

	// Create test sailors
	sailor1ID, err := db.CreateSailor("Sailor 1", "123456789", "Test Club")
	require.NoError(t, err)
	sailor2ID, err := db.CreateSailor("Sailor 2", "987654321", "Test Club")
	require.NoError(t, err)
	sailor3ID, err := db.CreateSailor("Sailor 3", "555666777", "Test Club")
	require.NoError(t, err)
	sailor4ID, err := db.CreateSailor("Sailor 4", "111222333", "Test Club")
	require.NoError(t, err)

	// Create participants
	participant1ID, err := db.AddEntypSeglingParticipantToEvent(eventID, sailor1ID, "SWE 1", 0)
	require.NoError(t, err)
	participant2ID, err := db.AddEntypSeglingParticipantToEvent(eventID, sailor2ID, "SWE 2", 0)
	require.NoError(t, err)
	participant3ID, err := db.AddEntypSeglingParticipantToEvent(eventID, sailor3ID, "SWE 3", 0)
	require.NoError(t, err)
	participant4ID, err := db.AddEntypSeglingParticipantToEvent(eventID, sailor4ID, "SWE 4", 0)
	require.NoError(t, err)

	// Create two heats for multi-heat testing
	heat1ID, err := db.CreateHeat(eventID, 1, "Heat 1", "12:00")
	require.NoError(t, err)
	heat2ID, err := db.CreateHeat(eventID, 2, "Heat 2", "13:00")
	require.NoError(t, err)

	// Heat 1 results: 1st place, 2nd place, DNF, DNS
	err = db.UpdateHeatPlacement(heat1ID, participant1ID, 1, false, false) // 1st place (1.0 points)
	require.NoError(t, err)
	err = db.UpdateHeatPlacement(heat1ID, participant2ID, 2, false, false) // 2nd place (2.0 points)
	require.NoError(t, err)
	err = db.UpdateHeatPlacement(heat1ID, participant3ID, 0, false, true) // DNF (3.0 points = last finisher + 1)
	require.NoError(t, err)
	err = db.UpdateHeatPlacement(heat1ID, participant4ID, 0, true, false) // DNS (5.0 points = total participants + 1)
	require.NoError(t, err)

	// Heat 2 results: 2nd place, 1st place, DNS, DNF
	err = db.UpdateHeatPlacement(heat2ID, participant1ID, 2, false, false) // 2nd place (2.0 points)
	require.NoError(t, err)
	err = db.UpdateHeatPlacement(heat2ID, participant2ID, 1, false, false) // 1st place (1.0 points)
	require.NoError(t, err)
	err = db.UpdateHeatPlacement(heat2ID, participant3ID, 0, true, false) // DNS (5.0 points)
	require.NoError(t, err)
	err = db.UpdateHeatPlacement(heat2ID, participant4ID, 0, false, true) // DNF (3.0 points)
	require.NoError(t, err)

	// Test cases for different print views
	testCases := []struct {
		name           string
		url            string
		expectedPoints []string
		expectedDNS    []string
		expectedDNF    []string
	}{
		{
			name: "Overall Results Print",
			url:  fmt.Sprintf("/events/%d/results/print?heat=overall", eventID),
			expectedPoints: []string{
				"3.0", // Total points with one decimal
				"3.0", // Total points with one decimal
				"8.0", // Total points with one decimal
				"8.0", // Total points with one decimal
			},
			expectedDNS: []string{
				"DNS (5.0p)", // DNS with points in parentheses
			},
			expectedDNF: []string{
				"DNF (3.0p)", // DNF with points in parentheses
			},
		},
		{
			name: "Heat 1 Results Print",
			url:  fmt.Sprintf("/events/%d/results/print?heat=%d", eventID, heat1ID),
			expectedPoints: []string{
				"1.0", // Individual heat points with one decimal
				"2.0", // Individual heat points with one decimal
				"3.0", // DNF points with one decimal
				"5.0", // DNS points with one decimal
			},
			expectedDNS: []string{
				// DNS shown as badge in individual heat view
			},
			expectedDNF: []string{
				// DNF shown as badge in individual heat view
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup Gin router
			gin.SetMode(gin.TestMode)
			router := gin.New()

			// Add template functions
			router.SetFuncMap(template.FuncMap{
				"add": func(a, b int) int {
					return a + b
				},
				"now": func() time.Time {
					return time.Now()
				},
				"split": strings.Split,
				"printf": func(format string, args ...interface{}) string {
					return fmt.Sprintf(format, args...)
				},
				"isDiscardEnabled": func(event interface{}) bool {
					if e, ok := event.(map[string]interface{}); ok {
						if discardAfterHeats, exists := e["DiscardAfterHeats"]; exists {
							if val, ok := discardAfterHeats.(int); ok {
								return val > 0
							}
						}
					}
					return false
				},
				"getDiscardCount": func(event interface{}, totalHeats int) int {
					if e, ok := event.(map[string]interface{}); ok {
						if discardAfterHeats, exists := e["DiscardAfterHeats"]; exists {
							if val, ok := discardAfterHeats.(int); ok && val > 0 {
								if totalHeats >= val {
									return totalHeats / val
								}
							}
						}
					}
					return 0
				},
			})

			router.LoadHTMLGlob("../templates/*")

			// Register route
			router.GET("/events/:id/results/print", handler.GetResultsPrint)

			// Create request
			req, err := http.NewRequest("GET", tc.url, nil)
			require.NoError(t, err)

			// Create response recorder
			w := httptest.NewRecorder()

			// Perform request
			router.ServeHTTP(w, req)

			// Check status code
			assert.Equal(t, http.StatusOK, w.Code, "Expected status 200 for %s", tc.name)

			// Get response body
			body := w.Body.String()

			// Verify all expected points are formatted with one decimal
			for _, expectedPoint := range tc.expectedPoints {
				assert.Contains(t, body, expectedPoint,
					"Expected point '%s' to be present with one decimal precision in %s", expectedPoint, tc.name)
			}

			// Verify DNS points are shown in parentheses
			for _, expectedDNS := range tc.expectedDNS {
				assert.Contains(t, body, expectedDNS,
					"Expected DNS format '%s' to be present in %s", expectedDNS, tc.name)
			}

			// Verify DNF points are shown in parentheses
			for _, expectedDNF := range tc.expectedDNF {
				assert.Contains(t, body, expectedDNF,
					"Expected DNF format '%s' to be present in %s", expectedDNF, tc.name)
			}

			// Verify no points are shown without decimal precision in parentheses
			// This checks that we don't have points like "1p)" or "2p)" without decimals
			badPointFormats := []string{
				"1p)", "2p)", "3p)", "4p)", "5p)", // Points without decimals in parentheses
				"(1p)", "(2p)", "(3p)", "(4p)", "(5p)", // Points without decimals in parentheses
			}
			for _, badFormat := range badPointFormats {
				assert.NotContains(t, body, badFormat,
					"Found bad point format '%s' in %s - all points should have one decimal precision", badFormat, tc.name)
			}

			// Debug: Print part of the response for manual verification
			t.Logf("Response snippet for %s:\n%s", tc.name, body[:min(500, len(body))])
		})
	}
}

// Helper function for min
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
