package handlers

// HTML templates for GitHub Pages
// These templates are used for generating GitHub Pages content
// They are separate from the main application templates
const defaultTemplate = `<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>{{ .title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        body {
            padding: 20px;
            font-size: 0.9rem;
        }
        /* Table styling for better readability */
        .table {
            font-size: 9pt;
            table-layout: fixed;
            width: 100%;
            margin-bottom: 1rem;
        }
        .table th {
            font-size: 9pt;
            font-weight: bold;
            padding: 0.3rem;
            background-color: #f8f9fa;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .table td {
            padding: 0.3rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .table-striped > tbody > tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.18) !important;
        }

        /* Column width definitions */
        .col-plac {
            width: 40px;
        }
        .col-seglare {
            width: 120px;
        }
        .col-klubb {
            width: 50px;
        }
        .col-battyp {
            width: 100px;
        }
        .col-segelnr {
            width: 70px;
        }
        .col-matbrev {
            width: 50px;
        }
        .col-srs {
            width: 65px;
        }
        .col-besattn {
            width: 50px;
        }
        .col-time {
            width: 70px;
        }
        .col-elapsed {
            width: 65px;
        }
        .col-corrected {
            width: 65px;
        }
        .col-diff {
            width: 65px;
        }
        .col-points {
            width: 80px;
        }

        .small {
            font-size: 8pt;
        }
        h1 {
            font-size: 1.5rem;
        }
        h2 {
            font-size: 1.3rem;
        }
        .container {
            max-width: 100%;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            @page {
                size: landscape;
                margin: 0.8cm;
            }
        }
        @media screen {
            /* Use landscape orientation for better table display */
            @page {
                size: landscape;
            }
            body {
                max-width: 1200px;
                margin: 0 auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <h1>Resultat - {{ .event.Namn }}{{ if .isMultiHeat }}{{ if .showOverall }} - Totala resultat{{ else if and .selectedHeat .selectedHeat.Name }} - {{ .selectedHeat.Name }}{{ end }}{{ end }}</h1>

                {{ if and .isMultiHeat .showOverall (isDiscardEnabled .event) }}
                    {{ $discardCount := getDiscardCount .event (len .heats) }}
                    {{ if gt $discardCount 0 }}
                        <p style="font-style: italic; margin-bottom: 15px; color: #6c757d;">
                            Borträkning aktiverad: {{ $discardCount }} sämsta resultat räknas bort per deltagare (genomstruket och rött).
                        </p>
                    {{ end }}
                {{ end }}

                {{ $totalCrew := 0 }}
                {{ if .usingSavedResults }}
                    {{ range .savedResults }}
                        {{ $totalCrew = add $totalCrew (add .CrewCount 1) }}
                    {{ end }}
                {{ else if .totalResults }}
                    {{ range .totalResults }}
                        {{ $totalCrew = add $totalCrew (add .EventParticipant.CrewCount 1) }}
                    {{ end }}
                {{ else if .heatResults }}
                    {{ range .heatResults }}
                        {{ $totalCrew = add $totalCrew .TotalPersons }}
                    {{ end }}
                {{ else }}
                    {{ range .results }}
                        {{ $totalCrew = add $totalCrew .TotalPersons }}
                    {{ end }}
                {{ end }}
                <p style="line-height: 1.2;">
                    <strong>Datum:</strong> {{ .event.Datum.Format "2006-01-02" }} | <strong>Starttid:</strong> {{ .event.Starttid }} | <strong>Vind:</strong> {{ .event.Vind }} m/s | <strong>Banlängd:</strong> {{ .event.Banlangd }} nm | <strong>Deltagare:</strong> {{ $totalCrew }} | <em>* = anpassat SRS-värde</em>
                </p>

                {{ if .event.Beskrivning }}
                <p>{{ .event.Beskrivning }}</p>
                {{ end }}

                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th class="col-plac">Plac.</th>
                                <th class="col-seglare">Seglare</th>
                                <th class="col-klubb">Klubb</th>
                                {{ if .event.Entypsegling }}
                                <th class="col-segelnr">Segelnummer</th>
                                {{ else }}
                                <th class="col-battyp">Båttyp</th>
                                <th class="col-segelnr">Segelnr</th>
                                <th class="col-matbrev">Mätbrev</th>
                                <th class="col-srs">SRS</th>
                                {{ end }}
                                <th class="col-besattn">Besättn.</th>
                                {{ if .totalResults }}
                                <th class="col-points">Totala poäng</th>
                                {{ range .heats }}
                                <th class="col-points">{{ .Name }}</th>
                                {{ end }}
                                {{ else if .event.Entypsegling }}
                                {{ if and .heatResults .isMultiHeat }}
                                <th class="col-points">Poäng</th>
                                {{ end }}
                                {{ else }}
                                {{ if .event.Jaktstart }}
                                <th class="col-time">Starttid</th>
                                {{ end }}
                                <th class="col-time">Måltid</th>
                                <th class="col-elapsed">Seglad tid</th>
                                <th class="col-corrected">Korr. tid</th>
                                <th class="col-diff">Efter föregående</th>
                                <th class="col-diff">Efter vinnare</th>
                                {{ if and .heatResults .isMultiHeat }}
                                <th class="col-points">Poäng</th>
                                {{ end }}
                                {{ end }}
                            </tr>
                        </thead>
                        <tbody>
                            {{ if .usingSavedResults }}
                                {{ range $result := .savedResults }}
                                <tr>
                                    <td class="col-plac">{{ $result.Position }}</td>
                                    <td class="col-seglare">{{ $result.SailorName }}</td>
                                    <td class="col-klubb">{{ $result.SailorClub }}</td>
                                    <td class="col-battyp">{{ if gt (len $result.BoatType) 14 }}{{ slice $result.BoatType 0 14 }}...{{ else }}{{ $result.BoatType }}{{ end }}</td>
                                    <td class="col-segelnr">
                                        {{ if and $result.Nationality $result.Segelnummer }}
                                            {{ $result.Nationality }}-{{ $result.Segelnummer }}
                                        {{ else if $result.Segelnummer }}
                                            {{ $result.Segelnummer }}
                                        {{ else if $result.Nationality }}
                                            {{ $result.Nationality }}
                                        {{ end }}
                                    </td>
                                    <td class="col-matbrev">{{ $result.MatbrevsNummer }}</td>
                                    <td class="col-srs">
                                        {{ printf "%.3f" $result.SRSValue }}
                                        {{ if $result.UseCustomSRSValue }}
                                            (*)
                                        {{ else if eq $result.SRSType "srs" }}
                                            (spinn)
                                        {{ else if eq $result.SRSType "srs_shorthanded" }}
                                            (S/H spinn)
                                        {{ end }}
                                    </td>
                                    <td class="col-besattn">{{ add $result.CrewCount 1 }}</td>
                                    {{ if $.event.Jaktstart }}
                                    <td class="col-time">{{ if $result.DNS }}-{{ else }}{{ $result.StartTime }}{{ end }}</td>
                                    {{ end }}
                                    <td class="col-time">{{ if $result.DNS }}<span style="background-color: #ffc107; padding: 2px 5px; border-radius: 3px; color: #000;">DNS</span>{{ else if $result.DNF }}<span style="background-color: #dc3545; padding: 2px 5px; border-radius: 3px; color: #fff;">DNF</span>{{ else }}{{ $result.FinishTime }}{{ end }}</td>
                                    <td class="col-elapsed">{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.ElapsedTime }}{{ end }}</td>
                                    <td class="col-corrected">{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.CorrectedTime }}{{ end }}</td>
                                    <td class="col-diff">{{ if or $result.DNS $result.DNF }}-{{ else if $result.TimeToPrevious }}{{ $result.TimeToPrevious }}{{ else }}-{{ end }}</td>
                                    <td class="col-diff">{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.TimeToWinner }}{{ end }}</td>
                                </tr>
                                {{ end }}
                            {{ else if .totalResults }}
                                {{ range $result := .totalResults }}
                                <tr>
                                    <td class="col-plac">{{ $result.TotalPosition }}</td>
                                    <td class="col-seglare">{{ $result.Sailor.Namn }}</td>
                                    <td class="col-klubb">{{ $result.Sailor.Klubb }}</td>
                                    {{ if $.event.Entypsegling }}
                                    <td class="col-segelnr">{{ $result.EventParticipant.PersonalNumber }}</td>
                                    {{ else }}
                                    <td class="col-battyp">{{ if gt (len $result.Boat.Battyp) 14 }}{{ slice $result.Boat.Battyp 0 14 }}...{{ else }}{{ $result.Boat.Battyp }}{{ end }}</td>
                                    <td class="col-segelnr">
                                        {{ if and $result.Boat.Nationality $result.Boat.Segelnummer }}
                                            {{ $result.Boat.Nationality }}-{{ $result.Boat.Segelnummer }}
                                        {{ else if $result.Boat.Segelnummer }}
                                            {{ $result.Boat.Segelnummer }}
                                        {{ else if $result.Boat.Nationality }}
                                            {{ $result.Boat.Nationality }}
                                        {{ end }}
                                    </td>
                                    <td class="col-matbrev">{{ $result.Boat.MatbrevsNummer }}</td>
                                    <td class="col-srs">
                                        {{ if $result.EventParticipant.UseCustomSRSValue }}
                                            {{ printf "%.3f" $result.EventParticipant.CustomSRSValue }} (*)
                                        {{ else }}
                                            {{ printf "%.3f" $result.EventParticipant.SelectedSRSValue }}
                                            {{ if eq $result.EventParticipant.SRSType "srs" }}
                                                (spinn)
                                            {{ else if eq $result.EventParticipant.SRSType "srs_shorthanded" }}
                                                (S/H spinn)
                                            {{ end }}
                                        {{ end }}
                                    </td>
                                    {{ end }}
                                    <td class="col-besattn">{{ add $result.EventParticipant.CrewCount 1 }}</td>
                                    <td class="col-points"><strong>{{ printf "%.1f" $result.TotalPoints }}</strong></td>
                                    {{ $currentResult := $result }}
                                    {{ range $heat := $.heats }}
                                        {{ $found := false }}
                                        {{ range $currentResult.HeatResults }}
                                            {{ if eq .Heat.ID $heat.ID }}
                                                {{ $found = true }}
                                                {{ $isDiscarded := false }}
                                                {{ range $currentResult.DiscardedHeats }}
                                                    {{ if eq . $heat.ID }}
                                                        {{ $isDiscarded = true }}
                                                    {{ end }}
                                                {{ end }}
                                                <td class="col-points">
                                                    {{ if $isDiscarded }}
                                                        {{ if .DNS }}
                                                            <span style="text-decoration: line-through; color: #dc3545;">DNS ({{ printf "%.1f" .Points }}p)</span>
                                                        {{ else if .DNF }}
                                                            <span style="text-decoration: line-through; color: #dc3545;">DNF ({{ printf "%.1f" .Points }}p)</span>
                                                        {{ else }}
                                                            <span style="text-decoration: line-through; color: #dc3545;">{{ .Position }} ({{ printf "%.1f" .Points }}p)</span>
                                                        {{ end }}
                                                    {{ else }}
                                                        {{ if .DNS }}
                                                            DNS ({{ printf "%.1f" .Points }}p)
                                                        {{ else if .DNF }}
                                                            DNF ({{ printf "%.1f" .Points }}p)
                                                        {{ else }}
                                                            {{ .Position }} ({{ printf "%.1f" .Points }}p)
                                                        {{ end }}
                                                    {{ end }}
                                                </td>
                                            {{ end }}
                                        {{ end }}
                                        {{ if not $found }}
                                            <td class="col-points">-</td>
                                        {{ end }}
                                    {{ end }}
                                </tr>
                                {{ end }}
                            {{ else if .heatResults }}
                                {{ range $result := .heatResults }}
                                <tr>
                                    <td class="col-plac">
                                        {{ if .DNS }}
                                            <span style="background-color: #ffc107; padding: 2px 5px; border-radius: 3px; color: #000;">DNS</span>
                                        {{ else if .DNF }}
                                            <span style="background-color: #dc3545; padding: 2px 5px; border-radius: 3px; color: #fff;">DNF</span>
                                        {{ else }}
                                            {{ .Position }}
                                        {{ end }}
                                    </td>
                                    <td class="col-seglare">{{ $result.Sailor.Namn }}</td>
                                    <td class="col-klubb">{{ $result.Sailor.Klubb }}</td>
                                    {{ if $.event.Entypsegling }}
                                    <td class="col-segelnr">{{ $result.EventParticipant.PersonalNumber }}</td>
                                    {{ else }}
                                    <td class="col-battyp">{{ if gt (len $result.Boat.Battyp) 14 }}{{ slice $result.Boat.Battyp 0 14 }}...{{ else }}{{ $result.Boat.Battyp }}{{ end }}</td>
                                    <td class="col-segelnr">
                                        {{ if and $result.Boat.Nationality $result.Boat.Segelnummer }}
                                            {{ $result.Boat.Nationality }}-{{ $result.Boat.Segelnummer }}
                                        {{ else if $result.Boat.Segelnummer }}
                                            {{ $result.Boat.Segelnummer }}
                                        {{ else if $result.Boat.Nationality }}
                                            {{ $result.Boat.Nationality }}
                                        {{ end }}
                                    </td>
                                    <td class="col-matbrev">{{ $result.Boat.MatbrevsNummer }}</td>
                                    <td class="col-srs">
                                        {{ if $result.EventParticipant.UseCustomSRSValue }}
                                            {{ printf "%.3f" $result.EventParticipant.CustomSRSValue }} (*)
                                        {{ else }}
                                            {{ printf "%.3f" $result.EventParticipant.SelectedSRSValue }}
                                            {{ if eq $result.EventParticipant.SRSType "srs" }}
                                                (spinn)
                                            {{ else if eq $result.EventParticipant.SRSType "srs_shorthanded" }}
                                                (S/H spinn)
                                            {{ end }}
                                        {{ end }}
                                    </td>
                                    {{ end }}
                                    <td class="col-besattn">{{ $result.TotalPersons }}</td>
                                    {{ if not $.event.Entypsegling }}
                                    {{ if $.event.Jaktstart }}
                                    <td class="col-time">{{ if $result.DNS }}-{{ else }}{{ $result.StartTime }}{{ end }}</td>
                                    {{ end }}
                                    <td class="col-time">{{ if $result.DNS }}<span style="background-color: #ffc107; padding: 2px 5px; border-radius: 3px; color: #000;">DNS</span>{{ else if $result.DNF }}<span style="background-color: #dc3545; padding: 2px 5px; border-radius: 3px; color: #fff;">DNF</span>{{ else }}{{ $result.FinishTime }}{{ end }}</td>
                                    <td class="col-elapsed">{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.ElapsedTime }}{{ end }}</td>
                                    <td class="col-corrected">{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.CorrectedTime }}{{ end }}</td>
                                    <td class="col-diff">{{ if or $result.DNS $result.DNF }}-{{ else if $result.TimeToPrevious }}{{ $result.TimeToPrevious }}{{ else }}-{{ end }}</td>
                                    <td class="col-diff">{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.TimeToWinner }}{{ end }}</td>
                                    {{ end }}
                                    {{ if $.isMultiHeat }}
                                    <td class="col-points">{{ printf "%.1f" $result.Points }}</td>
                                    {{ end }}
                                </tr>
                                {{ end }}
                            {{ else }}
                                {{ range $index, $result := .results }}
                                <tr>
                                    <td class="col-plac">{{ add $index 1 }}</td>
                                    <td class="col-seglare">{{ $result.Sailor.Namn }}</td>
                                    <td class="col-klubb">{{ $result.Sailor.Klubb }}</td>
                                    <td class="col-battyp">{{ if gt (len $result.Boat.Battyp) 14 }}{{ slice $result.Boat.Battyp 0 14 }}...{{ else }}{{ $result.Boat.Battyp }}{{ end }}</td>
                                    <td class="col-segelnr">
                                        {{ if and $result.Boat.Nationality $result.Boat.Segelnummer }}
                                            {{ $result.Boat.Nationality }}-{{ $result.Boat.Segelnummer }}
                                        {{ else if $result.Boat.Segelnummer }}
                                            {{ $result.Boat.Segelnummer }}
                                        {{ else if $result.Boat.Nationality }}
                                            {{ $result.Boat.Nationality }}
                                        {{ end }}
                                    </td>
                                    <td class="col-matbrev">{{ $result.Boat.MatbrevsNummer }}</td>
                                    <td class="col-srs">
                                        {{ if $result.EventParticipant.UseCustomSRSValue }}
                                            {{ printf "%.3f" $result.EventParticipant.CustomSRSValue }} (*)
                                        {{ else }}
                                            {{ printf "%.3f" $result.EventParticipant.SelectedSRSValue }}
                                            {{ if eq $result.EventParticipant.SRSType "srs" }}
                                                (spinn)
                                            {{ else if eq $result.EventParticipant.SRSType "srs_shorthanded" }}
                                                (S/H spinn)
                                            {{ end }}
                                        {{ end }}
                                    </td>
                                    <td class="col-besattn">{{ $result.TotalPersons }}</td>
                                    {{ if $.event.Jaktstart }}
                                    <td class="col-time">{{ if $result.DNS }}-{{ else }}{{ $result.StartTime }}{{ end }}</td>
                                    {{ end }}
                                    <td class="col-time">{{ if $result.DNS }}<span style="background-color: #ffc107; padding: 2px 5px; border-radius: 3px; color: #000;">DNS</span>{{ else if $result.DNF }}<span style="background-color: #dc3545; padding: 2px 5px; border-radius: 3px; color: #fff;">DNF</span>{{ else }}{{ $result.FinishTime }}{{ end }}</td>
                                    <td class="col-elapsed">{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.ElapsedTime }}{{ end }}</td>
                                    <td class="col-corrected">{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.CorrectedTime }}{{ end }}</td>
                                    <td class="col-diff">{{ if or $result.DNS $result.DNF }}-{{ else if $result.TimeToPrevious }}{{ $result.TimeToPrevious }}{{ else }}-{{ end }}</td>
                                    <td class="col-diff">{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.TimeToWinner }}{{ end }}</td>
                                </tr>
                                {{ end }}
                            {{ end }}
                        </tbody>
                    </table>
                </div>

                <div class="mt-3 no-print">
                    <a href="index.html" class="btn btn-secondary">Tillbaka till alla resultat</a>
                    <button class="btn btn-primary" onclick="window.print()">Skriv ut</button>
                </div>

                <div class="mt-5 text-center text-muted">
                    <p>Genererad av <a href="https://github.com/rogge66/sailapp">Segling</a> {{ .generatedAt }}</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`

const minimalTemplate = `<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>{{ .title }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.5;
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
            font-size: 0.9rem;
        }
        /* Table styling for better readability */
        table {
            font-size: 9pt;
            table-layout: fixed;
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        th {
            font-size: 9pt;
            font-weight: bold;
            padding: 0.3rem;
            text-align: left;
            background-color: #f8f9fa;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        td {
            padding: 0.3rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.18) !important;
        }

        /* Column width definitions */
        th:nth-child(6), td:nth-child(6) { /* Mätbrev column */
            width: 50px;
            max-width: 50px;
        }
        th:nth-child(7), td:nth-child(7) { /* SRS column */
            width: 65px;
            max-width: 65px;
        }
        th:nth-child(9), td:nth-child(9),
        th:nth-child(10), td:nth-child(10) { /* Time columns (start/finish) */
            width: 70px;
            max-width: 70px;
        }
        th:nth-child(11), td:nth-child(11),
        th:nth-child(12), td:nth-child(12),
        th:nth-child(13), td:nth-child(13),
        th:nth-child(14), td:nth-child(14) { /* Time columns (elapsed/corrected/diff) */
            width: 65px;
            max-width: 65px;
        }

        .footer {
            margin-top: 2rem;
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
        h1 {
            font-size: 1.5rem;
        }
        .small {
            font-size: 8pt;
        }
        @media print {
            .no-print {
                display: none;
            }
            @page {
                size: landscape;
                margin: 0.8cm;
            }
        }
        @media screen {
            /* Use landscape orientation for better table display */
            @page {
                size: landscape;
            }
        }
    </style>
</head>
<body>
    <h1>Resultat - {{ .event.Namn }}{{ if .isMultiHeat }}{{ if .showOverall }} - Totala resultat{{ else if and .selectedHeat .selectedHeat.Name }} - {{ .selectedHeat.Name }}{{ end }}{{ end }}</h1>

    {{ if and .isMultiHeat .showOverall (isDiscardEnabled .event) }}
        {{ $discardCount := getDiscardCount .event (len .heats) }}
        {{ if gt $discardCount 0 }}
            <p style="font-style: italic; margin-bottom: 15px; color: #6c757d;">
                Borträkning aktiverad: {{ $discardCount }} sämsta resultat räknas bort per deltagare (genomstruket och rött).
            </p>
        {{ end }}
    {{ end }}

    {{ $totalCrew := 0 }}
    {{ if .usingSavedResults }}
        {{ range .savedResults }}
            {{ $totalCrew = add $totalCrew (add .CrewCount 1) }}
        {{ end }}
    {{ else if .totalResults }}
        {{ range .totalResults }}
            {{ $totalCrew = add $totalCrew (add .EventParticipant.CrewCount 1) }}
        {{ end }}
    {{ else if .heatResults }}
        {{ range .heatResults }}
            {{ $totalCrew = add $totalCrew .TotalPersons }}
        {{ end }}
    {{ else }}
        {{ range .results }}
            {{ $totalCrew = add $totalCrew .TotalPersons }}
        {{ end }}
    {{ end }}
    <p style="line-height: 1.2;">
        <strong>Datum:</strong> {{ .event.Datum.Format "2006-01-02" }} | <strong>Starttid:</strong> {{ .event.Starttid }} | <strong>Vind:</strong> {{ .event.Vind }} m/s | <strong>Banlängd:</strong> {{ .event.Banlangd }} nm | <strong>Deltagare:</strong> {{ $totalCrew }} | <em>* = anpassat SRS-värde</em>
    </p>

    {{ if .event.Beskrivning }}
    <p>{{ .event.Beskrivning }}</p>
    {{ end }}

    <table>
        <thead>
            <tr>
                <th>Plac.</th>
                <th>Seglare</th>
                <th>Klubb</th>
                {{ if .event.Entypsegling }}
                <th>Segelnummer</th>
                {{ else }}
                <th>Båttyp</th>
                <th>Segelnr</th>
                <th>Mätbrev</th>
                <th>SRS</th>
                {{ end }}
                <th>Besättn.</th>
                {{ if .totalResults }}
                <th>Totala poäng</th>
                {{ range .heats }}
                <th>{{ .Name }}</th>
                {{ end }}
                {{ else if .event.Entypsegling }}
                {{ if and .heatResults .isMultiHeat }}
                <th>Poäng</th>
                {{ end }}
                {{ else }}
                {{ if .event.Jaktstart }}
                <th>Starttid</th>
                {{ end }}
                <th>Måltid</th>
                <th>Seglad tid</th>
                <th>Korr. tid</th>
                <th>Efter föregående</th>
                <th>Efter vinnare</th>
                {{ if and .heatResults .isMultiHeat }}
                <th>Poäng</th>
                {{ end }}
                {{ end }}
            </tr>
        </thead>
        <tbody>
            {{ if .usingSavedResults }}
                {{ range $result := .savedResults }}
                <tr>
                    <td>{{ $result.Position }}</td>
                    <td>{{ $result.SailorName }}</td>
                    <td>{{ $result.SailorClub }}</td>
                    <td>{{ if gt (len $result.BoatType) 14 }}{{ slice $result.BoatType 0 14 }}...{{ else }}{{ $result.BoatType }}{{ end }}</td>
                    <td>
                        {{ if and $result.Nationality $result.Segelnummer }}
                            {{ $result.Nationality }}-{{ $result.Segelnummer }}
                        {{ else if $result.Segelnummer }}
                            {{ $result.Segelnummer }}
                        {{ else if $result.Nationality }}
                            {{ $result.Nationality }}
                        {{ end }}
                    </td>
                    <td>{{ $result.MatbrevsNummer }}</td>
                    <td>
                        {{ printf "%.3f" $result.SRSValue }}
                        {{ if $result.UseCustomSRSValue }}
                            (*)
                        {{ else if eq $result.SRSType "srs" }}
                            (spinn)
                        {{ else if eq $result.SRSType "srs_shorthanded" }}
                            (S/H spinn)
                        {{ end }}
                    </td>
                    <td>{{ add $result.CrewCount 1 }}</td>
                    {{ if $.event.Jaktstart }}
                    <td>{{ if $result.DNS }}-{{ else }}{{ $result.StartTime }}{{ end }}</td>
                    {{ end }}
                    <td>{{ if $result.DNS }}<span style="background-color: #ffc107; padding: 2px 5px; border-radius: 3px; color: #000;">DNS</span>{{ else if $result.DNF }}<span style="background-color: #dc3545; padding: 2px 5px; border-radius: 3px; color: #fff;">DNF</span>{{ else }}{{ $result.FinishTime }}{{ end }}</td>
                    <td>{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.ElapsedTime }}{{ end }}</td>
                    <td>{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.CorrectedTime }}{{ end }}</td>
                    <td>{{ if or $result.DNS $result.DNF }}-{{ else if $result.TimeToPrevious }}{{ $result.TimeToPrevious }}{{ else }}-{{ end }}</td>
                    <td>{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.TimeToWinner }}{{ end }}</td>
                </tr>
                {{ end }}
            {{ else if .totalResults }}
                {{ range $result := .totalResults }}
                <tr>
                    <td>{{ $result.TotalPosition }}</td>
                    <td>{{ $result.Sailor.Namn }}</td>
                    <td>{{ $result.Sailor.Klubb }}</td>
                    {{ if $.event.Entypsegling }}
                    <td>{{ $result.EventParticipant.PersonalNumber }}</td>
                    {{ else }}
                    <td>{{ if gt (len $result.Boat.Battyp) 14 }}{{ slice $result.Boat.Battyp 0 14 }}...{{ else }}{{ $result.Boat.Battyp }}{{ end }}</td>
                    <td>
                        {{ if and $result.Boat.Nationality $result.Boat.Segelnummer }}
                            {{ $result.Boat.Nationality }}-{{ $result.Boat.Segelnummer }}
                        {{ else if $result.Boat.Segelnummer }}
                            {{ $result.Boat.Segelnummer }}
                        {{ else if $result.Boat.Nationality }}
                            {{ $result.Boat.Nationality }}
                        {{ end }}
                    </td>
                    <td>{{ $result.Boat.MatbrevsNummer }}</td>
                    <td>
                        {{ if $result.EventParticipant.UseCustomSRSValue }}
                            {{ printf "%.3f" $result.EventParticipant.CustomSRSValue }} (*)
                        {{ else }}
                            {{ printf "%.3f" $result.EventParticipant.SelectedSRSValue }}
                            {{ if eq $result.EventParticipant.SRSType "srs" }}
                                (spinn)
                            {{ else if eq $result.EventParticipant.SRSType "srs_shorthanded" }}
                                (S/H spinn)
                            {{ end }}
                        {{ end }}
                    </td>
                    {{ end }}
                    <td>{{ add $result.EventParticipant.CrewCount 1 }}</td>
                    <td><strong>{{ printf "%.1f" $result.TotalPoints }}</strong></td>
                    {{ $currentResult := $result }}
                    {{ range $heat := $.heats }}
                        {{ $found := false }}
                        {{ range $currentResult.HeatResults }}
                            {{ if eq .Heat.ID $heat.ID }}
                                {{ $found = true }}
                                {{ $isDiscarded := false }}
                                {{ range $currentResult.DiscardedHeats }}
                                    {{ if eq . $heat.ID }}
                                        {{ $isDiscarded = true }}
                                    {{ end }}
                                {{ end }}
                                <td>
                                    {{ if $isDiscarded }}
                                        {{ if .DNS }}
                                            <span style="text-decoration: line-through; color: #dc3545;">DNS ({{ printf "%.1f" .Points }}p)</span>
                                        {{ else if .DNF }}
                                            <span style="text-decoration: line-through; color: #dc3545;">DNF ({{ printf "%.1f" .Points }}p)</span>
                                        {{ else }}
                                            <span style="text-decoration: line-through; color: #dc3545;">{{ .Position }} ({{ printf "%.1f" .Points }}p)</span>
                                        {{ end }}
                                    {{ else }}
                                        {{ if .DNS }}
                                            DNS ({{ printf "%.1f" .Points }}p)
                                        {{ else if .DNF }}
                                            DNF ({{ printf "%.1f" .Points }}p)
                                        {{ else }}
                                            {{ .Position }} ({{ printf "%.1f" .Points }}p)
                                        {{ end }}
                                    {{ end }}
                                </td>
                            {{ end }}
                        {{ end }}
                        {{ if not $found }}
                            <td>-</td>
                        {{ end }}
                    {{ end }}
                </tr>
                {{ end }}
            {{ else if .heatResults }}
                {{ range $result := .heatResults }}
                <tr>
                    <td>
                        {{ if .DNS }}
                            <span style="background-color: #ffc107; padding: 2px 5px; border-radius: 3px; color: #000;">DNS</span>
                        {{ else if .DNF }}
                            <span style="background-color: #dc3545; padding: 2px 5px; border-radius: 3px; color: #fff;">DNF</span>
                        {{ else }}
                            {{ .Position }}
                        {{ end }}
                    </td>
                    <td>{{ $result.Sailor.Namn }}</td>
                    <td>{{ $result.Sailor.Klubb }}</td>
                    {{ if $.event.Entypsegling }}
                    <td>{{ $result.EventParticipant.PersonalNumber }}</td>
                    {{ else }}
                    <td>{{ if gt (len $result.Boat.Battyp) 14 }}{{ slice $result.Boat.Battyp 0 14 }}...{{ else }}{{ $result.Boat.Battyp }}{{ end }}</td>
                    <td>
                        {{ if and $result.Boat.Nationality $result.Boat.Segelnummer }}
                            {{ $result.Boat.Nationality }}-{{ $result.Boat.Segelnummer }}
                        {{ else if $result.Boat.Segelnummer }}
                            {{ $result.Boat.Segelnummer }}
                        {{ else if $result.Boat.Nationality }}
                            {{ $result.Boat.Nationality }}
                        {{ end }}
                    </td>
                    <td>{{ $result.Boat.MatbrevsNummer }}</td>
                    <td>
                        {{ if $result.EventParticipant.UseCustomSRSValue }}
                            {{ printf "%.3f" $result.EventParticipant.CustomSRSValue }} (*)
                        {{ else }}
                            {{ printf "%.3f" $result.EventParticipant.SelectedSRSValue }}
                            {{ if eq $result.EventParticipant.SRSType "srs" }}
                                (spinn)
                            {{ else if eq $result.EventParticipant.SRSType "srs_shorthanded" }}
                                (S/H spinn)
                            {{ end }}
                        {{ end }}
                    </td>
                    {{ end }}
                    <td>{{ $result.TotalPersons }}</td>
                    {{ if not $.event.Entypsegling }}
                    {{ if $.event.Jaktstart }}
                    <td>{{ if $result.DNS }}-{{ else }}{{ $result.StartTime }}{{ end }}</td>
                    {{ end }}
                    <td>{{ if $result.DNS }}<span style="background-color: #ffc107; padding: 2px 5px; border-radius: 3px; color: #000;">DNS</span>{{ else if $result.DNF }}<span style="background-color: #dc3545; padding: 2px 5px; border-radius: 3px; color: #fff;">DNF</span>{{ else }}{{ $result.FinishTime }}{{ end }}</td>
                    <td>{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.ElapsedTime }}{{ end }}</td>
                    <td>{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.CorrectedTime }}{{ end }}</td>
                    <td>{{ if or $result.DNS $result.DNF }}-{{ else if $result.TimeToPrevious }}{{ $result.TimeToPrevious }}{{ else }}-{{ end }}</td>
                    <td>{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.TimeToWinner }}{{ end }}</td>
                    {{ end }}
                    {{ if $.isMultiHeat }}
                    <td>{{ printf "%.1f" $result.Points }}</td>
                    {{ end }}
                </tr>
                {{ end }}
            {{ else }}
                {{ range $index, $result := .results }}
                <tr>
                    <td>{{ add $index 1 }}</td>
                    <td>{{ $result.Sailor.Namn }}</td>
                    <td>{{ $result.Sailor.Klubb }}</td>
                    <td>{{ if gt (len $result.Boat.Battyp) 14 }}{{ slice $result.Boat.Battyp 0 14 }}...{{ else }}{{ $result.Boat.Battyp }}{{ end }}</td>
                    <td>
                        {{ if and $result.Boat.Nationality $result.Boat.Segelnummer }}
                            {{ $result.Boat.Nationality }}-{{ $result.Boat.Segelnummer }}
                        {{ else if $result.Boat.Segelnummer }}
                            {{ $result.Boat.Segelnummer }}
                        {{ else if $result.Boat.Nationality }}
                            {{ $result.Boat.Nationality }}
                        {{ end }}
                    </td>
                    <td>{{ $result.Boat.MatbrevsNummer }}</td>
                    <td>
                        {{ if $result.EventParticipant.UseCustomSRSValue }}
                            {{ printf "%.3f" $result.EventParticipant.CustomSRSValue }} (*)
                        {{ else }}
                            {{ printf "%.3f" $result.EventParticipant.SelectedSRSValue }}
                            {{ if eq $result.EventParticipant.SRSType "srs" }}
                                (spinn)
                            {{ else if eq $result.EventParticipant.SRSType "srs_shorthanded" }}
                                (S/H spinn)
                            {{ end }}
                        {{ end }}
                    </td>
                    <td>{{ $result.TotalPersons }}</td>
                    {{ if $.event.Jaktstart }}
                    <td>{{ if $result.DNS }}-{{ else }}{{ $result.StartTime }}{{ end }}</td>
                    {{ end }}
                    <td>{{ if $result.DNS }}<span style="background-color: #ffc107; padding: 2px 5px; border-radius: 3px; color: #000;">DNS</span>{{ else if $result.DNF }}<span style="background-color: #dc3545; padding: 2px 5px; border-radius: 3px; color: #fff;">DNF</span>{{ else }}{{ $result.FinishTime }}{{ end }}</td>
                    <td>{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.ElapsedTime }}{{ end }}</td>
                    <td>{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.CorrectedTime }}{{ end }}</td>
                    <td>{{ if or $result.DNS $result.DNF }}-{{ else if $result.TimeToPrevious }}{{ $result.TimeToPrevious }}{{ else }}-{{ end }}</td>
                    <td>{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.TimeToWinner }}{{ end }}</td>
                </tr>
                {{ end }}
            {{ end }}
        </tbody>
    </table>

    <div class="no-print">
        <a href="index.html">Tillbaka till alla resultat</a>
        <button onclick="window.print()">Skriv ut</button>
    </div>

    <div class="footer">
        Genererad av <a href="https://github.com/rogge66/sailapp">Segling</a> {{ .generatedAt }}
    </div>
</body>
</html>`
