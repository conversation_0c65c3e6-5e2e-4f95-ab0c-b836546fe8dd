package handlers

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"slices"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rb<PERSON><PERSON>gren/segling/pkg/database"
	"github.com/rbjoregren/segling/pkg/models"
	"github.com/rbjo<PERSON>gren/segling/pkg/services"
	"github.com/rb<PERSON><PERSON>gren/segling/pkg/utils"
)

// Handler holds dependencies for the handlers
type Handler struct {
	DB             *database.DB
	Version        string
	ServerHost     string
	ServerPort     string
	EnableTLS      bool
	WeatherService *services.WeatherService
}

// ParticipantWithStartTime extends the participant data with jaktstart time information
type ParticipantWithStartTime struct {
	models.EventParticipant
	StartTimeOffset   string
	AbsoluteStartTime string
	Sailor            models.Sailor
	Boat              models.Boat
}

// NewHandler creates a new Handler
func NewHandler(db *database.DB) *Handler {
	return &Handler{
		DB:             db,
		Version:        "dev",
		WeatherService: services.NewWeatherService(db),
	}
}

// SetVersion sets the application version
func (h *Handler) SetVersion(version string) {
	h.Version = version
}

// SetServerConfig sets the server configuration
func (h *Handler) SetServerConfig(host, port string, enableTLS bool) {
	h.ServerHost = host
	h.ServerPort = port
	h.EnableTLS = enableTLS
}

// addCommonData adds common data to all templates
func (h *Handler) addCommonData(data gin.H) gin.H {
	// Add version to all templates
	data["version"] = h.Version
	return data
}

// Home handles the home page
func (h *Handler) Home(c *gin.Context) {
	// Get the year filter from the query parameter
	yearStr := c.Query("year")
	var year int
	var err error

	if yearStr != "" {
		year, err = strconv.Atoi(yearStr)
		if err != nil {
			// If the year is invalid, default to current year
			year = time.Now().Year()
		}
	} else {
		// If no year is specified, default to current year
		year = time.Now().Year()
	}

	// Get the competition type filter from the query parameter
	competitionType := c.Query("type")

	// Get events for the selected year and competition type
	events, err := h.DB.GetEventsByYearAndType(year, competitionType)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get all events for the year dropdown
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Extract unique years from all events for the dropdown
	years := make(map[int]bool)
	for _, event := range allEvents {
		years[event.Datum.Year()] = true
	}

	// Convert the map to a sorted slice
	uniqueYears := make([]int, 0, len(years))
	for y := range years {
		uniqueYears = append(uniqueYears, y)
	}
	sort.Ints(uniqueYears)

	// Get competition types settings
	competitionTypesStr, err := h.DB.GetSetting("competition_types")
	if err != nil {
		competitionTypesStr = "Kvällssegling,Regatta" // Fallback to default if setting not found
		log.Printf("Error getting competition_types setting: %v", err)
	}
	competitionTypesList := strings.Split(competitionTypesStr, ",")

	// Add participant and boat counts to each event
	totalParticipants := 0
	totalBoats := 0

	for i := range events {
		// Count participants
		participantCount, err := h.DB.CountEventParticipants(events[i].ID)
		if err != nil {
			log.Printf("Error counting participants for event %d: %v", events[i].ID, err)
			// Continue with count 0 if there's an error
			events[i].ParticipantCount = 0
		} else {
			events[i].ParticipantCount = participantCount
			totalParticipants += participantCount
		}

		// Count unique boats
		boatCount, err := h.DB.CountEventBoats(events[i].ID)
		if err != nil {
			log.Printf("Error counting boats for event %d: %v", events[i].ID, err)
			// Continue with count 0 if there's an error
			events[i].BoatCount = 0
		} else {
			events[i].BoatCount = boatCount
			totalBoats += boatCount
		}
	}

	data := gin.H{
		"title":                  "Hem",
		"events":                 events,
		"allEvents":              allEvents, // Add this for the event_nav template
		"activeMenu":             "home",
		"selectedEventJaktstart": false, // Add this to ensure icons display correctly
		"years":                  uniqueYears,
		"selectedYear":           year,
		"competitionTypesList":   competitionTypesList,
		"selectedType":           competitionType,
		"totalBoats":             totalBoats, // Counting unique boats
		"totalParticipants":      totalParticipants,
	}
	c.HTML(http.StatusOK, "index.html", h.addCommonData(data))
}

// GetHelp handles the help page
func (h *Handler) GetHelp(c *gin.Context) {
	// Get all events for the dropdown
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		// If there's an error, just continue with an empty list
		allEvents = []models.Event{}
	}

	c.HTML(http.StatusOK, "help.html", gin.H{
		"title":                  "Hjälp",
		"allEvents":              allEvents, // Add this for the event_nav template
		"activeMenu":             "help",
		"selectedEventJaktstart": false, // Add this to ensure icons display correctly
	})
}

// GetSettings handles the settings page
func (h *Handler) GetSettings(c *gin.Context) {
	// Get all events for the dropdown
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		// If there's an error, just continue with an empty list
		allEvents = []models.Event{}
	}

	// Get the current auto backup setting
	autoBackup, err := h.DB.GetSetting("auto_backup_on_event_create")
	if err != nil {
		// If there's an error, default to true
		autoBackup = "true"
		log.Printf("Error getting auto_backup_on_event_create setting: %v", err)
	}

	// Get the default club setting
	defaultClub, err := h.DB.GetSetting("default_club")
	if err != nil || defaultClub == "" {
		defaultClub = "LSS" // Fallback to LSS if setting not found
		log.Printf("Error getting default_club setting: %v", err)
	}

	// Get the time format setting
	use24hTime, err := h.DB.GetSetting("use_24h_time")
	if err != nil {
		use24hTime = "true" // Fallback to 24h if setting not found
		log.Printf("Error getting use_24h_time setting: %v", err)
	}

	// Get GitHub Pages settings
	githubPagesEnabled, err := h.DB.GetSetting("github_pages_enabled")
	if err != nil {
		githubPagesEnabled = "false" // Fallback to disabled if setting not found
		log.Printf("Error getting github_pages_enabled setting: %v", err)
	}

	githubPagesRepo, err := h.DB.GetSetting("github_pages_repo")
	if err != nil {
		githubPagesRepo = "" // Fallback to empty if setting not found
		log.Printf("Error getting github_pages_repo setting: %v", err)
	}

	githubPagesBranch, err := h.DB.GetSetting("github_pages_branch")
	if err != nil {
		githubPagesBranch = "gh-pages" // Fallback to gh-pages if setting not found
		log.Printf("Error getting github_pages_branch setting: %v", err)
	}

	githubPagesToken, err := h.DB.GetSetting("github_pages_token")
	if err != nil {
		githubPagesToken = "" // Fallback to empty if setting not found
		log.Printf("Error getting github_pages_token setting: %v", err)
	}

	githubPagesTemplate, err := h.DB.GetSetting("github_pages_template")
	if err != nil {
		githubPagesTemplate = "default" // Fallback to default if setting not found
		log.Printf("Error getting github_pages_template setting: %v", err)
	}

	// Get competition types settings
	competitionTypes, err := h.DB.GetSetting("competition_types")
	if err != nil {
		competitionTypes = "Kvällssegling,Regatta" // Fallback to default if setting not found
		log.Printf("Error getting competition_types setting: %v", err)
	}

	defaultCompetitionType, err := h.DB.GetSetting("default_competition_type")
	if err != nil {
		defaultCompetitionType = "Kvällssegling" // Fallback to default if setting not found
		log.Printf("Error getting default_competition_type setting: %v", err)
	}

	// Get Google Drive settings
	googleDriveEnabled, err := h.DB.GetSetting("google_drive_enabled")
	if err != nil {
		googleDriveEnabled = "false" // Fallback to disabled if setting not found
		log.Printf("Error getting google_drive_enabled setting: %v", err)
	}

	googleDriveFolderID, err := h.DB.GetSetting("google_drive_folder_id")
	if err != nil {
		googleDriveFolderID = "" // Fallback to empty if setting not found
		log.Printf("Error getting google_drive_folder_id setting: %v", err)
	}

	googleDriveNamingConvention, err := h.DB.GetSetting("google_drive_naming_convention")
	if err != nil {
		googleDriveNamingConvention = "{{event_name}}_{{date}}" // Fallback to default if setting not found
		log.Printf("Error getting google_drive_naming_convention setting: %v", err)
	}

	// Get weather settings
	weatherApiToken, err := h.DB.GetSetting("weather_api_token")
	if err != nil {
		weatherApiToken = "44b7a85536ef4238bddd7d885976a1f0" // Fallback to default if setting not found
		log.Printf("Error getting weather_api_token setting: %v", err)
	}

	weatherLocation, err := h.DB.GetSetting("weather_location")
	if err != nil {
		weatherLocation = "Linköping" // Fallback to default if setting not found
		log.Printf("Error getting weather_location setting: %v", err)
	}

	// Check if client secrets file exists
	clientSecretsExists := false
	if _, err := os.Stat("client_secrets.json"); err == nil {
		clientSecretsExists = true
	}

	// Generate the OAuth callback URL dynamically
	scheme := "http"
	if h.EnableTLS {
		scheme = "https"
	}
	host := h.ServerHost
	if host == "" {
		host = "localhost"
	}
	port := h.ServerPort
	if port == "" {
		port = "8080"
	}
	callbackURL := fmt.Sprintf("%s://%s:%s/settings/google-drive/callback", scheme, host, port)

	// Competition types list is used in the HTML template

	c.HTML(http.StatusOK, "settings.html", gin.H{
		"title":                          "Inställningar",
		"allEvents":                      allEvents,
		"activeMenu":                     "settings",
		"selectedEventJaktstart":         false,
		"autoBackup":                     autoBackup == "true",
		"defaultClub":                    defaultClub,
		"use24hTime":                     use24hTime == "true",
		"githubPagesEnabled":             githubPagesEnabled == "true",
		"githubPagesRepo":                githubPagesRepo,
		"githubPagesBranch":              githubPagesBranch,
		"githubPagesToken":               githubPagesToken,
		"githubPagesTemplate":            githubPagesTemplate,
		"competitionTypes":               competitionTypes,
		"defaultCompetitionType":         defaultCompetitionType,
		"competitionTypesList":           strings.Split(competitionTypes, ","),
		"googleDriveEnabled":             googleDriveEnabled == "true",
		"googleDriveFolderID":            googleDriveFolderID,
		"googleDriveNamingConvention":    googleDriveNamingConvention,
		"googleDriveClientSecretsExists": clientSecretsExists,
		"googleDriveCallbackURL":         callbackURL,
		"weatherApiToken":                weatherApiToken,
		"weatherLocation":                weatherLocation,
	})
}

// PostSettings handles the settings form submission
func (h *Handler) PostSettings(c *gin.Context) {
	// Get the auto backup setting from the form
	autoBackup := c.PostForm("auto_backup") == "on"

	// Convert to string value
	autoBackupValue := "false"
	if autoBackup {
		autoBackupValue = "true"
	}

	// Save the setting
	err := h.DB.SetSetting("auto_backup_on_event_create", autoBackupValue)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte spara inställningar: %v", err),
		})
		return
	}

	// Redirect back to the settings page
	c.Redirect(http.StatusSeeOther, "/settings")
}

// PostCheckpointWAL handles WAL checkpoint requests
func (h *Handler) PostCheckpointWAL(c *gin.Context) {
	err := h.DB.CheckpointWAL()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Kunde inte utföra WAL checkpoint: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "WAL checkpoint utförd framgångsrikt. Alla ändringar är nu synkroniserade till huvuddatabasfilen.",
	})
}

// PostClubSetting handles updating the default club setting
func (h *Handler) PostClubSetting(c *gin.Context) {
	// Get the default club setting from the form
	defaultClub := c.PostForm("default_club")

	// Default to LSS if empty
	if defaultClub == "" {
		defaultClub = "LSS"
	}

	// Save the setting
	err := h.DB.SetSetting("default_club", defaultClub)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte spara klubbinställning: %v", err),
		})
		return
	}

	// Redirect back to the settings page
	c.Redirect(http.StatusSeeOther, "/settings")
}

// PostTimeFormatSetting handles updating the time format setting
func (h *Handler) PostTimeFormatSetting(c *gin.Context) {
	// Get the time format setting from the form
	use24hTime := c.PostForm("use_24h_time") == "on"

	// Convert to string value
	use24hTimeValue := "false"
	if use24hTime {
		use24hTimeValue = "true"
	}

	// Save the setting
	err := h.DB.SetSetting("use_24h_time", use24hTimeValue)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte spara tidsinställning: %v", err),
		})
		return
	}

	// Redirect back to the settings page
	c.Redirect(http.StatusSeeOther, "/settings")
}

// PostCompetitionTypesSetting handles updating the competition types settings
func (h *Handler) PostCompetitionTypesSetting(c *gin.Context) {
	// Get the competition types from the form
	competitionTypes := c.PostForm("competition_types")
	defaultCompetitionType := c.PostForm("default_competition_type")

	// Default to Kvällssegling,Regatta if empty
	if competitionTypes == "" {
		competitionTypes = "Kvällssegling,Regatta"
	}

	// Default to Kvällssegling if empty or not in the list
	if defaultCompetitionType == "" {
		defaultCompetitionType = "Kvällssegling"
	}

	// Validate that the default competition type is in the list
	competitionTypesList := strings.Split(competitionTypes, ",")
	if !slices.Contains(competitionTypesList, defaultCompetitionType) {
		competitionTypes = competitionTypes + "," + defaultCompetitionType
	}

	// Save the settings
	err := h.DB.SetSetting("competition_types", competitionTypes)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte spara tävlingstypsinställningar: %v", err),
		})
		return
	}

	err = h.DB.SetSetting("default_competition_type", defaultCompetitionType)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte spara tävlingstypsinställningar: %v", err),
		})
		return
	}

	// Redirect back to the settings page
	c.Redirect(http.StatusSeeOther, "/settings")
}

// PostGitHubPagesSettings handles updating the GitHub Pages settings
func (h *Handler) PostGitHubPagesSettings(c *gin.Context) {
	// Get the GitHub Pages settings from the form
	githubPagesEnabled := c.PostForm("github_pages_enabled") == "on"
	githubPagesRepo := c.PostForm("github_pages_repo")
	githubPagesBranch := c.PostForm("github_pages_branch")
	githubPagesToken := c.PostForm("github_pages_token")
	githubPagesTemplate := c.PostForm("github_pages_template")

	// Validate the repository format (username/repo)
	if githubPagesEnabled && githubPagesRepo != "" {
		repoPattern := regexp.MustCompile(`^[a-zA-Z0-9_-]+/[a-zA-Z0-9_.-]+$`)
		if !repoPattern.MatchString(githubPagesRepo) {
			c.HTML(http.StatusBadRequest, "error.html", gin.H{
				"error": "GitHub repository måste anges i formatet användarnamn/repo",
			})
			return
		}
	}

	// Default branch to gh-pages if empty
	if githubPagesBranch == "" {
		githubPagesBranch = "gh-pages"
	}

	// Default template to default if empty
	if githubPagesTemplate == "" {
		githubPagesTemplate = "default"
	}

	// Convert to string value
	githubPagesEnabledValue := "false"
	if githubPagesEnabled {
		githubPagesEnabledValue = "true"
	}

	// Save the settings
	err := h.DB.SetSetting("github_pages_enabled", githubPagesEnabledValue)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte spara GitHub Pages inställningar: %v", err),
		})
		return
	}

	err = h.DB.SetSetting("github_pages_repo", githubPagesRepo)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte spara GitHub Pages inställningar: %v", err),
		})
		return
	}

	err = h.DB.SetSetting("github_pages_branch", githubPagesBranch)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte spara GitHub Pages inställningar: %v", err),
		})
		return
	}

	err = h.DB.SetSetting("github_pages_token", githubPagesToken)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte spara GitHub Pages inställningar: %v", err),
		})
		return
	}

	err = h.DB.SetSetting("github_pages_template", githubPagesTemplate)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte spara GitHub Pages inställningar: %v", err),
		})
		return
	}

	// Redirect back to the settings page
	c.Redirect(http.StatusSeeOther, "/settings")
}

// PostGoogleDriveSettings handles updating the Google Drive settings
func (h *Handler) PostGoogleDriveSettings(c *gin.Context) {
	// Get the Google Drive settings from the form
	googleDriveEnabled := c.PostForm("google_drive_enabled") == "on"
	googleDriveFolderID := c.PostForm("google_drive_folder_id")
	googleDriveNamingConvention := c.PostForm("google_drive_naming_convention")

	// Default naming convention if empty
	if googleDriveNamingConvention == "" {
		googleDriveNamingConvention = "{{event_name}}_{{date}}"
	}

	// Convert to string value
	googleDriveEnabledValue := "false"
	if googleDriveEnabled {
		googleDriveEnabledValue = "true"
	}

	// Save the settings
	err := h.DB.SetSetting("google_drive_enabled", googleDriveEnabledValue)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte spara Google Drive inställningar: %v", err),
		})
		return
	}

	err = h.DB.SetSetting("google_drive_folder_id", googleDriveFolderID)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte spara Google Drive inställningar: %v", err),
		})
		return
	}

	err = h.DB.SetSetting("google_drive_naming_convention", googleDriveNamingConvention)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte spara Google Drive inställningar: %v", err),
		})
		return
	}

	// Redirect back to the settings page
	c.Redirect(http.StatusSeeOther, "/settings")
}

// PostWeatherSettings handles updating the weather settings
func (h *Handler) PostWeatherSettings(c *gin.Context) {
	// Get the weather settings from the form
	weatherApiToken := c.PostForm("weather_api_token")
	weatherLocation := c.PostForm("weather_location")

	// Default to original values if empty
	if weatherApiToken == "" {
		weatherApiToken = "44b7a85536ef4238bddd7d885976a1f0"
	}
	if weatherLocation == "" {
		weatherLocation = "Linköping"
	}

	// Save the settings
	err := h.DB.SetSetting("weather_api_token", weatherApiToken)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte spara väderinställningar: %v", err),
		})
		return
	}

	err = h.DB.SetSetting("weather_location", weatherLocation)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte spara väderinställningar: %v", err),
		})
		return
	}

	// Redirect back to the settings page
	c.Redirect(http.StatusSeeOther, "/settings")
}

// UploadClientSecrets handles uploading the client_secrets.json file
func (h *Handler) UploadClientSecrets(c *gin.Context) {
	// Get the uploaded file
	file, err := c.FormFile("client_secrets")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Ingen fil uppladdad",
		})
		return
	}

	// Check file extension
	if !strings.HasSuffix(file.Filename, ".json") {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Filen måste vara en JSON-fil",
		})
		return
	}

	// Delete any existing token file since it's tied to the old client
	tokenFile := "google_drive_token.json"
	if _, err := os.Stat(tokenFile); err == nil {
		log.Printf("Deleting existing Google Drive token file: %s", tokenFile)
		err = os.Remove(tokenFile)
		if err != nil {
			log.Printf("Warning: Could not delete existing token file: %v", err)
		} else {
			log.Printf("Successfully deleted existing token file")
		}
	}

	// Save the file as client_secrets.json
	err = c.SaveUploadedFile(file, "client_secrets.json")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Kunde inte spara fil: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Client secrets fil uppladdad framgångsrikt. Tidigare autentisering har raderats - du behöver autentisera igen.",
	})
}

// GetSailors handles the sailors page
func (h *Handler) GetSailors(c *gin.Context) {
	search := c.Query("search")

	var sailors []models.Sailor
	var err error

	if search != "" {
		sailors, err = h.DB.SearchSailors(search)
	} else {
		sailors, err = h.DB.GetSailors()
	}

	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Check if this is an HTMX request for live search
	if c.GetHeader("HX-Request") == "true" {
		c.HTML(http.StatusOK, "sailor_list.html", gin.H{
			"sailors": sailors,
			"search":  search,
		})
		return
	}

	// Get all events for the dropdown
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		// If there's an error, just continue with an empty list
		allEvents = []models.Event{}
	}

	c.HTML(http.StatusOK, "sailors.html", gin.H{
		"title":                  "Seglare",
		"sailors":                sailors,
		"search":                 search,
		"allEvents":              allEvents, // Add this for the event_nav template
		"activeMenu":             "sailors",
		"selectedEventJaktstart": false, // Add this to ensure icons display correctly
	})
}

// SearchSailorsLive handles live search for sailors
func (h *Handler) SearchSailorsLive(c *gin.Context) {
	search := c.Query("search")

	var sailors []models.Sailor
	var err error

	if search != "" {
		sailors, err = h.DB.SearchSailors(search)
	} else {
		sailors, err = h.DB.GetSailors()
	}

	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	c.HTML(http.StatusOK, "sailor_list.html", gin.H{
		"sailors": sailors,
		"search":  search,
	})
}

// GetSailorForm handles the sailor form page
func (h *Handler) GetSailorForm(c *gin.Context) {
	// Get the default club setting
	defaultClub, err := h.DB.GetSetting("default_club")
	if err != nil || defaultClub == "" {
		defaultClub = "LSS" // Fallback to LSS if setting not found
	}

	idStr := c.Param("id")

	// If we have an ID, we're editing an existing sailor
	if idStr != "" && idStr != "new" {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			c.HTML(http.StatusBadRequest, "error.html", gin.H{
				"error": "Invalid sailor ID",
			})
			return
		}

		sailor, err := h.DB.GetSailor(id)
		if err != nil {
			c.HTML(http.StatusInternalServerError, "error.html", gin.H{
				"error": err.Error(),
			})
			return
		}

		c.HTML(http.StatusOK, "sailor_form.html", gin.H{
			"title":       "Redigera seglare",
			"sailor":      sailor,
			"defaultClub": defaultClub,
		})
		return
	}

	// Otherwise, we're creating a new sailor
	c.HTML(http.StatusOK, "sailor_form.html", gin.H{
		"title":       "Ny seglare",
		"defaultClub": defaultClub,
	})
}

// PostSailor handles the sailor form submission
func (h *Handler) PostSailor(c *gin.Context) {
	idStr := c.PostForm("id")
	namn := c.PostForm("namn")
	telefon := c.PostForm("telefon")
	klubb := c.PostForm("klubb")

	// klubb will be defaulted in the database functions if empty

	// Validate input
	if namn == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Namn är obligatoriskt",
		})
		return
	}

	// If we have an ID, we're updating an existing sailor
	if idStr != "" && idStr != "0" {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			c.HTML(http.StatusBadRequest, "error.html", gin.H{
				"error": "Invalid sailor ID",
			})
			return
		}

		err = h.DB.UpdateSailor(id, namn, telefon, klubb)
		if err != nil {
			c.HTML(http.StatusInternalServerError, "error.html", gin.H{
				"error": err.Error(),
			})
			return
		}

		c.Redirect(http.StatusSeeOther, "/sailors")
		return
	}

	// Otherwise, we're creating a new sailor
	_, err := h.DB.CreateSailor(namn, telefon, klubb)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	c.Redirect(http.StatusSeeOther, "/sailors")
}

// DeleteSailor handles the sailor deletion
func (h *Handler) DeleteSailor(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid sailor ID"})
		return
	}

	err = h.DB.DeleteSailor(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return an empty response with 200 status code
	// This will cause HTMX to remove the row without showing any content
	c.Status(http.StatusOK)
}

// GetBoats handles the boats page
func (h *Handler) GetBoats(c *gin.Context) {
	search := c.Query("search")

	var boats []models.Boat
	var err error

	if search != "" {
		boats, err = h.DB.SearchBoats(search)
	} else {
		boats, err = h.DB.GetBoats()
	}

	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Check if this is an HTMX request for live search
	if c.GetHeader("HX-Request") == "true" {
		c.HTML(http.StatusOK, "boat_list.html", gin.H{
			"boats":  boats,
			"search": search,
		})
		return
	}

	// Get all events for the dropdown
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		// If there's an error, just continue with an empty list
		allEvents = []models.Event{}
	}

	c.HTML(http.StatusOK, "boats.html", gin.H{
		"title":                  "Båtar",
		"boats":                  boats,
		"search":                 search,
		"allEvents":              allEvents, // Add this for the event_nav template
		"activeMenu":             "boats",
		"selectedEventJaktstart": false, // Add this to ensure icons display correctly
	})
}

// SearchBoatsLive handles live search for boats
func (h *Handler) SearchBoatsLive(c *gin.Context) {
	search := c.Query("search")

	var boats []models.Boat
	var err error

	if search != "" {
		boats, err = h.DB.SearchBoats(search)
	} else {
		boats, err = h.DB.GetBoats()
	}

	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	c.HTML(http.StatusOK, "boat_list.html", gin.H{
		"boats":  boats,
		"search": search,
	})
}

// GetBoatJSON returns boat data in JSON format
func (h *Handler) GetBoatJSON(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid boat ID"})
		return
	}

	boat, err := h.DB.GetBoat(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, boat)
}

// GetBoatForm handles the boat form page
func (h *Handler) GetBoatForm(c *gin.Context) {
	idStr := c.Param("id")

	// If we have an ID, we're editing an existing boat
	if idStr != "" && idStr != "new" {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			c.HTML(http.StatusBadRequest, "error.html", gin.H{
				"error": "Invalid boat ID",
			})
			return
		}

		boat, err := h.DB.GetBoat(id)
		if err != nil {
			c.HTML(http.StatusInternalServerError, "error.html", gin.H{
				"error": err.Error(),
			})
			return
		}

		// Get all boat types for the dropdown from local database only
		boatTypes, err := h.DB.GetSRSBoatTypeNames()
		if err != nil {
			log.Printf("Error getting boat types from local database: %v", err)
			boatTypes = []string{}
		} else {
			log.Printf("Using %d boat types from local database", len(boatTypes))
		}

		c.HTML(http.StatusOK, "boat_form.html", gin.H{
			"title":     "Redigera båt",
			"boat":      boat,
			"boatTypes": boatTypes,
		})
		return
	}

	// Otherwise, we're creating a new boat
	// Get all boat types for the dropdown from local database only
	boatTypes, err := h.DB.GetSRSBoatTypeNames()
	if err != nil {
		log.Printf("Error getting boat types from local database: %v", err)
		boatTypes = []string{}
	} else {
		log.Printf("Using %d boat types from local database", len(boatTypes))
	}

	c.HTML(http.StatusOK, "boat_form.html", gin.H{
		"title":     "Ny båt",
		"boatTypes": boatTypes,
	})
}

// PostBoat handles the boat form submission
func (h *Handler) PostBoat(c *gin.Context) {
	idStr := c.PostForm("id")
	namn := c.PostForm("namn")
	battyp := c.PostForm("battyp")
	matbrevsNummer := c.PostForm("matbrevs_nummer")
	segelnummer := c.PostForm("segelnummer")
	nationality := c.PostForm("nationality")
	srsStr := c.PostForm("srs")
	srsUUStr := c.PostForm("srs_utan_undanvindsegel")
	srsSHStr := c.PostForm("srs_shorthanded")
	srsSHUUStr := c.PostForm("srs_shorthanded_utan_undanvindsegel")

	// Validate input with detailed error messages
	if namn == "" && battyp == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Namn och båttyp är obligatoriska",
		})
		return
	} else if namn == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Namn är obligatoriskt",
		})
		return
	} else if battyp == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Båttyp är obligatoriskt",
		})
		return
	}

	// Log the received values for debugging
	log.Printf("PostBoat: namn=%s, battyp=%s", namn, battyp)

	// Parse SRS values
	srs, err := strconv.ParseFloat(srsStr, 64)
	if err != nil {
		srs = 0
	}
	srsUU, err := strconv.ParseFloat(srsUUStr, 64)
	if err != nil {
		srsUU = 0
	}
	srsSH, err := strconv.ParseFloat(srsSHStr, 64)
	if err != nil {
		srsSH = 0
	}
	srsSHUU, err := strconv.ParseFloat(srsSHUUStr, 64)
	if err != nil {
		srsSHUU = 0
	}

	// If a mätbrevs nummer is provided, always use the boat type and SRS values from it
	if matbrevsNummer != "" {
		// First try to get from local database
		matbrev, err := h.DB.GetSRSMatbrevByNumber(matbrevsNummer)
		if err == nil {
			// Found in local database, use the boat type and SRS values
			battyp = matbrev.Battyp
			srs = matbrev.SRS
			srsUU = matbrev.SRSUtanUndanvindsegel
			srsSH = matbrev.SRSShorthanded
			srsSHUU = matbrev.SRSShorthandedUtanUndanvindsegel

			// Also use the sail number and nationality if provided
			if segelnummer == "" && matbrev.Segelnummer != "" {
				segelnummer = matbrev.Segelnummer
			}
			if nationality == "" && matbrev.Nationality != "" {
				nationality = matbrev.Nationality
			}

			log.Printf("Using SRS data for %s from local database", matbrevsNummer)
		} else {
			// Not found in local database, fetch from website
			srsData, err := utils.FetchSRSDataByMatbrev(matbrevsNummer)
			if err == nil {
				// Always use the boat type from the mätbrevs nummer
				battyp = srsData.Battyp
				srs = srsData.SRS
				srsUU = srsData.SRSUtanUndanvindsegel
				srsSH = srsData.SRSShorthanded
				srsSHUU = srsData.SRSShorthandedUtanUndanvindsegel

				// Also use the sail number and nationality if provided
				if segelnummer == "" && srsData.Segelnummer != "" {
					segelnummer = srsData.Segelnummer
				}
				if nationality == "" && srsData.Nationality != "" {
					nationality = srsData.Nationality
				}
			} else {
				log.Printf("Error fetching SRS data by mätbrevs nummer: %v", err)
			}
		}
	}

	// Default nationality to SWE if not provided
	if nationality == "" {
		nationality = "SWE"
	} else if srs == 0 || srsUU == 0 || srsSH == 0 || srsSHUU == 0 {
		// Otherwise, try to fetch by boat type
		srsData, err := utils.FetchSRSData(battyp)
		if err == nil {
			if srs == 0 {
				srs = srsData.SRS
			}
			if srsUU == 0 {
				srsUU = srsData.SRSUtanUndanvindsegel
			}
			if srsSH == 0 {
				srsSH = srsData.SRSShorthanded
			}
			if srsSHUU == 0 {
				srsSHUU = srsData.SRSShorthandedUtanUndanvindsegel
			}
		} else {
			log.Printf("Error fetching SRS data by boat type: %v", err)
		}
	}

	// If we have an ID, we're updating an existing boat
	if idStr != "" && idStr != "0" {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			c.HTML(http.StatusBadRequest, "error.html", gin.H{
				"error": "Invalid boat ID",
			})
			return
		}

		err = h.DB.UpdateBoat(id, namn, battyp, matbrevsNummer, segelnummer, nationality, srs, srsUU, srsSH, srsSHUU)
		if err != nil {
			c.HTML(http.StatusInternalServerError, "error.html", gin.H{
				"error": err.Error(),
			})
			return
		}

		c.Redirect(http.StatusSeeOther, "/boats")
		return
	}

	// Otherwise, we're creating a new boat
	_, err = h.DB.CreateBoat(namn, battyp, matbrevsNummer, segelnummer, nationality, srs, srsUU, srsSH, srsSHUU)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	c.Redirect(http.StatusSeeOther, "/boats")
}

// DeleteBoat handles the boat deletion
func (h *Handler) DeleteBoat(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid boat ID"})
		return
	}

	err = h.DB.DeleteBoat(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return an empty response with 200 status code
	// This will cause HTMX to remove the row without showing any content
	c.Status(http.StatusOK)
}

// GetSRSData handles the AJAX request for SRS data
func (h *Handler) GetSRSData(c *gin.Context) {
	battyp := c.Query("battyp")
	matbrevsNummer := c.Query("matbrevs_nummer")

	if battyp == "" && matbrevsNummer == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Båttyp eller mätbrevs nummer är obligatorisk"})
		return
	}

	var srsData utils.SRSData

	// First try to get data from local database
	if matbrevsNummer != "" {
		// Try to get from local database first
		matbrev, err := h.DB.GetSRSMatbrevByNumber(matbrevsNummer)
		if err == nil {
			// Found in local database
			srsData = utils.SRSData{
				MatbrevsNummer:                   matbrev.MatbrevsNummer,
				Battyp:                           matbrev.Battyp,
				Agare:                            matbrev.Agare,
				Segelnummer:                      matbrev.Segelnummer,
				Nationality:                      matbrev.Nationality,
				SRS:                              matbrev.SRS,
				SRSUtanUndanvindsegel:            matbrev.SRSUtanUndanvindsegel,
				SRSShorthanded:                   matbrev.SRSShorthanded,
				SRSShorthandedUtanUndanvindsegel: matbrev.SRSShorthandedUtanUndanvindsegel,
			}
			log.Printf("SRS data for %s found in local database", matbrevsNummer)
		} else {
			// Not found in local database, fetch from website
			srsData, err = utils.FetchSRSDataByMatbrev(matbrevsNummer)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
		}
	} else {
		// Get from local database only
		boatType, err := h.DB.GetSRSBoatTypeByName(battyp)
		if err == nil {
			// Found in local database
			srsData = utils.SRSData{
				Battyp:                           boatType.Battyp,
				SRS:                              boatType.SRS,
				SRSUtanUndanvindsegel:            boatType.SRSUtanUndanvindsegel,
				SRSShorthanded:                   boatType.SRSShorthanded,
				SRSShorthandedUtanUndanvindsegel: boatType.SRSShorthandedUtanUndanvindsegel,
			}
			log.Printf("SRS data for %s found in local database", battyp)
		} else {
			// Not found in local database
			log.Printf("SRS data for %s not found in local database", battyp)
			c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("Båttyp '%s' hittades inte i lokal databas. Synkronisera SRS-data först.", battyp)})
			return
		}
	}

	// Log the SRS data
	log.Printf("SRS data: %+v", srsData)

	// Convert to lowercase field names with underscores for consistency with the matbrev API
	c.JSON(http.StatusOK, gin.H{
		"battyp":                              srsData.Battyp,
		"srs":                                 srsData.SRS,
		"srs_utan_undanvindsegel":             srsData.SRSUtanUndanvindsegel,
		"srs_shorthanded":                     srsData.SRSShorthanded,
		"srs_shorthanded_utan_undanvindsegel": srsData.SRSShorthandedUtanUndanvindsegel,
	})
}

// SearchSRSData handles the AJAX request for searching SRS data
func (h *Handler) SearchSRSData(c *gin.Context) {
	query := c.Query("q")

	if query == "" {
		c.JSON(http.StatusOK, []models.SRSMatbrev{})
		return
	}

	// Search for boats with measurement certificates in the local database
	boats, err := h.DB.SearchSRSMatbrev(query)
	if err != nil {
		log.Printf("Error searching SRS measurement certificates: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Sort results to prioritize exact matches for mätbrevsnummer
	sortedBoats := make([]models.SRSMatbrev, 0, len(boats))

	// First, look for exact matches on mätbrevsnummer
	exactMatches := make([]models.SRSMatbrev, 0)
	for _, boat := range boats {
		if strings.EqualFold(boat.MatbrevsNummer, query) {
			exactMatches = append(exactMatches, boat)
		}
	}

	// Then, look for boats where mätbrevsnummer starts with the query
	startsWithMatches := make([]models.SRSMatbrev, 0)
	for _, boat := range boats {
		if strings.HasPrefix(strings.ToLower(boat.MatbrevsNummer), strings.ToLower(query)) &&
			!strings.EqualFold(boat.MatbrevsNummer, query) {
			startsWithMatches = append(startsWithMatches, boat)
		}
	}

	// Finally, add all other matches
	otherMatches := make([]models.SRSMatbrev, 0)
	for _, boat := range boats {
		if !strings.EqualFold(boat.MatbrevsNummer, query) &&
			!strings.HasPrefix(strings.ToLower(boat.MatbrevsNummer), strings.ToLower(query)) {
			otherMatches = append(otherMatches, boat)
		}
	}

	// Combine all matches in priority order
	sortedBoats = append(sortedBoats, exactMatches...)
	sortedBoats = append(sortedBoats, startsWithMatches...)
	sortedBoats = append(sortedBoats, otherMatches...)

	// Limit the results to 10 for better performance
	if len(sortedBoats) > 10 {
		sortedBoats = sortedBoats[:10]
	}

	// Log the search results
	log.Printf("Found %d boats matching query '%s'", len(sortedBoats), query)

	c.JSON(http.StatusOK, sortedBoats)
}

// GetMatbrevData handles the API request for getting measurement certificate data
func (h *Handler) GetMatbrevData(c *gin.Context) {
	matbrevsNummer := c.Query("matbrevs_nummer")

	if matbrevsNummer == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Mätbrevsnummer is required"})
		return
	}

	// Validate the mätbrevsnummer format (should start with B or E followed by 4 digits)
	validFormat := false
	// Use a regular expression to validate the format: B or E followed by 4 digits
	validRegex := regexp.MustCompile(`^[BE]\d{4}$`)
	if validRegex.MatchString(strings.ToUpper(matbrevsNummer)) {
		validFormat = true
	}

	if !validFormat {
		log.Printf("Invalid mätbrevsnummer format: %s", matbrevsNummer)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid mätbrevsnummer format. Should be B#### or E#### where #### is a 4-digit number"})
		return
	}

	// Log the request
	log.Printf("Getting data for mätbrevsnummer: %s", matbrevsNummer)
	log.Printf("Request headers: %v", c.Request.Header)
	log.Printf("Request query parameters: %v", c.Request.URL.Query())

	var srsData utils.SRSData

	// Get data from local database only
	matbrev, err := h.DB.GetSRSMatbrevByNumber(matbrevsNummer)
	if err == nil {
		// Found in local database
		srsData = utils.SRSData{
			MatbrevsNummer:                   matbrev.MatbrevsNummer,
			Battyp:                           matbrev.Battyp,
			BatNamn:                          matbrev.BatNamn,
			Agare:                            matbrev.Agare,
			Segelnummer:                      matbrev.Segelnummer,
			Nationality:                      matbrev.Nationality,
			SRS:                              matbrev.SRS,
			SRSUtanUndanvindsegel:            matbrev.SRSUtanUndanvindsegel,
			SRSShorthanded:                   matbrev.SRSShorthanded,
			SRSShorthandedUtanUndanvindsegel: matbrev.SRSShorthandedUtanUndanvindsegel,
		}
		log.Printf("Found data in local database for %s", matbrevsNummer)
		log.Printf("Boat name from database: '%s'", matbrev.BatNamn)
	} else {
		// Not found in local database
		log.Printf("Data not found in local database for %s", matbrevsNummer)
		c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("Mätbrevsnummer %s hittades inte i lokal databas. Synkronisera SRS-data först.", matbrevsNummer)})
		return
	}

	// Log the SRS data
	log.Printf("SRS data for %s: %+v", matbrevsNummer, srsData)

	c.JSON(http.StatusOK, srsData)
}

// SearchMatbrevData handles the API request for searching measurement certificates
func (h *Handler) SearchMatbrevData(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		// Try to get the query from the input field directly
		query = c.Query("matbrevs_nummer")
	}

	if query == "" {
		// Return an empty dropdown if no query
		c.HTML(http.StatusOK, "matbrev_search_results.html", gin.H{
			"results": []models.SRSMatbrev{},
		})
		return
	}

	// Log the search query
	log.Printf("Searching for mätbrev with query: %s", query)

	// Search for boats with measurement certificates in the local database
	boats, err := h.DB.SearchSRSMatbrev(query)
	if err != nil {
		log.Printf("Error searching SRS measurement certificates: %v", err)
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Sort results to prioritize exact matches for mätbrevsnummer
	sortedBoats := make([]models.SRSMatbrev, 0, len(boats))

	// First, look for exact matches on mätbrevsnummer
	exactMatches := make([]models.SRSMatbrev, 0)
	for _, boat := range boats {
		if strings.EqualFold(boat.MatbrevsNummer, query) {
			exactMatches = append(exactMatches, boat)
		}
	}

	// Then, look for boats where mätbrevsnummer starts with the query
	startsWithMatches := make([]models.SRSMatbrev, 0)
	for _, boat := range boats {
		if strings.HasPrefix(strings.ToLower(boat.MatbrevsNummer), strings.ToLower(query)) &&
			!strings.EqualFold(boat.MatbrevsNummer, query) {
			startsWithMatches = append(startsWithMatches, boat)
		}
	}

	// Then, look for matches in boat type, boat name, or owner
	otherMatches := make([]models.SRSMatbrev, 0)
	for _, boat := range boats {
		if !strings.EqualFold(boat.MatbrevsNummer, query) &&
			!strings.HasPrefix(strings.ToLower(boat.MatbrevsNummer), strings.ToLower(query)) &&
			(strings.Contains(strings.ToLower(boat.Battyp), strings.ToLower(query)) ||
				strings.Contains(strings.ToLower(boat.BatNamn), strings.ToLower(query)) ||
				strings.Contains(strings.ToLower(boat.Agare), strings.ToLower(query))) {
			otherMatches = append(otherMatches, boat)
		}
	}

	// Combine all matches in priority order
	sortedBoats = append(sortedBoats, exactMatches...)
	sortedBoats = append(sortedBoats, startsWithMatches...)
	sortedBoats = append(sortedBoats, otherMatches...)

	// Limit the results to 10 for better performance
	if len(sortedBoats) > 10 {
		sortedBoats = sortedBoats[:10]
	}

	// Log the search results
	log.Printf("Found %d boats matching query '%s'", len(sortedBoats), query)

	// Return the results as HTML dropdown items
	c.HTML(http.StatusOK, "matbrev_search_results.html", gin.H{
		"results": sortedBoats,
	})
}

// GetSRSManagement handles the SRS data management page
func (h *Handler) GetSRSManagement(c *gin.Context) {
	// Get the last sync logs
	logs, err := h.DB.GetSRSSyncLogs(10)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get counts of SRS table entries and boats with measurement certificates
	var srsTableCount, matbrevCount int
	err = h.DB.QueryRow("SELECT COUNT(*) FROM srs_boat_types").Scan(&srsTableCount)
	if err != nil {
		srsTableCount = 0
	}
	err = h.DB.QueryRow("SELECT COUNT(*) FROM srs_matbrev").Scan(&matbrevCount)
	if err != nil {
		matbrevCount = 0
	}

	// Get all events for the dropdown
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		// If there's an error, just continue with an empty list
		allEvents = []models.Event{}
	}

	// Check if there's an import success message
	importSuccess := c.Query("import_success") == "true"
	count := c.Query("count")

	// Check if there's an import error message
	importError := c.Query("import_error") == "true"
	message := c.Query("message")

	c.HTML(http.StatusOK, "srs_management.html", gin.H{
		"title":                  "SRS Datahantering",
		"logs":                   logs,
		"boatTypeCount":          srsTableCount,
		"matbrevCount":           matbrevCount,
		"allEvents":              allEvents, // Add this for the event_nav template
		"activeMenu":             "srs",
		"selectedEventJaktstart": false, // Add this to ensure icons display correctly
		"import_success":         importSuccess,
		"count":                  count,
		"import_error":           importError,
		"message":                message,
	})
}

// GetSRSMatbrev handles the SRS measurement certificates page
func (h *Handler) GetSRSMatbrev(c *gin.Context) {
	search := c.Query("search")

	var matbrevs []models.SRSMatbrev
	var err error

	if search != "" {
		matbrevs, err = h.DB.SearchSRSMatbrev(search)
	} else {
		// Get all entries
		matbrevs, err = h.DB.GetSRSMatbrev()
	}

	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Check if this is an HTMX request for live search
	if c.GetHeader("HX-Request") == "true" {
		c.HTML(http.StatusOK, "srs_matbrev_list.html", gin.H{
			"matbrevs": matbrevs,
			"search":   search,
		})
		return
	}

	// Get all events for the dropdown
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		// If there's an error, just continue with an empty list
		allEvents = []models.Event{}
	}

	c.HTML(http.StatusOK, "srs_matbrev.html", gin.H{
		"title":                  "SRS Mätbrev",
		"matbrevs":               matbrevs,
		"search":                 search,
		"allEvents":              allEvents, // Add this for the event_nav template
		"activeMenu":             "srs_matbrev",
		"selectedEventJaktstart": false, // Add this to ensure icons display correctly
	})
}

// SearchSRSMatbrevLive handles live search for SRS measurement certificates
func (h *Handler) SearchSRSMatbrevLive(c *gin.Context) {
	search := c.Query("search")

	var matbrevs []models.SRSMatbrev
	var err error

	if search != "" {
		matbrevs, err = h.DB.SearchSRSMatbrev(search)
	} else {
		// Get all entries
		matbrevs, err = h.DB.GetSRSMatbrev()
	}

	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	c.HTML(http.StatusOK, "srs_matbrev_list.html", gin.H{
		"matbrevs": matbrevs,
		"search":   search,
	})
}

// GetSRSBoatTypes handles the SRS boat types page
func (h *Handler) GetSRSBoatTypes(c *gin.Context) {
	search := c.Query("search")

	var boatTypes []models.SRSBoatType
	var err error

	if search != "" {
		boatTypes, err = h.DB.SearchSRSBoatTypes(search)
	} else {
		// Get all entries
		boatTypes, err = h.DB.GetSRSBoatTypes()
	}

	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Check if this is an HTMX request for live search
	if c.GetHeader("HX-Request") == "true" {
		c.HTML(http.StatusOK, "srs_boat_types_list.html", gin.H{
			"boatTypes": boatTypes,
			"search":    search,
		})
		return
	}

	// Get all events for the dropdown
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		// If there's an error, just continue with an empty list
		allEvents = []models.Event{}
	}

	c.HTML(http.StatusOK, "srs_boat_types.html", gin.H{
		"title":                  "SRS Tabell",
		"boatTypes":              boatTypes,
		"search":                 search,
		"allEvents":              allEvents, // Add this for the event_nav template
		"activeMenu":             "srs_boat_types",
		"selectedEventJaktstart": false, // Add this to ensure icons display correctly
	})
}

// SearchSRSBoatTypesLive handles live search for SRS boat types
func (h *Handler) SearchSRSBoatTypesLive(c *gin.Context) {
	search := c.Query("search")

	var boatTypes []models.SRSBoatType
	var err error

	if search != "" {
		boatTypes, err = h.DB.SearchSRSBoatTypes(search)
	} else {
		// Get all entries
		boatTypes, err = h.DB.GetSRSBoatTypes()
	}

	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	c.HTML(http.StatusOK, "srs_boat_types_list.html", gin.H{
		"boatTypes": boatTypes,
		"search":    search,
	})
}

// SyncSRSData handles the synchronization of SRS data
func (h *Handler) SyncSRSData(c *gin.Context) {
	source := c.PostForm("source")
	var dataUpdated bool

	if source == "srs_tabell" || source == "boat_types" || source == "all" {
		// Sync SRS table
		err := h.syncSRSTable()
		if err != nil {
			c.HTML(http.StatusInternalServerError, "error.html", gin.H{
				"error": err.Error(),
			})
			return
		}
		dataUpdated = true
	}

	if source == "matbrev" || source == "all" {
		// Sync measurement certificates
		err := h.syncMatbrev()
		if err != nil {
			c.HTML(http.StatusInternalServerError, "error.html", gin.H{
				"error": err.Error(),
			})
			return
		}
		dataUpdated = true
	}

	// If SRS data was updated, update all boats with the new data
	if dataUpdated {
		log.Printf("SyncSRSData: Updating all boats with new SRS data")
		err := h.DB.UpdateAllBoatsFromSRSData()
		if err != nil {
			log.Printf("SyncSRSData: Error updating boats: %v", err)
			if err := h.DB.LogSRSSync("boats", "error", "Failed to update boats with new SRS data: "+err.Error()); err != nil {
				log.Printf("Error logging SRS sync: %v", err)
			}
		} else {
			if err := h.DB.LogSRSSync("boats", "success", "Successfully updated all boats with new SRS data"); err != nil {
				log.Printf("Error logging SRS sync: %v", err)
			}
		}
	}

	c.Redirect(http.StatusSeeOther, "/srs")
}

// syncSRSTable syncs all entries from the SRS table from the SRS website
func (h *Handler) syncSRSTable() error {
	log.Println("Starting to sync SRS table entries...")

	// Fetch all entries from the SRS table from the SRS website
	srsTableEntries, err := utils.FetchAllSRSData()
	if err != nil {
		if err := h.DB.LogSRSSync("srs_tabell", "error", err.Error()); err != nil {
			log.Printf("Error logging SRS sync: %v", err)
		}
		return err
	}

	log.Printf("Fetched %d SRS table entries from website", len(srsTableEntries))

	// Clear existing SRS table entries
	err = h.DB.ClearSRSBoatTypes()
	if err != nil {
		if err := h.DB.LogSRSSync("srs_tabell", "error", "Failed to clear existing SRS table entries: "+err.Error()); err != nil {
			log.Printf("Error logging SRS sync: %v", err)
		}
		return err
	}

	log.Println("Cleared existing SRS table entries")

	// Convert utils.SRSData to models.SRSBoatType
	var boatTypes []models.SRSBoatType
	for _, entry := range srsTableEntries {
		boatType := models.SRSBoatType{
			Battyp:                           entry.Battyp,
			SRS:                              entry.SRS,
			SRSUtanUndanvindsegel:            entry.SRSUtanUndanvindsegel,
			SRSShorthanded:                   entry.SRSShorthanded,
			SRSShorthandedUtanUndanvindsegel: entry.SRSShorthandedUtanUndanvindsegel,
		}
		boatTypes = append(boatTypes, boatType)
	}

	// Save all SRS table entries to the database in a batch
	log.Println("Saving SRS table entries in batch...")
	err = h.DB.SaveSRSBoatTypesBatch(boatTypes)
	if err != nil {
		if err := h.DB.LogSRSSync("srs_tabell", "error", "Failed to save SRS table entries: "+err.Error()); err != nil {
			log.Printf("Error logging SRS sync: %v", err)
		}
		return err
	}

	// Log success
	if err := h.DB.LogSRSSync("srs_tabell", "success", fmt.Sprintf("Successfully synced %d SRS table entries", len(srsTableEntries))); err != nil {
		log.Printf("Error logging SRS sync: %v", err)
	}

	log.Println("SRS table sync completed successfully")
	return nil
}

// syncMatbrev syncs all boats with measurement certificates from the SRS website
func (h *Handler) syncMatbrev() error {
	log.Println("Starting to sync measurement certificates...")

	// Fetch all boats with measurement certificates from the SRS website
	matbrevBoats, err := utils.FetchAllApprovedBoats()
	if err != nil {
		if err := h.DB.LogSRSSync("matbrev", "error", err.Error()); err != nil {
			log.Printf("Error logging SRS sync: %v", err)
		}
		return err
	}

	log.Printf("Fetched %d measurement certificates from website", len(matbrevBoats))

	// Clear existing measurement certificates
	err = h.DB.ClearSRSMatbrev()
	if err != nil {
		if err := h.DB.LogSRSSync("matbrev", "error", "Failed to clear existing measurement certificates: "+err.Error()); err != nil {
			log.Printf("Error logging SRS sync: %v", err)
		}
		return err
	}

	log.Println("Cleared existing measurement certificates")

	// Convert utils.SRSData to models.SRSMatbrev
	var matbrevEntries []models.SRSMatbrev
	for _, b := range matbrevBoats {
		matbrev := models.SRSMatbrev{
			MatbrevsNummer:                   b.MatbrevsNummer,
			Battyp:                           b.Battyp,
			BatNamn:                          b.BatNamn,
			Agare:                            b.Agare,
			Segelnummer:                      b.Segelnummer,
			Nationality:                      b.Nationality,
			SRS:                              b.SRS,
			SRSUtanUndanvindsegel:            b.SRSUtanUndanvindsegel,
			SRSShorthanded:                   b.SRSShorthanded,
			SRSShorthandedUtanUndanvindsegel: b.SRSShorthandedUtanUndanvindsegel,
		}
		matbrevEntries = append(matbrevEntries, matbrev)
	}

	// Save all measurement certificates to the database in a batch
	log.Println("Saving measurement certificates in batch...")
	err = h.DB.SaveSRSMatbrevBatch(matbrevEntries)
	if err != nil {
		if err := h.DB.LogSRSSync("matbrev", "error", "Failed to save measurement certificates: "+err.Error()); err != nil {
			log.Printf("Error logging SRS sync: %v", err)
		}
		return err
	}

	// Log success
	if err := h.DB.LogSRSSync("matbrev", "success", fmt.Sprintf("Successfully synced %d measurement certificates", len(matbrevBoats))); err != nil {
		log.Printf("Error logging SRS sync: %v", err)
	}

	log.Println("Measurement certificate sync completed successfully")
	return nil
}

// GetEvents handles the events page
func (h *Handler) GetEvents(c *gin.Context) {
	// Get the year filter from the query parameter
	yearStr := c.Query("year")
	var year int
	var err error

	if yearStr != "" {
		year, err = strconv.Atoi(yearStr)
		if err != nil {
			// If the year is invalid, default to current year
			year = time.Now().Year()
		}
	} else {
		// If no year is specified, default to current year
		year = time.Now().Year()
	}

	// Get events for the selected year (or all years if year is 0)
	events, err := h.DB.GetEventsByYear(year)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// We no longer display participant counts
	for i := range events {
		events[i].ParticipantCount = 0
	}

	// Get all events for the dropdown (regardless of year filter)
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get available years for the year filter dropdown
	years, err := h.getAvailableYears()
	if err != nil {
		// If there's an error, just continue without years
		years = []int{}
	}

	// Check if there's a selected event in the query parameter
	selectedEventIDStr := c.Query("selectedEventID")
	var selectedEventID int64
	var selectedEventJaktstart bool

	if selectedEventIDStr != "" {
		selectedEventID, err = strconv.ParseInt(selectedEventIDStr, 10, 64)
		if err == nil {
			// Get the event to check if jaktstart is enabled
			selectedEvent, err := h.DB.GetEvent(selectedEventID)
			if err == nil {
				selectedEventJaktstart = selectedEvent.Jaktstart
			}
		}
	}

	c.HTML(http.StatusOK, "events.html", gin.H{
		"title":                  "Seglingstävlingar",
		"events":                 events,
		"allEvents":              allEvents, // Add this for the event_nav template
		"activeMenu":             "list",
		"years":                  years,
		"selectedYear":           year,
		"selectedEventID":        selectedEventID,
		"selectedEventJaktstart": selectedEventJaktstart,
	})
}

// getAvailableYears returns a list of years that have events
func (h *Handler) getAvailableYears() ([]int, error) {
	// Get all events
	events, err := h.DB.GetEvents()
	if err != nil {
		return nil, err
	}

	// Create a map to store unique years
	yearsMap := make(map[int]bool)

	// Add the current year and next year to ensure they're always available
	currentYear := time.Now().Year()
	yearsMap[currentYear] = true
	yearsMap[currentYear+1] = true

	// Extract years from event dates
	for _, event := range events {
		year := event.Datum.Year()
		yearsMap[year] = true
	}

	// Convert map to slice
	var years []int
	for year := range yearsMap {
		years = append(years, year)
	}

	// Sort years in descending order (newest first)
	sort.Sort(sort.Reverse(sort.IntSlice(years)))

	return years, nil
}

// GetEventForm handles the event form page
func (h *Handler) GetEventForm(c *gin.Context) {
	idStr := c.Param("id")

	// Get all sailors and boats for the dropdowns
	sailors, err := h.DB.GetSailors()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	boats, err := h.DB.GetBoats()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// If we have an ID, we're editing an existing event
	if idStr != "" && idStr != "new" {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			c.HTML(http.StatusBadRequest, "error.html", gin.H{
				"error": "Invalid event ID",
			})
			return
		}

		event, err := h.DB.GetEvent(id)
		if err != nil {
			c.HTML(http.StatusInternalServerError, "error.html", gin.H{
				"error": err.Error(),
			})
			return
		}

		// Get all events for the dropdown
		events, err := h.DB.GetEvents()
		if err != nil {
			log.Printf("Error fetching events: %v", err)
			// Continue anyway, we'll just have an empty dropdown
		}

		// Get competition types settings
		competitionTypes, err := h.DB.GetSetting("competition_types")
		if err != nil {
			competitionTypes = "Kvällssegling,Regatta" // Fallback to default if setting not found
			log.Printf("Error getting competition_types setting: %v", err)
		}

		defaultCompetitionType, err := h.DB.GetSetting("default_competition_type")
		if err != nil {
			defaultCompetitionType = "Kvällssegling" // Fallback to default if setting not found
			log.Printf("Error getting default_competition_type setting: %v", err)
		}

		c.HTML(http.StatusOK, "event_form.html", gin.H{
			"title":                  "Redigera tävlingsdetaljer",
			"event":                  event,
			"sailors":                sailors,
			"boats":                  boats,
			"events":                 events,
			"allEvents":              events,
			"selectedEventID":        id,
			"selectedEventJaktstart": event.Jaktstart,
			"activeMenu":             "edit",
			"use24hTime":             h.getTimeFormatSetting(),
			"competitionTypesList":   strings.Split(competitionTypes, ","),
			"defaultCompetitionType": defaultCompetitionType,
		})
		return
	}

	// Otherwise, we're creating a new event
	// Get all events for the dropdown
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		log.Printf("Error fetching events: %v", err)
		// Continue anyway, we'll just have an empty dropdown
	}

	// Get competition types settings
	competitionTypes, err := h.DB.GetSetting("competition_types")
	if err != nil {
		competitionTypes = "Kvällssegling,Regatta" // Fallback to default if setting not found
		log.Printf("Error getting competition_types setting: %v", err)
	}

	defaultCompetitionType, err := h.DB.GetSetting("default_competition_type")
	if err != nil {
		defaultCompetitionType = "Kvällssegling" // Fallback to default if setting not found
		log.Printf("Error getting default_competition_type setting: %v", err)
	}

	c.HTML(http.StatusOK, "event_form.html", gin.H{
		"title":                  "Ny tävling",
		"sailors":                sailors,
		"boats":                  boats,
		"allEvents":              allEvents,
		"activeMenu":             "create",
		"selectedEventJaktstart": false, // Add this to ensure icons display correctly
		"use24hTime":             h.getTimeFormatSetting(),
		"competitionTypesList":   strings.Split(competitionTypes, ","),
		"defaultCompetitionType": defaultCompetitionType,
		"now":                    time.Now(), // Add current date for default value
	})
}

// PostEvent handles the event form submission
func (h *Handler) PostEvent(c *gin.Context) {
	idStr := c.PostForm("id")
	namn := c.PostForm("namn")
	datumStr := c.PostForm("datum")
	starttid := c.PostForm("starttid")
	vindStr := c.PostForm("vind")
	banlangdStr := c.PostForm("banlangd")
	jaktstartType := c.PostForm("jaktstart_type")
	tavlingstyp := c.PostForm("tavlingstyp")
	boatType := c.PostForm("boat_type")
	beskrivning := c.PostForm("beskrivning")
	discardAfterHeatsStr := c.PostForm("discard_after_heats")

	// Validate jaktstart_type
	if jaktstartType == "" {
		jaktstartType = "none"
	}

	// For entypsegling, boat_type is required
	if jaktstartType == "entypsegling" && boatType == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Båttyp krävs för entypsegling",
		})
		return
	}

	// Validate input
	if namn == "" || datumStr == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Namn och datum är obligatoriska",
		})
		return
	}

	// Parse datum
	datum, err := time.Parse("2006-01-02", datumStr)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Ogiltigt datumformat",
		})
		return
	}

	// Parse vind (wind speed)
	vind := 0
	if vindStr != "" {
		vind, err = strconv.Atoi(vindStr)
		if err != nil {
			c.HTML(http.StatusBadRequest, "error.html", gin.H{
				"error": "Ogiltigt vindvärde",
			})
			return
		}
	}

	// Parse banlangd (course length)
	banlangd := 0
	if banlangdStr != "" {
		banlangd, err = strconv.Atoi(banlangdStr)
		if err != nil {
			c.HTML(http.StatusBadRequest, "error.html", gin.H{
				"error": "Ogiltigt banlängdvärde",
			})
			return
		}
	}

	// Parse discard_after_heats
	discardAfterHeats := 0
	if discardAfterHeatsStr != "" {
		discardAfterHeats, err = strconv.Atoi(discardAfterHeatsStr)
		if err != nil || discardAfterHeats < 0 {
			c.HTML(http.StatusBadRequest, "error.html", gin.H{
				"error": "Ogiltigt värde för borträkning efter antal deltävlingar",
			})
			return
		}
	}

	// If we have an ID, we're updating an existing event
	if idStr != "" && idStr != "0" {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			c.HTML(http.StatusBadRequest, "error.html", gin.H{
				"error": "Invalid event ID",
			})
			return
		}

		err = h.DB.UpdateEventWithDiscardSupport(id, namn, datum, starttid, vind, banlangd, jaktstartType, tavlingstyp, beskrivning, boatType, discardAfterHeats)
		if err != nil {
			c.HTML(http.StatusInternalServerError, "error.html", gin.H{
				"error": err.Error(),
			})
			return
		}

		// Redirect to the edit page for the event instead of the events list
		c.Redirect(http.StatusSeeOther, fmt.Sprintf("/events/%d", id))
		return
	}

	// Otherwise, we're creating a new event
	eventID, err := h.DB.CreateEventWithDiscardSupport(namn, datum, starttid, vind, banlangd, jaktstartType, tavlingstyp, beskrivning, boatType, discardAfterHeats)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Redirect to the edit page for the newly created event so participants can be added directly
	c.Redirect(http.StatusSeeOther, fmt.Sprintf("/events/%d", eventID))
}

// DeleteEvent handles the event deletion
func (h *Handler) DeleteEvent(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
		return
	}

	// Get the event details before deleting it
	event, err := h.DB.GetEvent(id)
	if err != nil {
		log.Printf("DeleteEvent: Error getting event details: %v", err)
		// Continue with deletion even if we can't get event details
	} else {
		// Try to delete the published GitHub Pages file if it exists
		h.deletePublishedEventFile(event)
	}

	err = h.DB.DeleteEvent(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return an empty response with 200 status code
	// This will cause HTMX to remove the row without showing any content
	c.Status(http.StatusOK)
}

// deletePublishedEventFile attempts to delete the published GitHub Pages file for an event
func (h *Handler) deletePublishedEventFile(event models.Event) {
	// Check if GitHub Pages integration is enabled
	githubPagesEnabled, err := h.DB.GetSetting("github_pages_enabled")
	if err != nil || githubPagesEnabled != "true" {
		log.Printf("deletePublishedEventFile: GitHub Pages integration is not enabled")
		return
	}

	// Get GitHub Pages settings
	githubPagesRepo, err := h.DB.GetSetting("github_pages_repo")
	if err != nil || githubPagesRepo == "" {
		log.Printf("deletePublishedEventFile: GitHub repository is not configured")
		return
	}

	githubPagesBranch, err := h.DB.GetSetting("github_pages_branch")
	if err != nil || githubPagesBranch == "" {
		githubPagesBranch = "gh-pages" // Default to gh-pages
	}

	githubPagesToken, err := h.DB.GetSetting("github_pages_token")
	if err != nil || githubPagesToken == "" {
		log.Printf("deletePublishedEventFile: GitHub token is not configured")
		return
	}

	// Extract the year from the event date
	eventYear := event.Datum.Format("2006")

	// Get the competition type
	competitionType := event.Tavlingstyp
	if competitionType == "" {
		// Try to get the default competition type
		competitionType, err = h.DB.GetSetting("default_competition_type")
		if err != nil || competitionType == "" {
			competitionType = "Kvällssegling" // Default to Kvällssegling
		}
	}

	// Generate filename for the event
	baseFilename := fmt.Sprintf("%s-%s.html", event.Datum.Format("2006-01-02"), strings.ReplaceAll(strings.ToLower(event.Namn), " ", "-"))
	filename := fmt.Sprintf("%s/%s/%s", eventYear, competitionType, baseFilename)

	log.Printf("deletePublishedEventFile: Attempting to delete file: %s", filename)

	// Delete the file from GitHub
	err = h.deleteGitHubFile(githubPagesRepo, githubPagesBranch, githubPagesToken, filename)
	if err != nil {
		log.Printf("deletePublishedEventFile: Error deleting file: %v", err)
		return
	}

	log.Printf("deletePublishedEventFile: Successfully deleted file: %s", filename)

	// Update the year-specific index.html
	err = h.ensureYearIndexHTML(githubPagesRepo, githubPagesBranch, githubPagesToken, eventYear, "", "")
	if err != nil {
		log.Printf("deletePublishedEventFile: Warning: Could not update year index.html after deletion: %v", err)
		// Continue anyway, the file was deleted successfully
	}

	// Update the main index.html file
	err = h.ensureIndexHTML(githubPagesRepo, githubPagesBranch, githubPagesToken, "", "")
	if err != nil {
		log.Printf("deletePublishedEventFile: Warning: Could not update main index.html after deletion: %v", err)
		// Continue anyway, the file was deleted successfully
	}
}

// AddParticipant handles adding a participant to an event
func (h *Handler) AddParticipant(c *gin.Context) {
	eventIDStr := c.PostForm("event_id")
	sailorIDStr := c.PostForm("sailor_id")
	boatIDStr := c.PostForm("boat_id")
	personalNumber := c.PostForm("personal_number")

	// Parse event ID first to check if it's an entypsegling event
	eventID, err := strconv.ParseInt(eventIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
		return
	}

	// Get the event to check if it's entypsegling
	event, err := h.DB.GetEvent(eventID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get event details"})
		return
	}

	// Validate input based on event type
	if event.Entypsegling {
		// For entypsegling events, we need sailor ID and personal number
		if eventIDStr == "" || sailorIDStr == "" || personalNumber == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Event ID, sailor ID, and personal number are required for entypsegling events"})
			return
		}
	} else {
		// For regular events, we need sailor ID and boat ID
		if eventIDStr == "" || sailorIDStr == "" || boatIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Event ID, sailor ID, and boat ID are required"})
			return
		}
	}

	// Parse sailor ID
	sailorID, err := strconv.ParseInt(sailorIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid sailor ID"})
		return
	}

	// Parse boat ID only for regular events
	var boatID int64
	if !event.Entypsegling {
		boatID, err = strconv.ParseInt(boatIDStr, 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid boat ID"})
			return
		}
	}

	// Get SRS type and value from form
	srsType := c.PostForm("srs_type")
	customSRSValueStr := c.PostForm("custom_srs_value")
	useCustomSRSValue := c.PostForm("use_custom_srs_value") == "on"

	// Get the boat to access its SRS values (only for regular events)
	var boat models.Boat
	if !event.Entypsegling {
		boat, err = h.DB.GetBoat(boatID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
	}

	// Validate the SRS type
	switch srsType {
	case "srs", "srs_utan_undanvindsegel", "srs_shorthanded", "srs_shorthanded_utan_undanvindsegel":
		// Valid SRS type, do nothing
	default:
		// Default to standard SRS
		srsType = "srs"
	}

	// Calculate the correct SRS value based on the SRS type (only for regular events)
	var selectedSRSValue float64
	if !event.Entypsegling {
		selectedSRSValue = h.DB.UpdateSRSValueFromType(boat, srsType)
		log.Printf("DEBUG: AddParticipant - SRS Type: %s, Calculated SRS Value: %f", srsType, selectedSRSValue)
	} else {
		// For entypsegling events, SRS values are not used
		selectedSRSValue = 0
		srsType = ""
		log.Printf("DEBUG: AddParticipant - Entypsegling event, no SRS calculation needed")
	}

	// Parse custom SRS value if provided and checkbox is checked
	var customSRSValue float64
	if useCustomSRSValue && customSRSValueStr != "" {
		customSRSValue, err = strconv.ParseFloat(customSRSValueStr, 64)
		if err != nil {
			// If parsing fails, don't use custom value
			useCustomSRSValue = false
			customSRSValue = 0
		}
	} else {
		// If checkbox is not checked, don't use custom value
		useCustomSRSValue = false
		customSRSValue = 0
	}

	// Parse crew count
	var crewCount int
	crewCountStr := c.PostForm("crew_count")
	if crewCountStr != "" {
		crewCount, err = strconv.Atoi(crewCountStr)
		if err != nil || crewCount < 0 {
			log.Printf("Warning: Invalid crew count: %s", crewCountStr)
			crewCount = 0
		}
	}

	// Add participant to event based on event type
	var participantID int64
	if event.Entypsegling {
		// For entypsegling events, use personal number instead of boat
		participantID, err = h.DB.AddEntypSeglingParticipantToEvent(eventID, sailorID, personalNumber, crewCount)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
	} else {
		// For regular events, use boat and SRS values
		participantID, err = h.DB.AddParticipantToEvent(
			eventID, sailorID, boatID,
			srsType, selectedSRSValue, customSRSValue, useCustomSRSValue,
		)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
	}

	// Update crew count if needed (only for regular events, entypsegling events handle crew count during creation)
	if crewCount > 0 && !event.Entypsegling {
		err = h.DB.UpdateParticipantSRS(participantID, srsType, selectedSRSValue, customSRSValue, useCustomSRSValue, crewCount)
		if err != nil {
			log.Printf("Warning: Could not update crew count for participant %d: %v", participantID, err)
		}
	}

	// Get the sailor information
	sailor, err := h.DB.GetSailor(sailorID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Set boat information based on event type
	if event.Entypsegling {
		// For entypsegling events, use a placeholder boat with the event's boat type
		boat = models.Boat{
			ID:     0,
			Namn:   event.BoatType,
			Battyp: event.BoatType,
			SRS:    0,
		}
	} else {
		// For regular events, the boat was already fetched above
		// No additional action needed
	}

	// We no longer update participant count
	c.Header("X-Updated-Participant-Count", "0")
	c.Header("X-Event-ID", strconv.FormatInt(eventID, 10))

	// Return HTML for the new participant row
	c.HTML(http.StatusOK, "participant_row.html", gin.H{
		"ID":                participantID,
		"Sailor":            sailor,
		"Boat":              boat,
		"PersonalNumber":    personalNumber,
		"SRSType":           srsType,
		"SelectedSRSValue":  selectedSRSValue,
		"CustomSRSValue":    customSRSValue,
		"UseCustomSRSValue": useCustomSRSValue,
		"CrewCount":         crewCount,
		"Event":             event,
	})
}

// RemoveParticipant handles removing a participant from an event
func (h *Handler) RemoveParticipant(c *gin.Context) {
	log.Printf("DEBUG: RemoveParticipant - Request received with method %s", c.Request.Method)
	log.Printf("DEBUG: RemoveParticipant - URL: %s", c.Request.URL.String())
	log.Printf("DEBUG: RemoveParticipant - Headers: %v", c.Request.Header)
	log.Printf("DEBUG: RemoveParticipant - Content-Type: %s", c.GetHeader("Content-Type"))
	log.Printf("DEBUG: RemoveParticipant - HX-Request: %s", c.GetHeader("HX-Request"))

	idStr := c.Param("id")
	log.Printf("DEBUG: RemoveParticipant - ID: %s", idStr)

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		log.Printf("ERROR: RemoveParticipant - Invalid participant ID: %s", idStr)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid participant ID"})
		return
	}

	// Get the participant to find out which event it belongs to
	participant, err := h.DB.GetParticipant(id)
	if err != nil {
		log.Printf("ERROR: RemoveParticipant - Failed to get participant: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Store the event ID for later use
	eventID := participant.EventID
	log.Printf("DEBUG: RemoveParticipant - Participant belongs to event ID: %d", eventID)

	// Remove the participant
	err = h.DB.RemoveParticipantFromEvent(id)
	if err != nil {
		log.Printf("ERROR: RemoveParticipant - Failed to remove participant: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	log.Printf("DEBUG: RemoveParticipant - Successfully removed participant ID: %s", idStr)

	// Get the updated participant count for the event
	count, err := h.DB.CountEventParticipants(eventID)
	if err != nil {
		// If there's an error counting participants, just continue
		log.Printf("ERROR: RemoveParticipant - Error counting participants for event %d: %v", eventID, err)
	}
	log.Printf("DEBUG: RemoveParticipant - Updated participant count for event %d: %d", eventID, count)

	// Return the updated count in the response headers
	c.Header("X-Updated-Participant-Count", strconv.Itoa(count))
	c.Header("X-Event-ID", strconv.FormatInt(eventID, 10))
	log.Printf("DEBUG: RemoveParticipant - Set response headers: X-Updated-Participant-Count=%s, X-Event-ID=%d", strconv.Itoa(count), eventID)

	// Return an empty response with 200 status code
	// This will cause HTMX to remove the row without showing any content
	c.Status(http.StatusOK)
	log.Printf("DEBUG: RemoveParticipant - Returned 200 OK status")
}

// GetParticipantForm handles the participant edit form
func (h *Handler) GetParticipantForm(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Invalid participant ID",
		})
		return
	}

	// Get the participant
	participant, err := h.DB.GetParticipant(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get the sailor information
	sailor, err := h.DB.GetSailor(participant.SailorID)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get the event to check if it's entypsegling
	event, err := h.DB.GetEvent(participant.EventID)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get boat information only for non-entypsegling events
	var boat models.Boat
	var hasBoat bool
	if !event.Entypsegling && participant.BoatID > 0 {
		boat, err = h.DB.GetBoat(participant.BoatID)
		if err != nil {
			c.HTML(http.StatusInternalServerError, "error.html", gin.H{
				"error": err.Error(),
			})
			return
		}
		hasBoat = true
	}

	// Add debug logging
	log.Printf("DEBUG: GetParticipantForm - Participant %d SRS Type: %s", participant.ID, participant.SRSType)
	log.Printf("DEBUG: GetParticipantForm - Participant %d Selected SRS Value: %f", participant.ID, participant.SelectedSRSValue)
	log.Printf("DEBUG: GetParticipantForm - Participant %d Custom SRS Value: %f", participant.ID, participant.CustomSRSValue)
	log.Printf("DEBUG: GetParticipantForm - Participant %d Use Custom SRS Value: %v", participant.ID, participant.UseCustomSRSValue)
	log.Printf("DEBUG: GetParticipantForm - Participant %d Crew Count: %d", participant.ID, participant.CrewCount)

	// Set default SRS type if it's empty
	srsType := participant.SRSType
	if srsType == "" {
		srsType = "srs"
		log.Printf("DEBUG: GetParticipantForm - Setting default SRS type 'srs' for participant %d", participant.ID)

		// Update the database with the default SRS type
		err = h.DB.UpdateParticipantSRS(participant.ID, srsType, participant.SelectedSRSValue, participant.CustomSRSValue, participant.UseCustomSRSValue, participant.CrewCount)
		if err != nil {
			log.Printf("ERROR: GetParticipantForm - Failed to update participant %d with default SRS type: %v", participant.ID, err)
		} else {
			log.Printf("DEBUG: GetParticipantForm - Updated participant %d with default SRS type 'srs'", participant.ID)
		}
	}

	// Render the edit form
	c.HTML(http.StatusOK, "participant_form.html", gin.H{
		"title":             "Redigera deltagare",
		"ID":                participant.ID,
		"EventID":           participant.EventID,
		"Participant":       participant,
		"Sailor":            sailor,
		"Boat":              boat,
		"Event":             event,
		"HasBoat":           hasBoat,
		"SRSType":           srsType,
		"SelectedSRSValue":  participant.SelectedSRSValue,
		"CustomSRSValue":    participant.CustomSRSValue,
		"UseCustomSRSValue": participant.UseCustomSRSValue,
		"CrewCount":         participant.CrewCount,
	})
}

// UpdateParticipant handles updating a participant's SRS values
func (h *Handler) UpdateParticipant(c *gin.Context) {
	idStr := c.PostForm("id")
	eventIDStr := c.PostForm("event_id")
	srsType := c.PostForm("srs_type")
	customSRSValueStr := c.PostForm("custom_srs_value")
	useCustomSRSValue := c.PostForm("use_custom_srs_value") == "on"

	// Validate input
	if idStr == "" || eventIDStr == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Participant ID and Event ID are required",
		})
		return
	}

	// Parse IDs
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Invalid participant ID",
		})
		return
	}

	eventID, err := strconv.ParseInt(eventIDStr, 10, 64)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Invalid event ID",
		})
		return
	}

	// Get the participant to access its boat
	participant, err := h.DB.GetParticipant(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get the event to check if it's entypsegling
	event, err := h.DB.GetEvent(eventID)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Handle personal number update for entypsegling events
	if event.Entypsegling {
		personalNumber := c.PostForm("personal_number")
		crewCountStr := c.PostForm("crew_count")

		// Parse crew count
		var crewCount int
		if crewCountStr != "" {
			crewCount, err = strconv.Atoi(crewCountStr)
			if err != nil || crewCount < 0 {
				log.Printf("Warning: Invalid crew count: %s", crewCountStr)
				crewCount = 0
			}
		}

		// Update personal number and crew count for entypsegling participant
		err = h.DB.UpdateEntypSeglingParticipant(id, personalNumber, crewCount)
		if err != nil {
			c.HTML(http.StatusInternalServerError, "error.html", gin.H{
				"error": err.Error(),
			})
			return
		}

		// Redirect back to the participants page
		c.Redirect(http.StatusSeeOther, fmt.Sprintf("/events/%d/participants", eventID))
		return
	}

	// Get the boat to access its SRS values (for regular events only)
	boat, err := h.DB.GetBoat(participant.BoatID)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Validate the SRS type
	log.Printf("DEBUG: UpdateParticipant - Received SRS Type: %s", srsType)
	switch srsType {
	case "srs", "srs_utan_undanvindsegel", "srs_shorthanded", "srs_shorthanded_utan_undanvindsegel":
		// Valid SRS type, do nothing
		log.Printf("DEBUG: UpdateParticipant - Using valid SRS Type: %s", srsType)
	default:
		// Default to standard SRS
		log.Printf("DEBUG: UpdateParticipant - Invalid SRS Type: %s, defaulting to 'srs'", srsType)
		srsType = "srs"
	}

	// Calculate the correct SRS value based on the SRS type
	selectedSRSValue := h.DB.UpdateSRSValueFromType(boat, srsType)
	log.Printf("DEBUG: UpdateParticipant - SRS Type: %s, Calculated SRS Value: %f", srsType, selectedSRSValue)

	// Parse custom SRS value if provided and checkbox is checked
	var customSRSValue float64
	if useCustomSRSValue && customSRSValueStr != "" {
		customSRSValue, err = strconv.ParseFloat(customSRSValueStr, 64)
		if err != nil {
			// If parsing fails, don't use custom value
			useCustomSRSValue = false
			customSRSValue = 0
		}
	} else {
		// If checkbox is not checked, don't use custom value
		useCustomSRSValue = false
		customSRSValue = 0
	}

	// Parse crew count
	var crewCount int
	crewCountStr := c.PostForm("crew_count")
	if crewCountStr != "" {
		crewCount, err = strconv.Atoi(crewCountStr)
		if err != nil || crewCount < 0 {
			log.Printf("Warning: Invalid crew count: %s", crewCountStr)
			crewCount = 0
		}
	}

	// Update the participant's SRS values and crew count
	err = h.DB.UpdateParticipantSRS(id, srsType, selectedSRSValue, customSRSValue, useCustomSRSValue, crewCount)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Redirect back to the participants page
	c.Redirect(http.StatusSeeOther, fmt.Sprintf("/events/%d/participants", eventID))
}

// GetEventParticipantsList returns the HTML for the participants list
func (h *Handler) GetEventParticipantsList(c *gin.Context) {
	// Get the event ID from the URL
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
		return
	}

	// Get the event to check if it's entypsegling
	event, err := h.DB.GetEvent(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Get the participants for this event
	participants, err := h.DB.GetEventParticipants(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Create enriched participants with sailor and boat information
	var enrichedParticipants []gin.H
	for _, p := range participants {
		sailor, err := h.DB.GetSailor(p.SailorID)
		if err != nil {
			log.Printf("Error fetching sailor %d: %v", p.SailorID, err)
			continue
		}

		// Handle boat information based on event type
		var boat models.Boat
		if event.Entypsegling {
			// For entypsegling events, use a placeholder boat with the event's boat type
			boat = models.Boat{
				ID:     0,
				Namn:   event.BoatType,
				Battyp: event.BoatType,
				SRS:    0,
			}
		} else {
			boat, err = h.DB.GetBoat(p.BoatID)
			if err != nil {
				log.Printf("Error fetching boat %d: %v", p.BoatID, err)
				continue
			}
		}

		enrichedParticipants = append(enrichedParticipants, gin.H{
			"ID":                p.ID,
			"EventID":           p.EventID,
			"SailorID":          p.SailorID,
			"BoatID":            p.BoatID,
			"PersonalNumber":    p.PersonalNumber,
			"SRSType":           p.SRSType,
			"SelectedSRSValue":  p.SelectedSRSValue,
			"CustomSRSValue":    p.CustomSRSValue,
			"UseCustomSRSValue": p.UseCustomSRSValue,
			"CrewCount":         p.CrewCount,
			"Sailor":            sailor,
			"Boat":              boat,
		})
	}

	// Render the participants list template
	c.HTML(http.StatusOK, "participants_list.html", gin.H{
		"participants": enrichedParticipants,
		"event":        event,
	})
}

// GetEventParticipantsPage handles the participants management page
func (h *Handler) GetEventParticipantsPage(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Invalid event ID",
		})
		return
	}

	// Get the event
	event, err := h.DB.GetEvent(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get all sailors and boats for the dropdowns
	sailors, err := h.DB.GetSailors()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	boats, err := h.DB.GetBoats()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get participants for this event
	participants, err := h.DB.GetEventParticipants(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Fetch detailed information for each participant
	var enrichedParticipants []gin.H
	for _, p := range participants {
		sailor, err := h.DB.GetSailor(p.SailorID)
		if err != nil {
			log.Printf("Error fetching sailor %d: %v", p.SailorID, err)
			continue
		}

		// For entypsegling events, boat_id might be NULL
		var boat models.Boat
		if event.Entypsegling {
			// For entypsegling events, we don't need boat information
			// Use a placeholder boat with the event's boat type
			boat = models.Boat{
				ID:     0,
				Namn:   event.BoatType,
				Battyp: event.BoatType,
				SRS:    0,
			}
		} else {
			boat, err = h.DB.GetBoat(p.BoatID)
			if err != nil {
				log.Printf("Error fetching boat %d: %v", p.BoatID, err)
				continue
			}
		}

		enrichedParticipants = append(enrichedParticipants, gin.H{
			"ID":                p.ID,
			"EventID":           p.EventID,
			"SailorID":          p.SailorID,
			"BoatID":            p.BoatID,
			"PersonalNumber":    p.PersonalNumber,
			"SRSType":           p.SRSType,
			"SelectedSRSValue":  p.SelectedSRSValue,
			"CustomSRSValue":    p.CustomSRSValue,
			"UseCustomSRSValue": p.UseCustomSRSValue,
			"CrewCount":         p.CrewCount,
			"Sailor":            sailor,
			"Boat":              boat,
		})
	}

	// Get all events for the copy dropdown
	events, err := h.DB.GetEvents()
	if err != nil {
		log.Printf("Error fetching events: %v", err)
		// Continue anyway, we'll just have an empty dropdown
	}

	c.HTML(http.StatusOK, "event_participants.html", gin.H{
		"title":                  "Hantera deltagare",
		"event":                  event,
		"sailors":                sailors,
		"boats":                  boats,
		"participants":           enrichedParticipants,
		"events":                 events,
		"allEvents":              events,
		"selectedEventID":        id,
		"selectedEventJaktstart": event.Jaktstart,
		"activeMenu":             "participants",
		"use24hTime":             h.getTimeFormatSetting(),
	})
}

// CopyParticipants handles copying participants from one event to another
func (h *Handler) CopyParticipants(c *gin.Context) {
	targetEventIDStr := c.PostForm("target_event_id")
	sourceEventIDStr := c.PostForm("source_event_id")

	// Validate input
	if targetEventIDStr == "" || sourceEventIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Target event ID and source event ID are required"})
		return
	}

	// Parse IDs
	targetEventID, err := strconv.ParseInt(targetEventIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid target event ID"})
		return
	}
	sourceEventID, err := strconv.ParseInt(sourceEventIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid source event ID"})
		return
	}

	// Don't allow copying from the same event
	if targetEventID == sourceEventID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot copy participants from the same event"})
		return
	}

	// Copy participants
	count, err := h.DB.CopyParticipantsFromEvent(sourceEventID, targetEventID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Get all participants for the target event to return updated list
	participants, err := h.DB.GetEventParticipants(targetEventID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Fetch detailed information for each participant
	var enrichedParticipants []gin.H
	for _, p := range participants {
		sailor, err := h.DB.GetSailor(p.SailorID)
		if err != nil {
			log.Printf("Error fetching sailor %d: %v", p.SailorID, err)
			continue
		}

		boat, err := h.DB.GetBoat(p.BoatID)
		if err != nil {
			log.Printf("Error fetching boat %d: %v", p.BoatID, err)
			continue
		}

		enrichedParticipants = append(enrichedParticipants, gin.H{
			"ID":                p.ID,
			"EventID":           p.EventID,
			"SailorID":          p.SailorID,
			"BoatID":            p.BoatID,
			"SRSType":           p.SRSType,
			"SelectedSRSValue":  p.SelectedSRSValue,
			"CustomSRSValue":    p.CustomSRSValue,
			"UseCustomSRSValue": p.UseCustomSRSValue,
			"Sailor":            sailor,
			"Boat":              boat,
		})
	}

	// Set the count in the response header
	c.Header("X-Copied-Count", strconv.Itoa(count))

	// We no longer update participant count
	c.Header("X-Updated-Participant-Count", "0")
	c.Header("X-Event-ID", strconv.FormatInt(targetEventID, 10))

	// Return the updated participants list
	c.HTML(http.StatusOK, "participants_list.html", gin.H{
		"participants": enrichedParticipants,
	})
}

// RegisterRoutes sets up the HTTP routes
func (h *Handler) RegisterRoutes(router *gin.Engine) {
	// Home page
	router.GET("/", h.Home)

	// About page
	router.GET("/about", h.GetAbout)

	// Help page
	router.GET("/help", h.GetHelp)

	// Settings
	router.GET("/settings", h.GetSettings)
	router.POST("/settings", h.PostSettings)
	router.POST("/settings/club", h.PostClubSetting)
	router.POST("/settings/time-format", h.PostTimeFormatSetting)
	router.POST("/settings/competition-types", h.PostCompetitionTypesSetting)
	router.POST("/settings/github-pages", h.PostGitHubPagesSettings)
	router.POST("/settings/google-drive", h.PostGoogleDriveSettings)
	router.POST("/settings/weather", h.PostWeatherSettings)
	router.POST("/settings/upload-client-secrets", h.UploadClientSecrets)
	router.POST("/settings/checkpoint-wal", h.PostCheckpointWAL)

	// Backup management
	router.GET("/backups", h.GetBackups)
	router.POST("/backups/restore", h.RestoreBackup)

	// Sailors
	router.GET("/sailors", h.GetSailors)
	router.POST("/sailors", h.PostSailor)
	router.GET("/sailors/:id", h.GetSailorForm)
	router.DELETE("/sailors/:id", h.DeleteSailor)
	router.GET("/api/sailors/search", h.SearchSailorsLive)
	router.GET("/sailors/search", h.SearchSailorsLive) // Add this route to match the template

	// Boats
	router.GET("/boats", h.GetBoats)
	router.POST("/boats", h.PostBoat)
	router.GET("/boats/:id", h.GetBoatForm)
	router.GET("/boats/:id/json", h.GetBoatJSON)
	router.DELETE("/boats/:id", h.DeleteBoat)
	router.GET("/api/boats/search", h.SearchBoatsLive)
	router.GET("/boats/search", h.SearchBoatsLive) // Add this route to match the template

	// Events
	router.GET("/events", h.GetEvents)
	router.GET("/events/:id", h.GetEventForm)
	router.POST("/events", h.PostEvent)
	router.DELETE("/events/:id", h.DeleteEvent)
	router.GET("/api/events", h.GetEventsAPI)
	router.GET("/api/events/:id", h.GetEventAPI)
	router.GET("/api/events/:id/participant-count", h.GetEventParticipantCountAPI)

	// Event participants
	router.GET("/events/:id/participants", h.GetEventParticipantsPage) // New route for participants management page
	router.POST("/events/:id/participants", h.AddParticipant)
	router.GET("/events/:id/participants/list", h.GetEventParticipantsList) // Renamed to avoid conflict
	router.DELETE("/events/:id/participants/:participantId", h.RemoveParticipant)
	router.POST("/participants/copy", h.CopyParticipants)
	router.POST("/participants/update", h.UpdateParticipant)
	router.POST("/participants", h.AddParticipant) // Add this route for compatibility
	router.GET("/participants/:id", h.GetParticipantForm)
	router.DELETE("/participants/:id", h.RemoveParticipant)

	// Jaktstart
	router.GET("/events/:id/jaktstart", h.GetJaktstartTimes)
	router.GET("/events/:id/jaktstart/print", h.GetJaktstartTimesPrint)

	// Finish times
	router.GET("/events/:id/finish-times", h.GetFinishTimes)
	router.POST("/events/:id/finish-times/:participant_id", h.UpdateFinishTime)
	router.POST("/events/:id/finish-times/:participant_id/dns", h.UpdateParticipantDNS)
	router.POST("/events/:id/finish-times/:participant_id/dnf", h.UpdateParticipantDNF)

	// Results
	router.GET("/events/:id/results", h.GetResults)
	router.GET("/events/:id/results/print", h.GetResultsPrint)
	router.GET("/events/:id/results/csv", h.ExportResultsCSV)
	router.POST("/events/:id/publish-github-pages", h.PublishToGitHubPages)
	router.GET("/events/:id/test-github-pages", h.TestGitHubPages) // Test route
	router.POST("/events/:id/export-google-drive", h.ExportResultsToGoogleDrive)
	router.POST("/events/:id/update-discard-setting", h.UpdateDiscardSetting)

	// Google Drive authentication
	router.GET("/google-drive/auth", h.GetGoogleDriveAuth)
	router.POST("/google-drive/auth", h.PostGoogleDriveAuth)
	router.GET("/settings/google-drive/callback", h.GetGoogleDriveCallback)

	// GitHub Pages
	router.GET("/github-pages", h.GetGitHubPages)
	router.GET("/api/github-pages", h.ListGitHubPages)
	router.DELETE("/api/github-pages/*filename", h.DeleteFromGitHubPages)
	router.GET("/api/github-pages/check", h.CheckPageAvailability)

	// TEMPORARILY DISABLED: Lock/unlock events
	// router.POST("/events/:id/lock", h.LockEvent)
	// router.POST("/events/:id/unlock", h.UnlockEvent)

	// Starter boat information
	router.GET("/events/:id/starter-boat", h.GetStarterBoat)
	router.GET("/events/:id/starter-boat/print", h.GetStarterBoatPrint)

	// SRS data
	router.GET("/srs", h.GetSRSManagement)
	router.GET("/srs/boat-types", h.GetSRSBoatTypes)
	router.GET("/srs/matbrev", h.GetSRSMatbrev)
	router.GET("/srs/matbrev/search", h.SearchSRSMatbrevLive)
	router.GET("/srs/boat-types/search", h.SearchSRSBoatTypesLive)
	router.POST("/srs/sync", h.SyncSRSData)
	router.GET("/api/srs", h.GetSRSData)
	router.GET("/api/srs/search", h.SearchSRSData)
	router.GET("/api/matbrev/search", h.SearchMatbrevData)
	router.GET("/api/matbrev", h.GetMatbrevData)

	// SRS data export/import
	router.GET("/srs/boat-types/export", h.ExportSRSBoatTypesCSV)
	router.GET("/srs/matbrev/export", h.ExportSRSMatbrevCSV)
	router.POST("/srs/boat-types/import", h.ImportSRSBoatTypesCSV)
	router.POST("/srs/matbrev/import", h.ImportSRSMatbrevCSV)
	router.POST("/srs/data/import", h.ImportSRSDataCSV)

	// Heat management
	router.GET("/events/:id/heats", h.GetEventHeats)
	router.POST("/events/:id/heats", h.CreateHeat)
	router.DELETE("/heats/:id", h.DeleteHeat)
	router.GET("/api/events/:id/heats", h.GetEventHeatsAPI)
	router.POST("/heats/:heat_id/start-time", h.UpdateHeatStartTime)
	router.POST("/heats/:heat_id/finish-times/:participant_id", h.UpdateHeatFinishTime)
	router.POST("/heats/:heat_id/placements/:participant_id", h.UpdateHeatPlacement)
	router.GET("/heats/:id/results", h.GetHeatResults)

	// Weather API
	router.GET("/api/weather", h.GetWeather)
}

// GetWeather handles weather API requests
func (h *Handler) GetWeather(c *gin.Context) {
	weatherData, err := h.WeatherService.GetWeatherData()
	if err != nil {
		log.Printf("Error fetching weather data: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Kunde inte hämta väderdata",
		})
		return
	}

	// Add wind direction text for easier display
	response := gin.H{
		"wind_speed":          weatherData.WindSpeed,
		"wind_direction":      weatherData.WindDirection,
		"wind_speed_max":      weatherData.WindSpeedMax,
		"wind_speed_average":  weatherData.WindSpeedAverage,
		"temperature":         weatherData.Temperature,
		"wind_direction_text": weatherData.GetWindDirectionText(),
		"wind_direction_name": weatherData.GetWindDirectionFullName(),
		"last_updated":        weatherData.LastUpdated,
		"location":            weatherData.Location,
	}

	c.JSON(http.StatusOK, response)
}

// GetBackups displays the backup management page
func (h *Handler) GetBackups(c *gin.Context) {
	// Get all events for the dropdown
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		allEvents = []models.Event{}
	}

	// Get the list of available backups
	backups, err := h.getBackupFiles()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte hämta säkerhetskopior: %v", err),
		})
		return
	}

	c.HTML(http.StatusOK, "backups.html", gin.H{
		"title":     "Säkerhetskopior",
		"backups":   backups,
		"allEvents": allEvents,
	})
}

// getBackupFiles returns a list of available backup files
func (h *Handler) getBackupFiles() ([]models.Backup, error) {
	// Try to get the database path
	var dbPath string
	var seq int
	var name string
	err := h.DB.QueryRow("PRAGMA database_list").Scan(&seq, &name, &dbPath)
	if err != nil {
		return nil, fmt.Errorf("could not get database path: %w", err)
	}

	// If we can't get the path, use the default
	if dbPath == "" || dbPath == ":memory:" {
		dbPath = "segling.db"
	}

	// Get the backups directory
	backupDir := filepath.Join(filepath.Dir(dbPath), "backups")

	// Check if the directory exists
	if _, err := os.Stat(backupDir); os.IsNotExist(err) {
		return []models.Backup{}, nil
	}

	// Read the directory
	files, err := os.ReadDir(backupDir)
	if err != nil {
		return nil, fmt.Errorf("could not read backups directory: %w", err)
	}

	// Filter and sort backup files
	var backups []models.Backup
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".db") {
			info, err := file.Info()
			if err != nil {
				continue
			}

			backups = append(backups, models.Backup{
				Filename:  file.Name(),
				Size:      info.Size(),
				Timestamp: info.ModTime(),
			})
		}
	}

	// Sort backups by timestamp (newest first)
	sort.Slice(backups, func(i, j int) bool {
		return backups[i].Timestamp.After(backups[j].Timestamp)
	})

	return backups, nil
}

// Helper function to get the time format setting
func (h *Handler) getTimeFormatSetting() bool {
	// Get the time format setting
	use24hTime, err := h.DB.GetSetting("use_24h_time")
	if err != nil {
		use24hTime = "true" // Fallback to 24h if setting not found
		log.Printf("Error getting use_24h_time setting: %v", err)
	}
	return use24hTime == "true"
}

// RestoreBackup restores a database from a backup file
func (h *Handler) RestoreBackup(c *gin.Context) {
	filename := c.PostForm("filename")
	if filename == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Ingen säkerhetskopia vald",
		})
		return
	}

	// Validate the filename to prevent directory traversal
	if strings.Contains(filename, "..") || strings.Contains(filename, "/") || strings.Contains(filename, "\\") {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Ogiltigt filnamn",
		})
		return
	}

	// Try to get the database path
	var dbPath string
	var seq int
	var name string
	err := h.DB.QueryRow("PRAGMA database_list").Scan(&seq, &name, &dbPath)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte hämta databasväg: %v", err),
		})
		return
	}

	// If we can't get the path, use the default
	if dbPath == "" || dbPath == ":memory:" {
		dbPath = "segling.db"
	}

	// Get the backups directory
	backupDir := filepath.Join(filepath.Dir(dbPath), "backups")
	backupPath := filepath.Join(backupDir, filename)

	// Check if the backup file exists
	if _, err := os.Stat(backupPath); os.IsNotExist(err) {
		c.HTML(http.StatusNotFound, "error.html", gin.H{
			"error": "Säkerhetskopian hittades inte",
		})
		return
	}

	// Create a backup of the current database before restoring
	currentBackupPath := filepath.Join(backupDir, fmt.Sprintf("pre_restore_%s_%s",
		time.Now().Format("20060102_150405"),
		filepath.Base(dbPath)))

	// Close the database connection before copying
	h.DB.Close()

	// Copy the current database to the backup
	err = utils.CopyFile(dbPath, currentBackupPath)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte säkerhetskopiera nuvarande databas: %v", err),
		})
		return
	}

	// Copy the backup to the current database
	err = utils.CopyFile(backupPath, dbPath)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte återställa från säkerhetskopia: %v", err),
		})
		return
	}

	// Reopen the database connection
	newDB, err := database.New(dbPath)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": fmt.Sprintf("Kunde inte återansluta till databasen: %v", err),
		})
		return
	}
	*h.DB = *newDB

	// Redirect to the backups page with a success message
	c.Redirect(http.StatusSeeOther, "/backups?success=true")
}
