package handlers

import (
	"bytes"
	"crypto/sha256"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"html/template"
	"io"
	"log"
	"net/http"
	"net/url"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
	"unicode"

	"github.com/gin-gonic/gin"
	"github.com/rb<PERSON>regren/segling/pkg/models"
)

// titleCase converts a string to title case
func titleCase(s string) string {
	// Split the string into words
	words := strings.Fields(s)
	for i, word := range words {
		if len(word) == 0 {
			continue
		}

		runes := []rune(word)
		// Capitalize the first letter
		runes[0] = unicode.ToUpper(runes[0])
		// Convert the rest to lowercase
		for j := 1; j < len(runes); j++ {
			runes[j] = unicode.ToLower(runes[j])
		}
		words[i] = string(runes)
	}
	return strings.Join(words, " ")
}

// Note: This function has been replaced with slices.Contains from the standard library

// GitHub API structures
type GitHubContent struct {
	Message string `json:"message"`
	Content string `json:"content"`
	SHA     string `json:"sha,omitempty"`
	Branch  string `json:"branch,omitempty"`
	Name    string `json:"name,omitempty"` // Used in directory listing responses
	Type    string `json:"type,omitempty"` // Used in directory listing responses
	Path    string `json:"path,omitempty"` // Used in directory listing responses
}

type GitHubContentResponse struct {
	SHA  string `json:"sha"`
	Name string `json:"name"`
	Type string `json:"type"`
}

// calculateChecksum calculates a SHA-256 checksum of a string
func calculateChecksum(content string) string {
	hash := sha256.Sum256([]byte(content))
	return fmt.Sprintf("%x", hash)
}

// fetchPublishedPage fetches a published page from GitHub Pages
func (h *Handler) fetchPublishedPage(url string) (string, error) {
	// Add a timestamp parameter to bypass caching
	separator := "?"
	if strings.Contains(url, "?") {
		separator = "&"
	}
	urlWithTimestamp := fmt.Sprintf("%s%st=%d", url, separator, time.Now().Unix())

	log.Printf("fetchPublishedPage: Fetching page from %s", urlWithTimestamp)

	// Create a client with a timeout and transport that disables caching
	transport := &http.Transport{
		// Disable HTTP/2 as it can sometimes cause issues with cache control
		TLSNextProto: make(map[string]func(authority string, c *tls.Conn) http.RoundTripper),
	}

	client := &http.Client{
		Timeout:   10 * time.Second,
		Transport: transport,
	}

	// Create a request with cache-busting headers
	req, err := http.NewRequest("GET", urlWithTimestamp, nil)
	if err != nil {
		log.Printf("fetchPublishedPage: Error creating request: %v", err)
		return "", err
	}

	// Add cache-busting headers
	req.Header.Add("Cache-Control", "no-cache, no-store, must-revalidate")
	req.Header.Add("Pragma", "no-cache")
	req.Header.Add("Expires", "0")

	// Send the request
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("fetchPublishedPage: Error fetching page: %v", err)
		return "", err
	}
	defer resp.Body.Close()

	// Check the response status
	if resp.StatusCode != http.StatusOK {
		log.Printf("fetchPublishedPage: Unexpected status code: %d", resp.StatusCode)
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	// Read the response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("fetchPublishedPage: Error reading response body: %v", err)
		return "", err
	}

	return string(body), nil
}

// GetGitHubPages renders the GitHub Pages management page
func (h *Handler) GetGitHubPages(c *gin.Context) {
	// Get GitHub Pages settings
	githubPagesEnabled, err := h.DB.GetSetting("github_pages_enabled")
	if err != nil {
		githubPagesEnabled = "false" // Default to disabled if setting not found
	}

	githubPagesRepo, err := h.DB.GetSetting("github_pages_repo")
	if err != nil {
		githubPagesRepo = "" // Default to empty if setting not found
	}

	githubPagesToken, err := h.DB.GetSetting("github_pages_token")
	if err != nil {
		githubPagesToken = "" // Default to empty if setting not found
	}

	// Render the GitHub Pages management page
	c.HTML(http.StatusOK, "github_pages.html", gin.H{
		"title":              "GitHub Pages",
		"githubPagesEnabled": githubPagesEnabled == "true",
		"githubPagesRepo":    githubPagesRepo,
		"githubPagesToken":   githubPagesToken != "",
	})
}

// CheckPageAvailability checks if a published page is available with the expected content
func (h *Handler) CheckPageAvailability(c *gin.Context) {
	// Get the URL and expected checksum from the query parameters
	url := c.Query("url")
	expectedChecksum := c.Query("checksum")

	if url == "" || expectedChecksum == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Missing URL or checksum",
		})
		return
	}

	log.Printf("CheckPageAvailability: Checking if page %s is available with checksum %s", url, expectedChecksum)

	// Fetch the page
	content, err := h.fetchPublishedPage(url)
	if err != nil {
		log.Printf("CheckPageAvailability: Error fetching page: %v", err)
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Error fetching page: %v", err),
		})
		return
	}

	// Calculate the checksum
	checksum := calculateChecksum(content)
	log.Printf("CheckPageAvailability: Fetched page checksum: %s", checksum)

	// Check if the checksums match
	if checksum == expectedChecksum {
		log.Printf("CheckPageAvailability: Checksums match! Page is available with the correct content.")
		c.JSON(http.StatusOK, gin.H{
			"success":  true,
			"message":  "Page is available with the correct content",
			"checksum": checksum,
		})
	} else {
		log.Printf("CheckPageAvailability: Checksums do not match. Page is not yet updated.")
		c.JSON(http.StatusOK, gin.H{
			"success":  false,
			"message":  "Page is available but content does not match expected checksum",
			"checksum": checksum,
		})
	}
}

// PublishToGitHubPages publishes event results to GitHub Pages
func (h *Handler) PublishToGitHubPages(c *gin.Context) {
	log.Printf("=== PublishToGitHubPages: FUNCTION CALLED ===")
	log.Printf("PublishToGitHubPages: Request method: %s", c.Request.Method)
	log.Printf("PublishToGitHubPages: Request URL: %s", c.Request.URL.String())
	log.Printf("PublishToGitHubPages: Request received")
	log.Printf("PublishToGitHubPages: Starting GitHub Pages publishing process")

	// Ensure we always return JSON, even on errors
	defer func() {
		if r := recover(); r != nil {
			log.Printf("PublishToGitHubPages: PANIC RECOVERED: %v", r)
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Internal server error: %v", r)})
		}
	}()

	// Get the event ID from the URL
	idStr := c.Param("id")
	log.Printf("PublishToGitHubPages: Event ID string: %s", idStr)
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		log.Printf("PublishToGitHubPages: Invalid event ID: %v", err)
		log.Printf("PublishToGitHubPages: Returning BadRequest JSON response")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
		return
	}
	log.Printf("PublishToGitHubPages: Parsed event ID: %d", id)

	// Get the event
	log.Printf("PublishToGitHubPages: Getting event with ID %d", id)
	event, err := h.DB.GetEvent(id)
	if err != nil {
		log.Printf("PublishToGitHubPages: Event not found: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Kunde inte hämta tävling: %v", err)})
		return
	}
	log.Printf("PublishToGitHubPages: Found event: %s", event.Namn)

	// Check if GitHub Pages integration is enabled
	log.Printf("PublishToGitHubPages: Checking GitHub Pages settings")
	githubPagesEnabled, err := h.DB.GetSetting("github_pages_enabled")
	if err != nil || githubPagesEnabled != "true" {
		log.Printf("PublishToGitHubPages: GitHub Pages not enabled: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "GitHub Pages integration är inte aktiverad. Aktivera den i inställningarna."})
		return
	}
	log.Printf("PublishToGitHubPages: GitHub Pages is enabled")

	// Get GitHub Pages settings
	log.Printf("PublishToGitHubPages: Getting GitHub repository setting")
	githubPagesRepo, err := h.DB.GetSetting("github_pages_repo")
	if err != nil || githubPagesRepo == "" {
		log.Printf("PublishToGitHubPages: GitHub repository not configured: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "GitHub repository är inte konfigurerat. Konfigurera det i inställningarna."})
		return
	}
	log.Printf("PublishToGitHubPages: GitHub repository: %s", githubPagesRepo)

	log.Printf("PublishToGitHubPages: Getting GitHub branch setting")
	githubPagesBranch, err := h.DB.GetSetting("github_pages_branch")
	if err != nil || githubPagesBranch == "" {
		githubPagesBranch = "gh-pages" // Default to gh-pages
		log.Printf("PublishToGitHubPages: Using default branch: %s", githubPagesBranch)
	} else {
		log.Printf("PublishToGitHubPages: Using configured branch: %s", githubPagesBranch)
	}

	log.Printf("PublishToGitHubPages: Getting GitHub token setting")
	githubPagesToken, err := h.DB.GetSetting("github_pages_token")
	if err != nil || githubPagesToken == "" {
		log.Printf("PublishToGitHubPages: GitHub token not configured: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "GitHub token är inte konfigurerat. Konfigurera det i inställningarna."})
		return
	}
	log.Printf("PublishToGitHubPages: GitHub token is configured (length: %d)", len(githubPagesToken))

	githubPagesTemplate, err := h.DB.GetSetting("github_pages_template")
	if err != nil || githubPagesTemplate == "" {
		githubPagesTemplate = "default" // Default to default template
	}

	// Get heats for this event
	heats, err := h.DB.GetEventHeats(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Kunde inte hämta heats: %v", err)})
		return
	}

	// Get selected heat from query parameter
	selectedHeatIDStr := c.Query("heat")
	var selectedHeat models.Heat
	var showOverall bool = false
	var isMultiHeat bool = len(heats) > 1

	if selectedHeatIDStr != "" {
		if selectedHeatIDStr == "overall" {
			showOverall = true
		} else {
			selectedHeatID, err := strconv.ParseInt(selectedHeatIDStr, 10, 64)
			if err == nil {
				for _, heat := range heats {
					if heat.ID == selectedHeatID {
						selectedHeat = heat
						break
					}
				}
			}
		}
	}

	// Determine what to show based on event type and selection
	if isMultiHeat {
		// For multi-heat events, default to overall if no specific heat selected
		if selectedHeat.ID == 0 && !showOverall {
			showOverall = true
		}
	} else {
		// For single-heat events, always show the single heat
		if len(heats) > 0 {
			selectedHeat = heats[0]
		}
		showOverall = false
	}

	// Get the results for the event
	var savedResults []models.SavedResult
	var usingSavedResults bool
	var totalResults []models.TotalResult
	var heatResults []models.HeatResult

	// Get the appropriate results based on selection
	if event.Locked {
		log.Printf("PublishToGitHubPages: Event is locked, getting saved results")
		savedResults, err = h.DB.GetSavedResults(id)
		if err != nil {
			log.Printf("PublishToGitHubPages: Error getting saved results: %v", err)
			// Fall back to calculated results
			event.Locked = false // Temporarily treat as unlocked for fallback
		} else {
			usingSavedResults = true
			log.Printf("PublishToGitHubPages: Found %d saved results for event", len(savedResults))
		}
	}

	if !event.Locked {
		// Calculate results based on selection
		if showOverall && isMultiHeat {
			log.Printf("PublishToGitHubPages: Getting overall results for multi-heat event")
			totalResults, err = h.DB.GetEventTotalResults(id)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Kunde inte hämta resultat: %v", err)})
				return
			}
			log.Printf("PublishToGitHubPages: Found %d total results", len(totalResults))
		} else if selectedHeat.ID != 0 {
			log.Printf("PublishToGitHubPages: Getting results for heat %d", selectedHeat.ID)
			heatResults, err = h.DB.GetHeatResultsComplete(selectedHeat.ID)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Kunde inte hämta resultat: %v", err)})
				return
			}
			log.Printf("PublishToGitHubPages: Found %d heat results", len(heatResults))
		} else {
			log.Printf("PublishToGitHubPages: No valid heat or overall selection")
			heatResults = []models.HeatResult{}
		}
	}

	// Generate HTML content for GitHub Pages
	var results []models.Result
	if !usingSavedResults && selectedHeat.ID != 0 {
		// Convert heat results to regular results format for HTML generation
		results = h.convertHeatResultsToResultsForPublish(heatResults)
	}

	// Debug logging
	log.Printf("PublishToGitHubPages: Generating HTML with data:")
	log.Printf("  - usingSavedResults: %v, savedResults count: %d", usingSavedResults, len(savedResults))
	log.Printf("  - showOverall: %v, totalResults count: %d", showOverall, len(totalResults))
	log.Printf("  - selectedHeat: %v, heatResults count: %d", selectedHeat.Name, len(heatResults))
	log.Printf("  - results count: %d", len(results))
	log.Printf("  - heats count: %d", len(heats))
	log.Printf("  - isMultiHeat: %v", isMultiHeat)

	htmlContent, err := h.generateGitHubPagesHTML(event, results, savedResults, usingSavedResults, totalResults, heatResults, heats, selectedHeat, showOverall, isMultiHeat, githubPagesTemplate)
	if err != nil {
		log.Printf("PublishToGitHubPages: Error generating HTML: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Kunde inte generera HTML: %v", err)})
		return
	}

	// Calculate checksum of the HTML content
	contentChecksum := calculateChecksum(htmlContent)
	log.Printf("PublishToGitHubPages: Content checksum: %s", contentChecksum)
	log.Printf("PublishToGitHubPages: Generated HTML content length: %d bytes", len(htmlContent))

	// Log first 500 characters of HTML content for debugging
	if len(htmlContent) > 500 {
		log.Printf("PublishToGitHubPages: HTML content preview: %s...", htmlContent[:500])
	} else {
		log.Printf("PublishToGitHubPages: Full HTML content: %s", htmlContent)
	}

	// Get the year from the event date
	eventYear := event.Datum.Format("2006")

	// Get the competition type, default to "Kvällssegling" if empty
	competitionType := event.Tavlingstyp
	if competitionType == "" {
		competitionType = "Kvällssegling"
	}

	// Generate filename for the event - include heat information
	var baseFilename string
	if showOverall && isMultiHeat {
		baseFilename = fmt.Sprintf("%s-%s-totala.html", event.Datum.Format("2006-01-02"), strings.ReplaceAll(strings.ToLower(event.Namn), " ", "-"))
	} else if selectedHeat.ID != 0 && isMultiHeat {
		// Only include heat name for multi-heat events
		cleanHeatName := strings.ReplaceAll(strings.ToLower(selectedHeat.Name), " ", "-")
		cleanHeatName = strings.ReplaceAll(cleanHeatName, "/", "-")
		cleanHeatName = strings.ReplaceAll(cleanHeatName, "\\", "-")
		baseFilename = fmt.Sprintf("%s-%s-%s.html", event.Datum.Format("2006-01-02"), strings.ReplaceAll(strings.ToLower(event.Namn), " ", "-"), cleanHeatName)
	} else {
		// Single heat events or no specific heat selected
		baseFilename = fmt.Sprintf("%s-%s.html", event.Datum.Format("2006-01-02"), strings.ReplaceAll(strings.ToLower(event.Namn), " ", "-"))
	}

	// Create the full path with year and competition type directories
	yearPath := eventYear
	competitionTypePath := fmt.Sprintf("%s/%s", eventYear, competitionType)
	filename := fmt.Sprintf("%s/%s/%s", eventYear, competitionType, baseFilename)

	// Create year directory if it doesn't exist
	err = h.ensureGitHubDirectory(githubPagesRepo, githubPagesBranch, githubPagesToken, yearPath)
	if err != nil {
		log.Printf("Warning: Could not ensure year directory exists: %v", err)
		// Continue anyway, the upload might still work
	}

	// Create competition type directory if it doesn't exist
	err = h.ensureGitHubDirectory(githubPagesRepo, githubPagesBranch, githubPagesToken, competitionTypePath)
	if err != nil {
		log.Printf("Warning: Could not ensure competition type directory exists: %v", err)
		// Continue anyway, the upload might still work
	}

	// Upload to GitHub
	log.Printf("PublishToGitHubPages: Uploading to GitHub: repo=%s, branch=%s, filename=%s, content_length=%d", githubPagesRepo, githubPagesBranch, filename, len(htmlContent))
	err = h.uploadToGitHub(githubPagesRepo, githubPagesBranch, githubPagesToken, filename, htmlContent, event.Namn)
	if err != nil {
		log.Printf("PublishToGitHubPages: Upload to GitHub failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Kunde inte ladda upp till GitHub: %v", err)})
		return
	}
	log.Printf("PublishToGitHubPages: Successfully uploaded to GitHub")

	// Generate year-specific index.html
	err = h.ensureYearIndexHTML(githubPagesRepo, githubPagesBranch, githubPagesToken, eventYear, baseFilename, event.Namn)
	if err != nil {
		log.Printf("Warning: Could not create/update year index.html: %v", err)
		// Continue anyway, the event page was uploaded successfully
	}

	// Generate main index.html
	err = h.ensureIndexHTML(githubPagesRepo, githubPagesBranch, githubPagesToken, "", "")
	if err != nil {
		log.Printf("Warning: Could not create/update main index.html: %v", err)
		// Continue anyway, the event page was uploaded successfully
	}

	// Generate the URL to the published page
	publishedUrl := fmt.Sprintf("https://%s.github.io/%s/%s",
		strings.Split(githubPagesRepo, "/")[0],
		strings.Split(githubPagesRepo, "/")[1],
		filename)

	// Return success with the URL to the published page and the content checksum
	log.Printf("PublishToGitHubPages: Returning success response")
	response := gin.H{
		"success":  true,
		"message":  "Resultat publicerade till GitHub Pages",
		"url":      publishedUrl,
		"checksum": contentChecksum,
	}
	log.Printf("PublishToGitHubPages: Success response prepared")
	c.JSON(http.StatusOK, response)
	log.Printf("PublishToGitHubPages: JSON response sent")
}

// TestGitHubPages is a simple test endpoint to verify routing and JSON responses
func (h *Handler) TestGitHubPages(c *gin.Context) {
	log.Printf("=== TestGitHubPages: FUNCTION CALLED ===")
	eventID := c.Param("id")
	log.Printf("TestGitHubPages: Event ID: %s", eventID)

	response := gin.H{
		"success": true,
		"message": "Test endpoint working",
		"eventId": eventID,
	}
	log.Printf("TestGitHubPages: Returning JSON: %+v", response)
	c.JSON(http.StatusOK, response)
	log.Printf("TestGitHubPages: JSON response sent")
}

// Note: This function has been removed as it's not used directly.
// The functionality is available through listGitHubFilesInPath with an empty path.

// listGitHubFilesInPath lists files in a specific path of a GitHub repository with a specific extension
func (h *Handler) listGitHubFilesInPath(repo, branch, token, path, extension string) ([]string, error) {
	log.Printf("listGitHubFilesInPath: Listing files in %s/%s path=%s with extension %s", repo, branch, path, extension)

	// GitHub API URL for listing repository contents
	apiURL := fmt.Sprintf("https://api.github.com/repos/%s/contents/%s?ref=%s", repo, path, branch)
	log.Printf("listGitHubFilesInPath: API URL: %s", apiURL)

	// Create the HTTP request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		log.Printf("listGitHubFilesInPath: Error creating request: %v", err)
		return nil, fmt.Errorf("could not create request: %w", err)
	}

	// Set headers
	req.Header.Set("Authorization", "token "+token)
	req.Header.Set("Accept", "application/vnd.github.v3+json")
	log.Printf("listGitHubFilesInPath: Request headers set")

	// Send the request
	client := &http.Client{
		Timeout: 30 * time.Second, // Add a timeout to prevent hanging
	}
	log.Printf("listGitHubFilesInPath: Sending request to GitHub API")
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("listGitHubFilesInPath: Error sending request: %v", err)
		return nil, fmt.Errorf("could not send request: %w", err)
	}
	defer resp.Body.Close()
	log.Printf("listGitHubFilesInPath: Response status: %d", resp.StatusCode)

	// Check for errors
	if resp.StatusCode != http.StatusOK {
		// Read the response body for more details
		bodyBytes, _ := io.ReadAll(resp.Body)
		log.Printf("listGitHubFilesInPath: GitHub API error response: %s", string(bodyBytes))
		return nil, fmt.Errorf("GitHub API returned status code %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse the response
	var contents []GitHubContentResponse
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("listGitHubFilesInPath: Error reading response body: %v", err)
		return nil, fmt.Errorf("could not read response body: %w", err)
	}

	log.Printf("listGitHubFilesInPath: Response received (%d bytes)", len(bodyBytes))

	err = json.Unmarshal(bodyBytes, &contents)
	if err != nil {
		log.Printf("listGitHubFilesInPath: Error decoding response: %v", err)
		return nil, fmt.Errorf("could not decode response: %w", err)
	}
	log.Printf("listGitHubFilesInPath: Parsed %d items from response", len(contents))

	// Filter files by extension and process directories
	var files []string
	for _, content := range contents {
		log.Printf("listGitHubFilesInPath: Processing item: %s (type: %s)", content.Name, content.Type)
		if content.Type == "file" && (extension == "" || filepath.Ext(content.Name) == extension) {
			// Add path prefix if we're in a subdirectory
			if path == "" {
				files = append(files, content.Name)
			} else {
				files = append(files, path+"/"+content.Name)
			}
			log.Printf("listGitHubFilesInPath: Added file: %s", content.Name)
		} else if content.Type == "dir" {
			// Recursively list files in subdirectories
			subPath := content.Name
			if path != "" {
				subPath = path + "/" + content.Name
			}
			subFiles, err := h.listGitHubFilesInPath(repo, branch, token, subPath, extension)
			if err != nil {
				log.Printf("listGitHubFilesInPath: Error listing files in subdirectory %s: %v", subPath, err)
				// Continue with other directories even if one fails
				continue
			}
			files = append(files, subFiles...)
		}
	}
	log.Printf("listGitHubFilesInPath: Found %d matching files", len(files))

	return files, nil
}

// ListGitHubPages lists all published pages on GitHub Pages
func (h *Handler) ListGitHubPages(c *gin.Context) {
	log.Printf("ListGitHubPages: Starting to list GitHub Pages")

	// Check if GitHub Pages integration is enabled
	githubPagesEnabled, err := h.DB.GetSetting("github_pages_enabled")
	if err != nil || githubPagesEnabled != "true" {
		log.Printf("ListGitHubPages: GitHub Pages integration is not enabled: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "GitHub Pages integration är inte aktiverad. Aktivera den i inställningarna."})
		return
	}

	// Get GitHub Pages settings
	githubPagesRepo, err := h.DB.GetSetting("github_pages_repo")
	if err != nil || githubPagesRepo == "" {
		log.Printf("ListGitHubPages: GitHub repository is not configured: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "GitHub repository är inte konfigurerat. Konfigurera det i inställningarna."})
		return
	}
	log.Printf("ListGitHubPages: Using repository: %s", githubPagesRepo)

	githubPagesBranch, err := h.DB.GetSetting("github_pages_branch")
	if err != nil || githubPagesBranch == "" {
		githubPagesBranch = "gh-pages" // Default to gh-pages
		log.Printf("ListGitHubPages: Using default branch: %s", githubPagesBranch)
	} else {
		log.Printf("ListGitHubPages: Using branch: %s", githubPagesBranch)
	}

	githubPagesToken, err := h.DB.GetSetting("github_pages_token")
	if err != nil || githubPagesToken == "" {
		log.Printf("ListGitHubPages: GitHub token is not configured: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "GitHub token är inte konfigurerat. Konfigurera det i inställningarna."})
		return
	}
	log.Printf("ListGitHubPages: GitHub token is configured")

	// Get the list of HTML files in the repository (recursively)
	log.Printf("ListGitHubPages: Fetching HTML files from GitHub")
	htmlFiles, err := h.listGitHubFilesInPath(githubPagesRepo, githubPagesBranch, githubPagesToken, "", ".html")
	if err != nil {
		log.Printf("ListGitHubPages: Error fetching files: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Kunde inte hämta filer från GitHub: %v", err)})
		return
	}
	log.Printf("ListGitHubPages: Found %d HTML files", len(htmlFiles))

	// Filter out index.html files
	var publishedPages []map[string]string
	for _, file := range htmlFiles {
		// Skip index.html files
		if strings.HasSuffix(file, "/index.html") || file == "index.html" {
			continue
		}

		log.Printf("ListGitHubPages: Processing file: %s", file)

		// Extract the filename and path
		var path, filename string
		if strings.Contains(file, "/") {
			parts := strings.Split(file, "/")
			path = parts[0]
			filename = parts[len(parts)-1]
		} else {
			filename = file
		}

		// Extract event name and date from filename
		nameParts := strings.Split(filename, "-")
		if len(nameParts) > 3 { // Assuming format is YYYY-MM-DD-event-name.html
			eventName := strings.Join(nameParts[3:], "-")
			eventName = strings.TrimSuffix(eventName, ".html")
			eventName = strings.ReplaceAll(eventName, "-", " ")
			eventName = titleCase(eventName)

			// Extract date from filename
			datePart := strings.Join(nameParts[:3], "-")
			datePart = strings.TrimSuffix(datePart, "-")

			// Extract year from date
			year := ""
			if len(datePart) >= 4 {
				year = datePart[:4]
			}

			publishedPages = append(publishedPages, map[string]string{
				"filename":   file,
				"date":       datePart,
				"event_name": eventName,
				"year":       year,
				"url":        fmt.Sprintf("https://%s.github.io/%s/%s", strings.Split(githubPagesRepo, "/")[0], strings.Split(githubPagesRepo, "/")[1], file),
			})
			log.Printf("ListGitHubPages: Added page with date: %s, event: %s, year: %s", datePart, eventName, year)
		} else {
			// Fallback if filename doesn't match expected format
			publishedPages = append(publishedPages, map[string]string{
				"filename":   file,
				"date":       "",
				"event_name": filename,
				"year":       path, // Use the path as the year if available
				"url":        fmt.Sprintf("https://%s.github.io/%s/%s", strings.Split(githubPagesRepo, "/")[0], strings.Split(githubPagesRepo, "/")[1], file),
			})
			log.Printf("ListGitHubPages: Added page with fallback name: %s, year: %s", filename, path)
		}
	}

	// Group pages by year
	pagesByYear := make(map[string][]map[string]string)
	for _, page := range publishedPages {
		year := page["year"]
		if year == "" {
			year = "other"
		}
		pagesByYear[year] = append(pagesByYear[year], page)
	}

	// Sort pages within each year by date (newest first), then by filename when dates are the same
	for year, pages := range pagesByYear {
		sort.Slice(pages, func(i, j int) bool {
			dateI := pages[i]["date"]
			dateJ := pages[j]["date"]

			// If dates are different, sort by date (newest first)
			if dateI != dateJ {
				return dateI > dateJ
			}

			// If dates are the same, sort by filename (alphabetically)
			filenameI := pages[i]["filename"]
			filenameJ := pages[j]["filename"]
			return filenameI < filenameJ
		})
		pagesByYear[year] = pages
	}

	// Get the list of years and sort them (newest first)
	var years []string
	for year := range pagesByYear {
		years = append(years, year)
	}
	sort.Slice(years, func(i, j int) bool {
		return years[i] > years[j]
	})

	// Create the final response with years and pages
	var response []map[string]any
	for _, year := range years {
		response = append(response, map[string]any{
			"year":  year,
			"pages": pagesByYear[year],
		})
	}

	log.Printf("ListGitHubPages: Returning response with %d years", len(response))

	// Set cache control headers to prevent caching
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"years":   response,
	})
}

// DeleteFromGitHubPages deletes a published page from GitHub Pages
func (h *Handler) DeleteFromGitHubPages(c *gin.Context) {
	// Get the filename from the URL
	// When using *filename, the parameter includes the leading slash, so we need to remove it
	filename := c.Param("filename")
	if filename == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Filnamn saknas"})
		return
	}

	// Remove the leading slash if present
	filename = strings.TrimPrefix(filename, "/")

	log.Printf("DeleteFromGitHubPages: Raw filename from URL: %s", filename)

	// URL-decode the filename (it was URL-encoded in the client)
	var err error
	filename, err = url.QueryUnescape(filename)
	if err != nil {
		log.Printf("DeleteFromGitHubPages: Error decoding filename: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Ogiltigt filnamn: %v", err)})
		return
	}

	log.Printf("DeleteFromGitHubPages: Decoded filename: %s", filename)

	// Check if GitHub Pages integration is enabled
	githubPagesEnabled, err := h.DB.GetSetting("github_pages_enabled")
	if err != nil || githubPagesEnabled != "true" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "GitHub Pages integration är inte aktiverad. Aktivera den i inställningarna."})
		return
	}

	// Get GitHub Pages settings
	githubPagesRepo, err := h.DB.GetSetting("github_pages_repo")
	if err != nil || githubPagesRepo == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "GitHub repository är inte konfigurerat. Konfigurera det i inställningarna."})
		return
	}

	githubPagesBranch, err := h.DB.GetSetting("github_pages_branch")
	if err != nil || githubPagesBranch == "" {
		githubPagesBranch = "gh-pages" // Default to gh-pages
	}

	githubPagesToken, err := h.DB.GetSetting("github_pages_token")
	if err != nil || githubPagesToken == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "GitHub token är inte konfigurerat. Konfigurera det i inställningarna."})
		return
	}

	// Check if the filename includes a year directory
	var year string
	var competitionType string

	if strings.Contains(filename, "/") {
		// Extract the year and competition type from the path
		parts := strings.Split(filename, "/")
		if len(parts) >= 3 {
			// Format is year/competitionType/filename.html
			year = parts[0]
			competitionType = parts[1]
			log.Printf("DeleteFromGitHubPages: Extracted year=%s, competitionType=%s from path", year, competitionType)
		} else if len(parts) >= 2 {
			// Format is year/filename.html (older format)
			year = parts[0]
			log.Printf("DeleteFromGitHubPages: Extracted year=%s from path (old format)", year)
		}
	} else {
		// For backward compatibility, try to extract year from the filename
		parts := strings.Split(filename, "-")
		if len(parts) >= 1 {
			year = parts[0]
			// Check if it's a 4-digit year
			if len(year) == 4 && year >= "2000" && year <= "2100" {
				// This is likely a year, but the file is in the root directory
				// We'll still just delete it from where it is
				year = ""
				log.Printf("DeleteFromGitHubPages: Extracted year=%s from filename but file is in root", year)
			}
		}
	}

	// Delete the file from GitHub
	log.Printf("DeleteFromGitHubPages: Attempting to delete file %s", filename)
	err = h.deleteGitHubFile(githubPagesRepo, githubPagesBranch, githubPagesToken, filename)
	if err != nil {
		log.Printf("DeleteFromGitHubPages: Error deleting file: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Kunde inte ta bort filen från GitHub: %v", err)})
		return
	}

	// Add a longer delay to ensure GitHub's API has processed the deletion
	log.Printf("DeleteFromGitHubPages: Waiting for GitHub API to process deletion")
	time.Sleep(3 * time.Second)

	// Verify the file was deleted with retries
	log.Printf("DeleteFromGitHubPages: Verifying file was deleted")
	maxRetries := 3
	fileDeleted := false
	for i := 0; i < maxRetries; i++ {
		exists, err := h.checkFileExists(githubPagesRepo, githubPagesBranch, githubPagesToken, filename)
		if err != nil {
			log.Printf("DeleteFromGitHubPages: Error checking if file exists (attempt %d/%d): %v", i+1, maxRetries, err)
		} else if !exists {
			log.Printf("DeleteFromGitHubPages: File deletion verified on attempt %d", i+1)
			fileDeleted = true
			break
		} else {
			log.Printf("DeleteFromGitHubPages: File still exists (attempt %d/%d), waiting...", i+1, maxRetries)
			if i < maxRetries-1 {
				time.Sleep(2 * time.Second)
			}
		}
	}

	if !fileDeleted {
		log.Printf("DeleteFromGitHubPages: WARNING: Could not verify file deletion after %d attempts", maxRetries)
	}

	// If we have a year, update the year-specific index.html
	if year != "" {
		err = h.ensureYearIndexHTML(githubPagesRepo, githubPagesBranch, githubPagesToken, year, "", "")
		if err != nil {
			log.Printf("Warning: Could not update year index.html after deletion: %v", err)
			// Continue anyway, the file was deleted successfully
		}

		// If we have a competition type, update the competition type index.html
		if competitionType != "" {
			err = h.ensureCompetitionTypeIndexHTML(githubPagesRepo, githubPagesBranch, githubPagesToken, year, competitionType)
			if err != nil {
				log.Printf("Warning: Could not update competition type index.html after deletion: %v", err)
				// Continue anyway, the file was deleted successfully
			}
		}
	}

	// Update the main index.html file
	err = h.ensureIndexHTML(githubPagesRepo, githubPagesBranch, githubPagesToken, "", "")
	if err != nil {
		log.Printf("Warning: Could not update main index.html after deletion: %v", err)
		// Continue anyway, the file was deleted successfully
	}

	// Return success
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("Filen %s har tagits bort från GitHub Pages", filename),
	})
}

// generateGitHubPagesHTML generates HTML content for GitHub Pages
func (h *Handler) generateGitHubPagesHTML(event models.Event, results []models.Result, savedResults []models.SavedResult, usingSavedResults bool, totalResults []models.TotalResult, heatResults []models.HeatResult, heats []models.Heat, selectedHeat models.Heat, showOverall bool, isMultiHeat bool, templateName string) (string, error) {
	// Create a buffer to store the HTML content
	var buf bytes.Buffer

	// Create template data
	templateData := gin.H{
		"title":             "Resultat - " + event.Namn,
		"event":             event,
		"results":           results,
		"savedResults":      savedResults,
		"usingSavedResults": usingSavedResults,
		"totalResults":      totalResults,
		"heatResults":       heatResults,
		"heats":             heats,
		"selectedHeat":      selectedHeat,
		"showOverall":       showOverall,
		"isMultiHeat":       isMultiHeat,
		"generatedAt":       time.Now().Format("2006-01-02 15:04:05"),
	}

	log.Printf("generateGitHubPagesHTML: Template data prepared with %d keys", len(templateData))

	// Choose template based on templateName
	var tmplContent string
	switch templateName {
	case "minimal":
		tmplContent = minimalTemplate
	default:
		tmplContent = defaultTemplate
	}

	// Parse the template with functions
	tmpl, err := template.New("github-pages").Funcs(template.FuncMap{
		"add": func(i, j int) int { return i + j }, // Helper function for templates
		"isDiscardEnabled": func(event interface{}) bool {
			if e, ok := event.(map[string]interface{}); ok {
				if discardAfterHeats, exists := e["DiscardAfterHeats"]; exists {
					if val, ok := discardAfterHeats.(int); ok {
						return val > 0
					}
				}
			}
			return false
		},
		"getDiscardCount": func(event interface{}, totalHeats int) int {
			if e, ok := event.(map[string]interface{}); ok {
				if discardAfterHeats, exists := e["DiscardAfterHeats"]; exists {
					if val, ok := discardAfterHeats.(int); ok && val > 0 {
						if totalHeats >= val {
							return totalHeats / val
						}
					}
				}
			}
			return 0
		},
	}).Parse(tmplContent)
	if err != nil {
		return "", fmt.Errorf("could not parse template: %w", err)
	}

	// Execute the template
	log.Printf("generateGitHubPagesHTML: Executing template with data")
	err = tmpl.Execute(&buf, templateData)
	if err != nil {
		log.Printf("generateGitHubPagesHTML: Template execution error: %v", err)
		log.Printf("generateGitHubPagesHTML: Template data debug:")
		log.Printf("  - event: %+v", event)
		log.Printf("  - usingSavedResults: %v", usingSavedResults)
		log.Printf("  - showOverall: %v", showOverall)
		log.Printf("  - isMultiHeat: %v", isMultiHeat)
		log.Printf("  - selectedHeat: %+v", selectedHeat)
		log.Printf("  - totalResults count: %d", len(totalResults))
		log.Printf("  - heatResults count: %d", len(heatResults))
		log.Printf("  - savedResults count: %d", len(savedResults))
		log.Printf("  - results count: %d", len(results))
		log.Printf("  - heats count: %d", len(heats))
		return "", fmt.Errorf("could not execute template: %w", err)
	}
	log.Printf("generateGitHubPagesHTML: Template executed successfully, generated %d bytes", buf.Len())

	// Check if the generated content is suspiciously small
	if buf.Len() < 1000 {
		log.Printf("generateGitHubPagesHTML: WARNING: Generated HTML is very small (%d bytes), this might indicate missing data", buf.Len())
		log.Printf("generateGitHubPagesHTML: Generated content preview: %.100s...", buf.String())
	}

	return buf.String(), nil
}

// convertHeatResultsToResultsForPublish converts HeatResult to Result format for publishing
func (h *Handler) convertHeatResultsToResultsForPublish(heatResults []models.HeatResult) []models.Result {
	var results []models.Result

	for _, hr := range heatResults {
		// Create a copy of the event participant and set the finish time from the heat result
		eventParticipant := hr.EventParticipant
		eventParticipant.FinishTime = hr.FinishTime
		eventParticipant.DNS = hr.DNS
		eventParticipant.DNF = hr.DNF

		result := models.Result{
			EventParticipant:      eventParticipant,
			Sailor:                hr.Sailor,
			Boat:                  hr.Boat,
			StartTime:             hr.StartTime,
			ElapsedTime:           hr.ElapsedTime,
			CorrectedTime:         hr.CorrectedTime,
			ElapsedSeconds:        hr.ElapsedSeconds,
			CorrectedSeconds:      hr.CorrectedSeconds,
			CorrectedSecondsFloat: hr.CorrectedSecondsFloat,
			TimeToPrevious:        hr.TimeToPrevious,
			TimeToWinner:          hr.TimeToWinner,
			TotalPersons:          hr.TotalPersons,
			DNS:                   hr.DNS,
			DNF:                   hr.DNF,
		}
		results = append(results, result)
	}

	return results
}

// uploadToGitHub uploads content to GitHub
func (h *Handler) uploadToGitHub(repo, branch, token, filename, content, eventName string) error {
	// URL encode the filename path components to handle special characters
	pathComponents := strings.Split(filename, "/")
	for i, component := range pathComponents {
		pathComponents[i] = url.PathEscape(component)
	}
	encodedFilename := strings.Join(pathComponents, "/")

	// GitHub API URL for creating/updating file content
	apiURL := fmt.Sprintf("https://api.github.com/repos/%s/contents/%s", repo, encodedFilename)

	log.Printf("uploadToGitHub: Original filename: %s", filename)
	log.Printf("uploadToGitHub: Encoded filename: %s", encodedFilename)
	log.Printf("uploadToGitHub: API URL: %s", apiURL)

	// Check if the file already exists to get its SHA
	existingSHA, err := h.getGitHubFileSHA(repo, branch, token, filename)
	if err != nil {
		log.Printf("Warning: Could not check if file exists: %v", err)
		// Continue anyway, we'll try to create the file
	}

	// Prepare the request body
	requestBody := GitHubContent{
		Message: fmt.Sprintf("Update results for %s", eventName),
		Content: base64.StdEncoding.EncodeToString([]byte(content)),
		Branch:  branch,
	}

	// If the file exists, include its SHA to update it
	if existingSHA != "" {
		requestBody.SHA = existingSHA
	}

	// Convert request body to JSON
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("could not marshal request body: %w", err)
	}

	// Create the HTTP request
	req, err := http.NewRequest("PUT", apiURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		return fmt.Errorf("could not create request: %w", err)
	}

	// Set headers
	req.Header.Set("Authorization", "token "+token)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/vnd.github.v3+json")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("could not send request: %w", err)
	}
	defer resp.Body.Close()

	// Check the response
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		// Read the response body for more details
		bodyBytes, _ := io.ReadAll(resp.Body)
		log.Printf("uploadToGitHub: GitHub API error response: %s", string(bodyBytes))
		return fmt.Errorf("GitHub API returned status code %d: %s", resp.StatusCode, string(bodyBytes))
	}

	return nil
}

// checkFileExists checks if a file exists on GitHub
func (h *Handler) checkFileExists(repo, branch, token, filename string) (bool, error) {
	log.Printf("checkFileExists: Checking if file %s exists in %s/%s", filename, repo, branch)

	// Handle special characters in the path
	// URL encode the path components individually
	pathComponents := strings.Split(filename, "/")
	for i, component := range pathComponents {
		pathComponents[i] = url.PathEscape(component)
	}
	encodedPath := strings.Join(pathComponents, "/")

	// Add a timestamp to prevent caching
	timestamp := time.Now().UnixNano()

	// GitHub API URL for getting file content with cache-busting parameter
	apiURL := fmt.Sprintf("https://api.github.com/repos/%s/contents/%s?ref=%s&t=%d", repo, encodedPath, branch, timestamp)
	log.Printf("checkFileExists: API URL: %s", apiURL)

	// Create the HTTP request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		log.Printf("checkFileExists: Error creating request: %v", err)
		return false, fmt.Errorf("could not create request: %w", err)
	}

	// Set headers
	req.Header.Set("Authorization", "token "+token)
	req.Header.Set("Accept", "application/vnd.github.v3+json")

	// Send the request
	client := &http.Client{
		Timeout: 10 * time.Second, // Add a timeout to prevent hanging
	}
	log.Printf("checkFileExists: Sending request to GitHub API")
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("checkFileExists: Error sending request: %v", err)
		return false, fmt.Errorf("could not send request: %w", err)
	}
	defer resp.Body.Close()
	log.Printf("checkFileExists: Response status: %d", resp.StatusCode)

	// If the file doesn't exist, return false
	if resp.StatusCode == http.StatusNotFound {
		log.Printf("checkFileExists: File %s does not exist", filename)
		return false, nil
	}

	// Check for other errors
	if resp.StatusCode != http.StatusOK {
		log.Printf("checkFileExists: GitHub API error: %d", resp.StatusCode)
		return false, fmt.Errorf("GitHub API returned status code %d", resp.StatusCode)
	}

	log.Printf("checkFileExists: File %s exists", filename)
	return true, nil
}

// getGitHubFileSHA gets the SHA of a file on GitHub
func (h *Handler) getGitHubFileSHA(repo, branch, token, filename string) (string, error) {
	// URL encode the filename path components to handle special characters
	pathComponents := strings.Split(filename, "/")
	for i, component := range pathComponents {
		pathComponents[i] = url.PathEscape(component)
	}
	encodedFilename := strings.Join(pathComponents, "/")

	// Add a timestamp to prevent caching
	timestamp := time.Now().UnixNano()

	// GitHub API URL for getting file content with cache-busting parameter
	apiURL := fmt.Sprintf("https://api.github.com/repos/%s/contents/%s?ref=%s&t=%d", repo, encodedFilename, branch, timestamp)

	log.Printf("getGitHubFileSHA: Original filename: %s", filename)
	log.Printf("getGitHubFileSHA: Encoded filename: %s", encodedFilename)
	log.Printf("getGitHubFileSHA: API URL: %s", apiURL)

	// Create the HTTP request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return "", fmt.Errorf("could not create request: %w", err)
	}

	// Set headers
	req.Header.Set("Authorization", "token "+token)
	req.Header.Set("Accept", "application/vnd.github.v3+json")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("could not send request: %w", err)
	}
	defer resp.Body.Close()

	// If the file doesn't exist, return empty SHA
	if resp.StatusCode == http.StatusNotFound {
		return "", nil
	}

	// Check for other errors
	if resp.StatusCode != http.StatusOK {
		// Read the response body for more details
		bodyBytes, _ := io.ReadAll(resp.Body)
		log.Printf("getGitHubFileSHA: GitHub API error response: %s", string(bodyBytes))
		return "", fmt.Errorf("GitHub API returned status code %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Read the response body first
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("could not read response body: %w", err)
	}

	log.Printf("getGitHubFileSHA: Response received (%d bytes)", len(bodyBytes))

	// Parse the response
	var contentResponse GitHubContentResponse
	err = json.Unmarshal(bodyBytes, &contentResponse)
	if err != nil {
		log.Printf("getGitHubFileSHA: JSON parsing error. Response body: %s", string(bodyBytes))
		return "", fmt.Errorf("could not decode response: %w", err)
	}

	return contentResponse.SHA, nil
}

// deleteGitHubFile deletes a file from GitHub
func (h *Handler) deleteGitHubFile(repo, branch, token, filename string) error {
	log.Printf("deleteGitHubFile: Deleting file %s from %s/%s", filename, repo, branch)

	// Get the SHA of the file using the original filename (getGitHubFileSHA will handle encoding)
	sha, err := h.getGitHubFileSHA(repo, branch, token, filename)
	if err != nil {
		log.Printf("deleteGitHubFile: Error getting SHA: %v", err)
		return fmt.Errorf("could not get file SHA: %w", err)
	}

	// If the file doesn't exist, return success
	if sha == "" {
		log.Printf("deleteGitHubFile: File %s does not exist, nothing to delete", filename)
		return nil
	}

	// Handle special characters in the path for the API URL
	// URL encode the path components individually
	pathComponents := strings.Split(filename, "/")
	for i, component := range pathComponents {
		pathComponents[i] = url.PathEscape(component)
	}
	encodedPath := strings.Join(pathComponents, "/")

	// GitHub API URL for deleting file content
	apiURL := fmt.Sprintf("https://api.github.com/repos/%s/contents/%s", repo, encodedPath)
	log.Printf("deleteGitHubFile: API URL: %s", apiURL)

	// Prepare the request body
	requestBody := GitHubContent{
		Message: fmt.Sprintf("Delete %s", filename),
		SHA:     sha,
		Branch:  branch,
	}

	// Convert request body to JSON
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		log.Printf("deleteGitHubFile: Error marshaling request body: %v", err)
		return fmt.Errorf("could not marshal request body: %w", err)
	}

	// Create the HTTP request
	req, err := http.NewRequest("DELETE", apiURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		log.Printf("deleteGitHubFile: Error creating request: %v", err)
		return fmt.Errorf("could not create request: %w", err)
	}

	// Set headers
	req.Header.Set("Authorization", "token "+token)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/vnd.github.v3+json")
	log.Printf("deleteGitHubFile: Request headers set")

	// Send the request
	client := &http.Client{
		Timeout: 30 * time.Second, // Add a timeout to prevent hanging
	}
	log.Printf("deleteGitHubFile: Sending DELETE request to GitHub API")
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("deleteGitHubFile: Error sending request: %v", err)
		return fmt.Errorf("could not send request: %w", err)
	}
	defer resp.Body.Close()
	log.Printf("deleteGitHubFile: Response status: %d", resp.StatusCode)

	// Check the response
	if resp.StatusCode != http.StatusOK {
		// Read the response body for more details
		bodyBytes, _ := io.ReadAll(resp.Body)
		log.Printf("deleteGitHubFile: GitHub API error response: %s", string(bodyBytes))
		return fmt.Errorf("GitHub API returned status code %d: %s", resp.StatusCode, string(bodyBytes))
	}

	log.Printf("deleteGitHubFile: Successfully deleted file %s", filename)
	return nil
}

// ensureGitHubDirectory ensures that a directory exists in the GitHub repository
func (h *Handler) ensureGitHubDirectory(repo, branch, token, dirPath string) error {
	log.Printf("ensureGitHubDirectory: Ensuring directory %s exists in %s/%s", dirPath, repo, branch)

	// Check if the directory already exists
	exists, err := h.checkFileExists(repo, branch, token, dirPath)
	if err != nil {
		log.Printf("ensureGitHubDirectory: Error checking if directory exists: %v", err)
		// Continue anyway, we'll try to create it
	}

	// If the directory already exists, we're done
	if exists {
		log.Printf("ensureGitHubDirectory: Directory %s already exists", dirPath)
		return nil
	}

	// Create a .gitkeep file in the directory to ensure it exists
	gitkeepPath := fmt.Sprintf("%s/.gitkeep", dirPath)
	err = h.uploadToGitHub(repo, branch, token, gitkeepPath, "", fmt.Sprintf("Create directory %s", dirPath))
	if err != nil {
		return fmt.Errorf("could not create directory: %w", err)
	}

	log.Printf("ensureGitHubDirectory: Successfully created directory %s", dirPath)
	return nil
}

// ensureYearIndexHTML creates or updates the index.html file for a specific year
func (h *Handler) ensureYearIndexHTML(repo, branch, token, year, latestFilename, eventName string) error {
	log.Printf("ensureYearIndexHTML: Creating/updating index.html for year %s in %s/%s", year, repo, branch)

	// Get the list of directories in the year directory (competition types)
	yearPath := year

	// Get the list of directories directly from the GitHub API with cache busting
	timestamp := time.Now().UnixNano()
	apiURL := fmt.Sprintf("https://api.github.com/repos/%s/contents/%s?ref=%s&t=%d", repo, yearPath, branch, timestamp)
	log.Printf("ensureYearIndexHTML: API URL for listing competition types: %s", apiURL)

	// Create the HTTP request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		log.Printf("ensureYearIndexHTML: Error creating request: %v", err)
		return fmt.Errorf("could not create request: %w", err)
	}

	// Add authorization header if token is provided
	if token != "" {
		req.Header.Add("Authorization", "token "+token)
	}

	// Add headers for GitHub API
	req.Header.Add("Accept", "application/vnd.github.v3+json")
	req.Header.Add("User-Agent", "Segling-App")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("ensureYearIndexHTML: Error sending request: %v", err)
		return fmt.Errorf("could not send request: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		// If the directory doesn't exist, create it
		if resp.StatusCode == http.StatusNotFound {
			log.Printf("ensureYearIndexHTML: Year directory %s doesn't exist, creating it", yearPath)
			err = h.ensureGitHubDirectory(repo, branch, token, yearPath)
			if err != nil {
				return fmt.Errorf("could not create year directory: %w", err)
			}

			// Generate empty year index.html content
			yearIndexContent := h.generateYearIndexHTML(year, []string{}, latestFilename, eventName)

			// Upload year index.html
			yearIndexPath := fmt.Sprintf("%s/index.html", year)
			return h.uploadToGitHub(repo, branch, token, yearIndexPath, yearIndexContent, fmt.Sprintf("Create index page for year %s", year))
		}

		log.Printf("ensureYearIndexHTML: GitHub API returned status %d", resp.StatusCode)
		return fmt.Errorf("GitHub API returned status %d", resp.StatusCode)
	}

	// Read the response body first
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("ensureYearIndexHTML: Error reading response body: %v", err)
		return fmt.Errorf("could not read response body: %w", err)
	}

	log.Printf("ensureYearIndexHTML: Response received (%d bytes)", len(bodyBytes))

	// Parse the response
	var contents []GitHubContent
	err = json.Unmarshal(bodyBytes, &contents)
	if err != nil {
		log.Printf("ensureYearIndexHTML: JSON parsing error. Response body: %s", string(bodyBytes))
		return fmt.Errorf("could not parse response: %w", err)
	}

	// Extract competition types (directories)
	var competitionTypes []string
	for _, content := range contents {
		log.Printf("ensureYearIndexHTML: Processing item: %s (type: %s)", content.Name, content.Type)
		// Check if it's a directory
		if content.Type == "dir" {
			competitionTypes = append(competitionTypes, content.Name)
			log.Printf("ensureYearIndexHTML: Found competition type directory: %s", content.Name)

			// Ensure competition type index.html exists
			err = h.ensureCompetitionTypeIndexHTML(repo, branch, token, year, content.Name)
			if err != nil {
				log.Printf("ensureYearIndexHTML: Warning: Could not ensure competition type index.html: %v", err)
				// Continue anyway
			}
		}
	}

	// Generate year index.html content
	yearIndexContent := h.generateYearIndexHTML(year, competitionTypes, latestFilename, eventName)

	// Upload year index.html
	yearIndexPath := fmt.Sprintf("%s/index.html", year)
	return h.uploadToGitHub(repo, branch, token, yearIndexPath, yearIndexContent, fmt.Sprintf("Update index page for year %s", year))
}

// ensureIndexHTML creates or updates the main index.html file on GitHub Pages
// Note: latestFilename and eventName parameters are not used but kept for backward compatibility
func (h *Handler) ensureIndexHTML(repo, branch, token string, _, _ string) error {
	log.Printf("ensureIndexHTML: Creating/updating main index.html for %s/%s", repo, branch)

	// Get the list of directories directly from the GitHub API with cache busting
	timestamp := time.Now().UnixNano()
	apiURL := fmt.Sprintf("https://api.github.com/repos/%s/contents?ref=%s&t=%d", repo, branch, timestamp)
	log.Printf("ensureIndexHTML: API URL for listing directories: %s", apiURL)

	// Create the HTTP request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		log.Printf("ensureIndexHTML: Error creating request: %v", err)
		return fmt.Errorf("could not create request: %w", err)
	}

	// Set headers
	req.Header.Set("Authorization", "token "+token)
	req.Header.Set("Accept", "application/vnd.github.v3+json")

	// Send the request
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("ensureIndexHTML: Error sending request: %v", err)
		return fmt.Errorf("could not send request: %w", err)
	}
	defer resp.Body.Close()

	// Check for errors
	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		log.Printf("ensureIndexHTML: GitHub API error response: %s", string(bodyBytes))
		return fmt.Errorf("GitHub API returned status code %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse the response
	var contents []GitHubContentResponse
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("ensureIndexHTML: Error reading response body: %v", err)
		return fmt.Errorf("could not read response body: %w", err)
	}

	log.Printf("ensureIndexHTML: Response received (%d bytes)", len(bodyBytes))

	err = json.Unmarshal(bodyBytes, &contents)
	if err != nil {
		log.Printf("ensureIndexHTML: Error decoding response: %v", err)
		return fmt.Errorf("could not decode response: %w", err)
	}

	// Extract years (directories that are 4-digit numbers)
	var years []string
	yearPattern := regexp.MustCompile(`^[0-9]{4}$`)
	for _, content := range contents {
		log.Printf("ensureIndexHTML: Processing item: %s (type: %s)", content.Name, content.Type)
		// Check if it's a directory and matches the year pattern
		if content.Type == "dir" && yearPattern.MatchString(content.Name) {
			years = append(years, content.Name)
			log.Printf("ensureIndexHTML: Found year directory: %s", content.Name)
		}
	}

	// Sort years in descending order (newest first)
	sort.Slice(years, func(i, j int) bool {
		return years[i] > years[j]
	})

	log.Printf("ensureIndexHTML: Found %d year directories: %v", len(years), years)

	// Generate main index.html content
	indexContent := h.generateMainIndexHTML(years)

	// Upload main index.html
	return h.uploadToGitHub(repo, branch, token, "index.html", indexContent, "Update main index page")
}

// generateMainIndexHTML generates the content for the main index.html file
func (h *Handler) generateMainIndexHTML(years []string) string {
	// Create a buffer to store the HTML content
	var buf bytes.Buffer

	// Generate a timestamp for cache busting
	timestamp := time.Now().Unix()

	// Write the HTML header
	buf.WriteString(`<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Seglingsresultat</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .card-header {
            background-color: #0d6efd;
            color: white;
            border-top-left-radius: 10px !important;
            border-top-right-radius: 10px !important;
            padding: 15px 20px;
        }
        .list-group-item {
            border-left: none;
            border-right: none;
            padding: 15px 20px;
            transition: all 0.2s;
        }
        .list-group-item:hover {
            background-color: #f0f7ff;
            transform: translateY(-2px);
        }
        .list-group-item:first-child {
            border-top: none;
        }
        .year-item {
            font-weight: 600;
            font-size: 1.2rem;
            color: #212529;
        }
        .year-link {
            text-decoration: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .year-link:hover .year-item {
            color: #0d6efd;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="card">
            <div class="card-header">
                <h1 class="mb-0">Seglingsresultat</h1>
            </div>
`)

	// Add the list of years
	buf.WriteString(`
            <ul class="list-group list-group-flush">
`)

	// Add each year as a link
	for _, year := range years {
		buf.WriteString(fmt.Sprintf(`
                <li class="list-group-item">
                    <a href="%s/index.html?t=%d" class="year-link">
                        <span class="year-item">%s</span>
                        <i class="bi bi-chevron-right text-primary"></i>
                    </a>
                </li>
`, year, timestamp, year))
	}

	// Close the HTML
	buf.WriteString(`
            </ul>
        </div>
        <footer class="footer">
            <p>Genererad av <a href="https://github.com/rogge66/sailapp" class="text-decoration-none">Segling</a></p>
        </footer>
    </div>
</body>
</html>
`)

	return buf.String()
}

// ensureCompetitionTypeIndexHTML creates or updates the index.html file for a specific competition type
func (h *Handler) ensureCompetitionTypeIndexHTML(repo, branch, token, year, competitionType string) error {
	log.Printf("ensureCompetitionTypeIndexHTML: Creating/updating index.html for competition type %s in year %s", competitionType, year)

	// Get the list of HTML files in the competition type directory
	competitionTypePath := fmt.Sprintf("%s/%s", year, competitionType)
	htmlFiles, err := h.listGitHubFilesInPath(repo, branch, token, competitionTypePath, ".html")
	if err != nil {
		return fmt.Errorf("could not list HTML files for competition type %s in year %s: %w", competitionType, year, err)
	}

	// Filter out any index.html files
	var eventFiles []string
	for _, file := range htmlFiles {
		// Extract just the filename without the path
		_, filename := filepath.Split(file)
		if filename != "index.html" {
			eventFiles = append(eventFiles, file)
		}
	}

	// Generate competition type index.html content
	competitionTypeIndexContent := h.generateCompetitionTypeIndexHTML(year, competitionType, eventFiles)

	// Upload competition type index.html
	competitionTypeIndexPath := fmt.Sprintf("%s/%s/index.html", year, competitionType)
	return h.uploadToGitHub(repo, branch, token, competitionTypeIndexPath, competitionTypeIndexContent, fmt.Sprintf("Update index page for %s in year %s", competitionType, year))
}

// generateCompetitionTypeIndexHTML generates the content for a competition type-specific index.html file
func (h *Handler) generateCompetitionTypeIndexHTML(year, competitionType string, htmlFiles []string) string {
	// Create a buffer to store the HTML content
	var buf bytes.Buffer

	// Generate a timestamp for cache busting
	timestamp := time.Now().Unix()

	// Write the HTML header
	buf.WriteString(`<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Seglingsresultat - `)
	buf.WriteString(year)
	buf.WriteString(" - ")
	buf.WriteString(competitionType)
	buf.WriteString(`</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 0.8em;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Seglingsresultat - `)
	buf.WriteString(year)
	buf.WriteString(" - ")
	buf.WriteString(competitionType)
	buf.WriteString(`</h1>
        <p><a href="../index.html">Tillbaka till alla tävlingstyper</a></p>
        <div class="list-group">`)

	// Sort files by date (descending) and then by filename (ascending) when dates are the same
	sort.Slice(htmlFiles, func(i, j int) bool {
		// Extract just the filename without the path
		_, filenameI := filepath.Split(htmlFiles[i])
		_, filenameJ := filepath.Split(htmlFiles[j])

		// Extract date from filename (format: YYYY-MM-DD-event-name.html)
		partsI := strings.SplitN(filenameI, "-", 4)
		partsJ := strings.SplitN(filenameJ, "-", 4)

		if len(partsI) >= 3 && len(partsJ) >= 3 {
			dateI := partsI[0] + "-" + partsI[1] + "-" + partsI[2]
			dateJ := partsJ[0] + "-" + partsJ[1] + "-" + partsJ[2]

			// If dates are different, sort by date (newest first)
			if dateI != dateJ {
				return dateI > dateJ
			}

			// If dates are the same, sort by filename (alphabetically)
			return filenameI < filenameJ
		}

		// Fallback to filename comparison if date extraction fails
		return filenameI > filenameJ
	})

	// Add links to each HTML file
	for _, file := range htmlFiles {
		// Extract just the filename without the path
		_, filename := filepath.Split(file)
		if filename == "index.html" {
			continue
		}

		// Extract the date and event name from the filename
		// Format: YYYY-MM-DD-event-name.html
		parts := strings.SplitN(filename, "-", 4)
		if len(parts) >= 4 {
			date := parts[0] + "-" + parts[1] + "-" + parts[2]
			eventName := strings.TrimSuffix(parts[3], ".html")
			eventName = strings.ReplaceAll(eventName, "-", " ")
			eventName = titleCase(eventName)

			buf.WriteString(`
            <a href="`)
			buf.WriteString(filename)
			buf.WriteString(`?t=`)
			buf.WriteString(strconv.FormatInt(timestamp, 10))
			buf.WriteString(`" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                    <h5 class="mb-1">`)
			buf.WriteString(eventName)
			buf.WriteString(`</h5>
                    <small>`)
			buf.WriteString(date)
			buf.WriteString(`</small>
                </div>
            </a>`)
		}
	}

	// Close the HTML
	buf.WriteString(`
        </div>
        <footer class="footer">
            <p>Genererad av <a href="https://github.com/rogge66/sailapp" class="text-decoration-none">Segling</a></p>
        </footer>
    </div>
</body>
</html>
`)

	return buf.String()
}

// generateYearIndexHTML generates the content for a year-specific index.html file
// Note: The last two parameters are not used but kept for backward compatibility
func (h *Handler) generateYearIndexHTML(year string, competitionTypes []string, _, _ string) string {
	// Create a buffer to store the HTML content
	var buf bytes.Buffer

	// Generate a timestamp for cache busting
	timestamp := time.Now().Unix()

	// Write the HTML header
	buf.WriteString(`<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Seglingsresultat ` + year + `</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .card-header {
            background-color: #0d6efd;
            color: white;
            border-top-left-radius: 10px !important;
            border-top-right-radius: 10px !important;
            padding: 15px 20px;
        }
        .list-group-item {
            border-left: none;
            border-right: none;
            padding: 15px 20px;
            transition: all 0.2s;
        }
        .list-group-item:hover {
            background-color: #f0f7ff;
            transform: translateY(-2px);
        }
        .list-group-item:first-child {
            border-top: none;
        }
        .event-date {
            color: #6c757d;
            font-weight: 500;
            min-width: 110px;
        }
        .event-name {
            font-weight: 500;
            color: #212529;
        }
        .event-link {
            text-decoration: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .event-link:hover .event-name {
            color: #0d6efd;
        }
        .back-link {
            margin-bottom: 20px;
            display: inline-block;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
        @media (max-width: 576px) {
            .event-link {
                flex-direction: column;
                align-items: flex-start;
            }
            .event-date {
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <a href="../index.html" class="back-link text-decoration-none">
            <i class="bi bi-arrow-left"></i> Tillbaka till alla år
        </a>

        <div class="card">
            <div class="card-header">
                <h1 class="mb-0">Seglingsresultat ` + year + `</h1>
            </div>
`)

	// Add the list of competition types
	buf.WriteString(`
            <ul class="list-group list-group-flush">
`)

	// Sort competition types alphabetically
	sort.Strings(competitionTypes)

	// Add each competition type as a link
	for _, competitionType := range competitionTypes {
		buf.WriteString(fmt.Sprintf(`
                <li class="list-group-item">
                    <a href="%s/index.html?t=%d" class="event-link">
                        <div class="d-flex align-items-center">
                            <span class="event-name">%s</span>
                        </div>
                        <i class="bi bi-chevron-right text-primary"></i>
                    </a>
                </li>
`, competitionType, timestamp, competitionType))
	}

	// Close the HTML
	buf.WriteString(`
            </ul>
        </div>
        <footer class="footer">
            <p>Genererad av <a href="https://github.com/rogge66/sailapp" class="text-decoration-none">Segling</a></p>
        </footer>
    </div>
</body>
</html>
`)

	return buf.String()
}

// Note: This function has been removed as it's not used anymore.
// The functionality has been replaced by the hierarchical directory structure.
