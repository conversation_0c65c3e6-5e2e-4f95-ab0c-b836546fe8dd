package handlers

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rbjoregren/segling/pkg/database"
	"github.com/rbjoregren/segling/pkg/models"
)

// TestEntypseglingDisablesJaktstart tests that enabling entypsegling automatically disables jaktstart
// since all boats have the same SRS in entypsegling competitions
func TestEntypseglingDisablesJaktstart(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create test database
	testDB := "test_entypsegling_jaktstart.db"
	os.Remove(testDB) // Remove any existing test database

	db, err := database.New(testDB)
	if err != nil {
		t.Fatalf("Failed to create test database: %v", err)
	}
	defer func() {
		db.Close()
		os.Remove(testDB)
	}()

	// Initialize database
	err = db.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize database: %v", err)
	}

	// Create handler
	handler := NewHandler(db)

	t.Run("Create Event with Entypsegling and Jaktstart Both Enabled", func(t *testing.T) {
		// Create form data with both entypsegling and jaktstart enabled
		form := url.Values{}
		form.Add("namn", "Test Entypsegling Event")
		form.Add("datum", "2025-01-10")
		form.Add("starttid", "12:00")
		form.Add("vind", "5")
		form.Add("banlangd", "10")
		form.Add("jaktstart_type", "entypsegling") // Enable entypsegling (this should override any jaktstart)
		form.Add("boat_type", "Laser")             // Required for entypsegling
		form.Add("tavlingstyp", "Test")
		form.Add("beskrivning", "Test event with both options")

		// Create request
		req, _ := http.NewRequest("POST", "/events", strings.NewReader(form.Encode()))
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

		// Create response recorder
		w := httptest.NewRecorder()

		// Create router and add route
		router := gin.New()
		router.POST("/events", handler.PostEvent)

		// Perform request
		router.ServeHTTP(w, req)

		// Check that the request was successful (should redirect)
		if w.Code != http.StatusSeeOther {
			t.Errorf("Expected status %d, got %d. Response: %s", http.StatusSeeOther, w.Code, w.Body.String())
			return
		}

		// Get the created event from the database
		events, err := db.GetEvents()
		if err != nil {
			t.Fatalf("Failed to get events: %v", err)
		}

		if len(events) != 1 {
			t.Fatalf("Expected 1 event, got %d", len(events))
		}

		event := events[0]

		// Verify that entypsegling is enabled
		if !event.Entypsegling {
			t.Error("Expected entypsegling to be enabled")
		}

		// Verify that jaktstart is disabled (this is the key test)
		if event.IsJaktstart() {
			t.Error("Expected jaktstart to be disabled when entypsegling is enabled")
		}

		// Verify other properties
		if event.BoatType != "Laser" {
			t.Errorf("Expected boat type 'Laser', got '%s'", event.BoatType)
		}

		if event.Namn != "Test Entypsegling Event" {
			t.Errorf("Expected name 'Test Entypsegling Event', got '%s'", event.Namn)
		}
	})

	t.Run("Update Event to Enable Entypsegling Should Disable Jaktstart", func(t *testing.T) {
		// First create a regular event with jaktstart enabled
		eventDate, _ := time.Parse("2006-01-02", "2025-01-11")
		eventID, err := db.CreateEventWithJaktstartType("Test Regular Event", eventDate, "13:00", 5, 10, "regular", "Test", "Regular event with jaktstart", "")
		if err != nil {
			t.Fatalf("Failed to create test event: %v", err)
		}

		// Verify the event was created with jaktstart enabled
		event, err := db.GetEvent(eventID)
		if err != nil {
			t.Fatalf("Failed to get event: %v", err)
		}

		if !event.IsJaktstart() {
			t.Error("Expected jaktstart to be enabled initially")
		}

		if event.Entypsegling {
			t.Error("Expected entypsegling to be disabled initially")
		}

		// Now update the event to enable entypsegling (keeping jaktstart "on" in form)
		form := url.Values{}
		form.Add("id", fmt.Sprintf("%d", eventID))
		form.Add("namn", "Test Updated Event")
		form.Add("datum", "2025-01-11")
		form.Add("starttid", "13:00")
		form.Add("vind", "5")
		form.Add("banlangd", "10")
		form.Add("jaktstart_type", "entypsegling") // Enable entypsegling (this should override any jaktstart)
		form.Add("boat_type", "Optimist")          // Required for entypsegling
		form.Add("tavlingstyp", "Test")
		form.Add("beskrivning", "Updated event")

		// Create request
		req, _ := http.NewRequest("POST", "/events", strings.NewReader(form.Encode()))
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

		// Create response recorder
		w := httptest.NewRecorder()

		// Create router and add route
		router := gin.New()
		router.POST("/events", handler.PostEvent)

		// Perform request
		router.ServeHTTP(w, req)

		// Check that the request was successful (should redirect)
		if w.Code != http.StatusSeeOther {
			t.Errorf("Expected status %d, got %d. Response: %s", http.StatusSeeOther, w.Code, w.Body.String())
			return
		}

		// Get the updated event from the database
		updatedEvent, err := db.GetEvent(eventID)
		if err != nil {
			t.Fatalf("Failed to get updated event: %v", err)
		}

		// Verify that entypsegling is now enabled
		if !updatedEvent.Entypsegling {
			t.Error("Expected entypsegling to be enabled after update")
		}

		// Verify that jaktstart is now disabled (this is the key test)
		if updatedEvent.IsJaktstart() {
			t.Error("Expected jaktstart to be disabled when entypsegling is enabled during update")
		}

		// Verify other properties were updated
		if updatedEvent.BoatType != "Optimist" {
			t.Errorf("Expected boat type 'Optimist', got '%s'", updatedEvent.BoatType)
		}

		if updatedEvent.Namn != "Test Updated Event" {
			t.Errorf("Expected name 'Test Updated Event', got '%s'", updatedEvent.Namn)
		}
	})

	t.Run("Regular Event Can Have Jaktstart Enabled", func(t *testing.T) {
		// Create form data for regular event with jaktstart enabled
		form := url.Values{}
		form.Add("namn", "Test Regular Jaktstart Event")
		form.Add("datum", "2025-01-12")
		form.Add("starttid", "14:00")
		form.Add("vind", "5")
		form.Add("banlangd", "10")
		form.Add("jaktstart_type", "regular") // Enable regular jaktstart
		form.Add("tavlingstyp", "Test")
		form.Add("beskrivning", "Regular event with jaktstart")

		// Create request
		req, _ := http.NewRequest("POST", "/events", strings.NewReader(form.Encode()))
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

		// Create response recorder
		w := httptest.NewRecorder()

		// Create router and add route
		router := gin.New()
		router.POST("/events", handler.PostEvent)

		// Perform request
		router.ServeHTTP(w, req)

		// Check that the request was successful (should redirect)
		if w.Code != http.StatusSeeOther {
			t.Errorf("Expected status %d, got %d. Response: %s", http.StatusSeeOther, w.Code, w.Body.String())
			return
		}

		// Get all events from the database
		events, err := db.GetEvents()
		if err != nil {
			t.Fatalf("Failed to get events: %v", err)
		}

		// Find the regular jaktstart event (should be the last one created)
		var regularEvent *models.Event
		for _, event := range events {
			if event.Namn == "Test Regular Jaktstart Event" {
				regularEvent = &event
				break
			}
		}

		if regularEvent == nil {
			t.Fatal("Could not find the regular jaktstart event")
		}

		// Verify that entypsegling is disabled
		if regularEvent.Entypsegling {
			t.Error("Expected entypsegling to be disabled for regular event")
		}

		// Verify that jaktstart is enabled (should work for regular events)
		if !regularEvent.IsJaktstart() {
			t.Error("Expected jaktstart to be enabled for regular event")
		}

		// Verify boat type is empty for regular events
		if regularEvent.BoatType != "" {
			t.Errorf("Expected empty boat type for regular event, got '%s'", regularEvent.BoatType)
		}
	})
}
