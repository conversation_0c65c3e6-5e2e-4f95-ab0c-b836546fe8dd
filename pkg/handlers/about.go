package handlers

import (
	"bufio"
	"bytes"
	"html/template"
	"net/http"
	"os"
	"os/exec"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rbjoregren/segling/pkg/models"
	"github.com/russross/blackfriday/v2"
)

// ReleaseNote represents a single release note entry
type ReleaseNote struct {
	Version string
	Date    string
	Content template.HTML // Using template.HTML to prevent escaping of HTML content
}

// GetAbout handles the about page
func (h *Handler) GetAbout(c *gin.Context) {
	// Get all events for the dropdown
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		// If there's an error, just continue with an empty list
		allEvents = []models.Event{}
	}

	// Parse the release notes
	releaseNotes, err := h.parseReleaseNotes()
	if err != nil {
		// If there's an error, just continue with empty release notes
		releaseNotes = []ReleaseNote{}
	}

	// Get build date and commit hash
	buildDate := time.Now().Format("2006-01-02")

	// Get the commit hash from git
	commitHash := ""
	cmd := exec.Command("git", "rev-parse", "--short", "HEAD")
	output, err := cmd.Output()
	if err == nil {
		commitHash = strings.TrimSpace(string(output))
	}

	c.HTML(http.StatusOK, "about.html", gin.H{
		"title":                  "Om",
		"allEvents":              allEvents,
		"activeMenu":             "about",
		"selectedEventJaktstart": false,
		"version":                h.Version,
		"buildDate":              buildDate,
		"commitHash":             commitHash,
		"releaseNotes":           releaseNotes,
	})
}

// parseReleaseNotes parses the RELEASE_NOTES.md file and returns a slice of ReleaseNote structs
func (h *Handler) parseReleaseNotes() ([]ReleaseNote, error) {
	// Try to open the release notes file from different possible locations
	var file *os.File
	var err error

	// First try the root directory (original location)
	file, err = os.Open("RELEASE_NOTES.md")
	if err != nil {
		// If not found in root, try the docs directory (new location)
		file, err = os.Open("docs/RELEASE_NOTES.md")
		if err != nil {
			return nil, err
		}
	}
	defer file.Close()

	// Create a scanner to read the file line by line
	scanner := bufio.NewScanner(file)

	// Create a slice to store the release notes
	var releaseNotes []ReleaseNote

	// Create a regular expression to match version headers
	versionRegex := regexp.MustCompile(`^## Version (\d+\.\d+\.\d+) \(([^)]+)\)`)

	// Variables to track the current release note
	var currentVersion, currentDate string
	var currentContentBuffer bytes.Buffer
	inReleaseNote := false

	// Read the file line by line
	for scanner.Scan() {
		line := scanner.Text()

		// Check if this is a version header
		matches := versionRegex.FindStringSubmatch(line)
		if len(matches) > 0 {
			// If we were already in a release note, save it
			if inReleaseNote {
				// Convert Markdown to HTML
				htmlContent := blackfriday.Run(currentContentBuffer.Bytes())

				releaseNotes = append(releaseNotes, ReleaseNote{
					Version: currentVersion,
					Date:    currentDate,
					Content: template.HTML(htmlContent),
				})
			}

			// Start a new release note
			currentVersion = matches[1]
			currentDate = matches[2]
			currentContentBuffer.Reset()
			inReleaseNote = true
		} else if inReleaseNote {
			// Add this line to the current release note content
			if currentContentBuffer.Len() > 0 {
				currentContentBuffer.WriteString("\n")
			}
			currentContentBuffer.WriteString(line)
		}
	}

	// Add the last release note if there is one
	if inReleaseNote {
		// Convert Markdown to HTML
		htmlContent := blackfriday.Run(currentContentBuffer.Bytes())

		releaseNotes = append(releaseNotes, ReleaseNote{
			Version: currentVersion,
			Date:    currentDate,
			Content: template.HTML(htmlContent),
		})
	}

	// Check for scanner errors
	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return releaseNotes, nil
}
