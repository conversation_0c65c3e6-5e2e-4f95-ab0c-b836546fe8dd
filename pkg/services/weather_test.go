package services

import (
	"testing"
)

func TestNewWeatherService(t *testing.T) {
	// Create a mock database (nil is acceptable for this test)
	ws := NewWeatherService(nil)

	if ws == nil {
		t.Fatal("NewWeatherService() returned nil")
	}

	if ws.apiURL == "" {
		t.<PERSON>r("apiURL should not be empty")
	}

	if ws.authKey == "" {
		t.<PERSON><PERSON>("authKey should not be empty")
	}
}

func TestGetWindDirectionText(t *testing.T) {
	testCases := []struct {
		direction int
		expected  string
		comment   string
	}{
		// Test all 16 compass directions (each covers 22.5 degrees)
		// N: 348.75° - 11.25° (0°)
		{0, "N", "Nord"},
		{11, "N", "Nord (edge case)"},
		{12, "NNO", "Nord-nordost (boundary)"},

		// NNO: 11.25° - 33.75°
		{22, "NNO", "Nord-nordost"},
		{33, "NNO", "Nord-nordost (edge case)"},
		{34, "NO", "Nordost (boundary)"},

		// NO: 33.75° - 56.25°
		{45, "NO", "Nordost"},
		{56, "NO", "Nordost (edge case)"},
		{57, "ONO", "Ost-nordost (boundary)"},

		// ONO: 56.25° - 78.75°
		{67, "ONO", "Ost-nordost"},
		{78, "ONO", "Ost-nordost (edge case)"},
		{79, "O", "Ost (boundary)"},

		// O: 78.75° - 101.25°
		{90, "O", "Ost"},
		{101, "O", "Ost (edge case)"},
		{102, "OSO", "Ost-sydost (boundary)"},

		// OSO: 101.25° - 123.75°
		{112, "OSO", "Ost-sydost"},
		{123, "OSO", "Ost-sydost (edge case)"},
		{124, "SO", "Sydost (boundary)"},

		// SO: 123.75° - 146.25°
		{135, "SO", "Sydost"},
		{146, "SO", "Sydost (edge case)"},
		{147, "SSO", "Syd-sydost (boundary)"},

		// SSO: 146.25° - 168.75°
		{157, "SSO", "Syd-sydost"},
		{168, "SSO", "Syd-sydost (edge case)"},
		{169, "S", "Syd (boundary)"},

		// S: 168.75° - 191.25°
		{180, "S", "Syd"},
		{191, "S", "Syd (edge case)"},
		{192, "SSV", "Syd-sydväst (boundary)"},

		// SSV: 191.25° - 213.75°
		{202, "SSV", "Syd-sydväst"},
		{213, "SSV", "Syd-sydväst (edge case)"},
		{214, "SV", "Sydväst (boundary)"},

		// SV: 213.75° - 236.25°
		{225, "SV", "Sydväst"},
		{236, "SV", "Sydväst (edge case)"},
		{237, "VSV", "Väst-sydväst (boundary)"},

		// VSV: 236.25° - 258.75°
		{243, "VSV", "Väst-sydväst (real API example)"},
		{246, "VSV", "Väst-sydväst (another real API example)"},
		{258, "VSV", "Väst-sydväst (edge case)"},
		{259, "V", "Väst (boundary)"},

		// V: 258.75° - 281.25°
		{270, "V", "Väst"},
		{281, "V", "Väst (edge case)"},
		{282, "VNV", "Väst-nordväst (boundary)"},

		// VNV: 281.25° - 303.75°
		{292, "VNV", "Väst-nordväst"},
		{303, "VNV", "Väst-nordväst (edge case)"},
		{304, "NV", "Nordväst (boundary)"},

		// NV: 303.75° - 326.25°
		{315, "NV", "Nordväst"},
		{326, "NV", "Nordväst (edge case)"},
		{327, "NNV", "Nord-nordväst (boundary)"},

		// NNV: 326.25° - 348.75°
		{337, "NNV", "Nord-nordväst"},
		{348, "NNV", "Nord-nordväst (edge case)"},
		{349, "N", "Nord (boundary)"},

		// Special cases
		{360, "N", "Nord (full circle)"},
		{361, "N", "Nord (over 360)"},
	}

	for _, tc := range testCases {
		wd := &WeatherData{WindDirection: tc.direction}
		result := wd.GetWindDirectionText()
		if result != tc.expected {
			t.Errorf("GetWindDirectionText(%d) = %s, expected %s (%s)", tc.direction, result, tc.expected, tc.comment)
		}
	}
}

func TestAllCompassDirections(t *testing.T) {
	// Test that all 16 compass directions are correctly mapped
	expectedDirections := []struct {
		degrees   int
		direction string
		swedish   string
	}{
		{0, "N", "Nord"},
		{22, "NNO", "Nord-nordost"},
		{45, "NO", "Nordost"},
		{67, "ONO", "Ost-nordost"},
		{90, "O", "Ost"},
		{112, "OSO", "Ost-sydost"},
		{135, "SO", "Sydost"},
		{157, "SSO", "Syd-sydost"},
		{180, "S", "Syd"},
		{202, "SSV", "Syd-sydväst"},
		{225, "SV", "Sydväst"},
		{247, "VSV", "Väst-sydväst"},
		{270, "V", "Väst"},
		{292, "VNV", "Väst-nordväst"},
		{315, "NV", "Nordväst"},
		{337, "NNV", "Nord-nordväst"},
	}

	t.Log("Testing all 16 Swedish compass directions:")
	for _, expected := range expectedDirections {
		wd := &WeatherData{WindDirection: expected.degrees}
		result := wd.GetWindDirectionText()
		if result != expected.direction {
			t.Errorf("GetWindDirectionText(%d°) = %s, expected %s (%s)",
				expected.degrees, result, expected.direction, expected.swedish)
		} else {
			t.Logf("✓ %3d° = %s (%s)", expected.degrees, result, expected.swedish)
		}
	}
}

func TestGetWindDirectionFullName(t *testing.T) {
	testCases := []struct {
		direction int
		expected  string
	}{
		{0, "Nord"},
		{22, "Nord-nordost"},
		{45, "Nordost"},
		{67, "Ost-nordost"},
		{90, "Ost"},
		{112, "Ost-sydost"},
		{135, "Sydost"},
		{157, "Syd-sydost"},
		{180, "Syd"},
		{202, "Syd-sydväst"},
		{225, "Sydväst"},
		{243, "Väst-sydväst"}, // Real API example
		{246, "Väst-sydväst"}, // Another real API example
		{270, "Väst"},
		{292, "Väst-nordväst"},
		{315, "Nordväst"},
		{337, "Nord-nordväst"},
		{360, "Nord"},
	}

	for _, tc := range testCases {
		wd := &WeatherData{WindDirection: tc.direction}
		result := wd.GetWindDirectionFullName()
		if result != tc.expected {
			t.Errorf("GetWindDirectionFullName(%d) = %s, expected %s", tc.direction, result, tc.expected)
		}
	}
}

// Note: We don't test GetWeatherData() with real API calls in unit tests
// as it would require network access and could be unreliable.
// Integration tests would be more appropriate for that.
