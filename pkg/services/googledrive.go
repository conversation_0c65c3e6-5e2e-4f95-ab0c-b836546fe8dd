package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/drive/v3"
	"google.golang.org/api/option"
	"google.golang.org/api/sheets/v4"
)

// GoogleDriveService handles Google Drive operations
type GoogleDriveService struct {
	config          *oauth2.Config
	tokenFile       string
	credentialsFile string
}

// NewGoogleDriveService creates a new Google Drive service
func NewGoogleDriveService(credentialsFile string, serverHost string, serverPort string, enableTLS bool) (*GoogleDriveService, error) {
	// Read credentials file
	b, err := os.ReadFile(credentialsFile)
	if err != nil {
		return nil, fmt.Errorf("unable to read client secret file: %v", err)
	}

	// Parse credentials
	config, err := google.ConfigFromJSON(b, drive.DriveScope, sheets.SpreadsheetsScope)
	if err != nil {
		return nil, fmt.Errorf("unable to parse client secret file to config: %v", err)
	}

	// Build the redirect URI dynamically based on server configuration
	scheme := "http"
	if enableTLS {
		scheme = "https"
	}
	if serverHost == "" {
		serverHost = "localhost"
	}
	if serverPort == "" {
		serverPort = "8080"
	}
	config.RedirectURL = fmt.Sprintf("%s://%s:%s/settings/google-drive/callback", scheme, serverHost, serverPort)

	// Set token file path
	tokenFile := filepath.Join(filepath.Dir(credentialsFile), "google_drive_token.json")

	return &GoogleDriveService{
		config:          config,
		tokenFile:       tokenFile,
		credentialsFile: credentialsFile,
	}, nil
}

// GetAuthURL returns the authorization URL for OAuth2 flow
func (gds *GoogleDriveService) GetAuthURL() string {
	log.Printf("DEBUG: OAuth scopes being requested: %v", gds.config.Scopes)
	authURL := gds.config.AuthCodeURL("state-token", oauth2.AccessTypeOffline)
	log.Printf("DEBUG: Generated auth URL: %s", authURL)
	return authURL
}

// ExchangeCodeForToken exchanges authorization code for token
func (gds *GoogleDriveService) ExchangeCodeForToken(code string) error {
	token, err := gds.config.Exchange(context.TODO(), code)
	if err != nil {
		return fmt.Errorf("unable to retrieve token from web: %v", err)
	}
	return gds.saveToken(token)
}

// saveToken saves a token to a file path
func (gds *GoogleDriveService) saveToken(token *oauth2.Token) error {
	log.Printf("Saving credential file to: %s\n", gds.tokenFile)
	f, err := os.OpenFile(gds.tokenFile, os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0600)
	if err != nil {
		return fmt.Errorf("unable to cache oauth token: %v", err)
	}
	defer f.Close()
	return json.NewEncoder(f).Encode(token)
}

// getTokenFromFile retrieves a token from a local file
func (gds *GoogleDriveService) getTokenFromFile() (*oauth2.Token, error) {
	f, err := os.Open(gds.tokenFile)
	if err != nil {
		return nil, err
	}
	defer f.Close()
	tok := &oauth2.Token{}
	err = json.NewDecoder(f).Decode(tok)
	return tok, err
}

// getClient retrieves a token, saves the token, then returns the generated client
func (gds *GoogleDriveService) getClient() (*http.Client, error) {
	tok, err := gds.getTokenFromFile()
	if err != nil {
		return nil, fmt.Errorf("token file not found, authorization required: %v", err)
	}
	return gds.config.Client(context.Background(), tok), nil
}

// IsAuthenticated checks if the service is authenticated
func (gds *GoogleDriveService) IsAuthenticated() bool {
	_, err := gds.getTokenFromFile()
	return err == nil
}

// CreateSpreadsheet creates a new Google Sheets spreadsheet
func (gds *GoogleDriveService) CreateSpreadsheet(title string, folderID string) (string, error) {
	log.Printf("DEBUG: CreateSpreadsheet called with title: %s, folderID: %s", title, folderID)

	client, err := gds.getClient()
	if err != nil {
		log.Printf("ERROR: Failed to get client: %v", err)
		return "", err
	}
	log.Printf("DEBUG: Successfully got OAuth client")

	srv, err := sheets.NewService(context.Background(), option.WithHTTPClient(client))
	if err != nil {
		log.Printf("ERROR: Failed to create Sheets service: %v", err)
		return "", fmt.Errorf("unable to retrieve Sheets client: %v", err)
	}
	log.Printf("DEBUG: Successfully created Sheets service")

	// Create spreadsheet directly in the target folder if specified
	if folderID != "" {
		log.Printf("DEBUG: Creating spreadsheet directly in folder ID: %s", folderID)
		spreadsheetID, err := gds.createSpreadsheetInFolder(title, folderID)
		if err != nil {
			log.Printf("ERROR: Failed to create spreadsheet in folder: %v", err)
			return "", fmt.Errorf("unable to create spreadsheet in folder: %v", err)
		}
		log.Printf("DEBUG: Successfully created spreadsheet in folder, ID: %s", spreadsheetID)
		return spreadsheetID, nil
	} else {
		log.Printf("DEBUG: No folder ID specified, creating spreadsheet in root folder")
		// Create spreadsheet in root folder
		spreadsheet := &sheets.Spreadsheet{
			Properties: &sheets.SpreadsheetProperties{
				Title: title,
			},
		}

		log.Printf("DEBUG: Calling Sheets API to create spreadsheet")
		resp, err := srv.Spreadsheets.Create(spreadsheet).Do()
		if err != nil {
			log.Printf("ERROR: Sheets API call failed: %v", err)
			return "", fmt.Errorf("unable to create spreadsheet: %v", err)
		}
		log.Printf("DEBUG: Sheets API call successful, spreadsheet ID: %s", resp.SpreadsheetId)
		return resp.SpreadsheetId, nil
	}
}

// createSpreadsheetInFolder creates a spreadsheet directly in a specific folder using Google Drive API
func (gds *GoogleDriveService) createSpreadsheetInFolder(title, folderID string) (string, error) {
	client, err := gds.getClient()
	if err != nil {
		return "", err
	}

	driveSrv, err := drive.NewService(context.Background(), option.WithHTTPClient(client))
	if err != nil {
		return "", fmt.Errorf("unable to retrieve Drive client: %v", err)
	}

	// Create a Google Sheets file directly in the specified folder
	file := &drive.File{
		Name:     title,
		MimeType: "application/vnd.google-apps.spreadsheet",
		Parents:  []string{folderID},
	}

	log.Printf("DEBUG: Creating spreadsheet file in Drive API")
	resp, err := driveSrv.Files.Create(file).Do()
	if err != nil {
		return "", fmt.Errorf("unable to create spreadsheet file: %v", err)
	}

	log.Printf("DEBUG: Successfully created spreadsheet file in folder, ID: %s", resp.Id)
	return resp.Id, nil
}

// moveFileToFolder moves a file to a specific folder in Google Drive
func (gds *GoogleDriveService) moveFileToFolder(fileID, folderID string) error {
	client, err := gds.getClient()
	if err != nil {
		return err
	}

	driveSrv, err := drive.NewService(context.Background(), option.WithHTTPClient(client))
	if err != nil {
		return fmt.Errorf("unable to retrieve Drive client: %v", err)
	}

	// Get the file to retrieve current parents
	file, err := driveSrv.Files.Get(fileID).Fields("parents").Do()
	if err != nil {
		return fmt.Errorf("unable to get file: %v", err)
	}

	// Move the file to the new folder
	_, err = driveSrv.Files.Update(fileID, nil).
		AddParents(folderID).
		RemoveParents(file.Parents[0]).
		Do()
	if err != nil {
		return fmt.Errorf("unable to move file: %v", err)
	}

	return nil
}

// WriteToSpreadsheet writes data to a Google Sheets spreadsheet
func (gds *GoogleDriveService) WriteToSpreadsheet(spreadsheetID string, sheetName string, data [][]interface{}) error {
	client, err := gds.getClient()
	if err != nil {
		return err
	}

	srv, err := sheets.NewService(context.Background(), option.WithHTTPClient(client))
	if err != nil {
		return fmt.Errorf("unable to retrieve Sheets client: %v", err)
	}

	// Prepare the range
	writeRange := fmt.Sprintf("%s!A1", sheetName)

	// Prepare the value range
	valueRange := &sheets.ValueRange{
		Values: data,
	}

	// Write the data
	_, err = srv.Spreadsheets.Values.Update(spreadsheetID, writeRange, valueRange).
		ValueInputOption("RAW").Do()
	if err != nil {
		return fmt.Errorf("unable to write data to spreadsheet: %v", err)
	}

	return nil
}

// ListFolders lists folders in Google Drive
func (gds *GoogleDriveService) ListFolders() ([]*drive.File, error) {
	client, err := gds.getClient()
	if err != nil {
		return nil, err
	}

	driveSrv, err := drive.NewService(context.Background(), option.WithHTTPClient(client))
	if err != nil {
		return nil, fmt.Errorf("unable to retrieve Drive client: %v", err)
	}

	// List folders
	query := "mimeType='application/vnd.google-apps.folder' and trashed=false"
	resp, err := driveSrv.Files.List().Q(query).Fields("files(id, name)").Do()
	if err != nil {
		return nil, fmt.Errorf("unable to list folders: %v", err)
	}

	return resp.Files, nil
}

// CreateFolder creates a new folder in Google Drive
func (gds *GoogleDriveService) CreateFolder(name string, parentFolderID string) (string, error) {
	client, err := gds.getClient()
	if err != nil {
		return "", err
	}

	driveSrv, err := drive.NewService(context.Background(), option.WithHTTPClient(client))
	if err != nil {
		return "", fmt.Errorf("unable to retrieve Drive client: %v", err)
	}

	// Create folder metadata
	folder := &drive.File{
		Name:     name,
		MimeType: "application/vnd.google-apps.folder",
	}

	// Set parent folder if specified
	if parentFolderID != "" {
		folder.Parents = []string{parentFolderID}
	}

	// Create the folder
	resp, err := driveSrv.Files.Create(folder).Do()
	if err != nil {
		return "", fmt.Errorf("unable to create folder: %v", err)
	}

	return resp.Id, nil
}

// GetFolderByName finds a folder by name
func (gds *GoogleDriveService) GetFolderByName(name string) (*drive.File, error) {
	client, err := gds.getClient()
	if err != nil {
		return nil, err
	}

	driveSrv, err := drive.NewService(context.Background(), option.WithHTTPClient(client))
	if err != nil {
		return nil, fmt.Errorf("unable to retrieve Drive client: %v", err)
	}

	// Search for folder by name
	query := fmt.Sprintf("name='%s' and mimeType='application/vnd.google-apps.folder' and trashed=false", name)
	resp, err := driveSrv.Files.List().Q(query).Fields("files(id, name)").Do()
	if err != nil {
		return nil, fmt.Errorf("unable to search for folder: %v", err)
	}

	if len(resp.Files) == 0 {
		return nil, fmt.Errorf("folder not found: %s", name)
	}

	return resp.Files[0], nil
}

// FormatResultsForSpreadsheet converts results data to spreadsheet format
func FormatResultsForSpreadsheet(results interface{}, eventName string, eventDate time.Time) [][]interface{} {
	var data [][]interface{}

	// Add title row
	data = append(data, []interface{}{fmt.Sprintf("Resultat - %s", eventName)})
	data = append(data, []interface{}{fmt.Sprintf("Datum: %s", eventDate.Format("2006-01-02"))})
	data = append(data, []interface{}{}) // Empty row

	// Add headers
	headers := []interface{}{
		"Placering",
		"Seglare",
		"Klubb",
		"Båt",
		"Båttyp",
		"Segelnummer",
		"Mätbrevsnummer",
		"SRS-värde",
		"SRS-typ",
		"Besättning",
		"Starttid",
		"Måltid",
		"Seglad tid",
		"Korrigerad tid",
		"Efter föregående",
		"Efter vinnare",
	}
	data = append(data, headers)

	// This will be implemented based on the specific result type
	// For now, return the basic structure
	return data
}
