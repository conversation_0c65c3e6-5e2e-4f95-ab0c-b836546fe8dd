package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/rb<PERSON>regren/segling/pkg/database"
)

// WeatherService handles weather data operations
type WeatherService struct {
	apiURL  string
	authKey string
	db      *database.DB
}

// WindValue represents a measured value from the weather API
type WindValue struct {
	Origin      string  `json:"Origin"`
	SensorNames string  `json:"SensorNames"`
	Value       float64 `json:"Value"`
}

// WindDirection represents wind direction from the weather API
type WindDirection struct {
	Origin      string `json:"Origin"`
	SensorNames string `json:"SensorNames"`
	Value       int    `json:"Value"`
}

// WindData represents wind information from the weather API
type WindData struct {
	Speed     WindValue     `json:"Speed"`
	Direction WindDirection `json:"Direction"`
}

// TemperatureData represents temperature information from the weather API
type TemperatureData struct {
	Temperature WindValue `json:"Temperature"`
}

// AggregatedWindData represents 10-minute aggregated wind data
type AggregatedWindData struct {
	Wind struct {
		SpeedMax     WindValue `json:"SpeedMax"`
		SpeedAverage WindValue `json:"SpeedAverage"`
	} `json:"Wind"`
}

// WeatherObservation represents a weather observation from the API
type WeatherObservation struct {
	Wind                []WindData         `json:"Wind"`
	Air                 TemperatureData    `json:"Air"`
	Aggregated10minutes AggregatedWindData `json:"Aggregated10minutes"`
	ModifiedTime        string             `json:"ModifiedTime"`
}

// WeatherResponse represents the full API response
type WeatherResponse struct {
	RESPONSE struct {
		RESULT []struct {
			WeatherObservation []WeatherObservation `json:"WeatherObservation"`
		} `json:"RESULT"`
	} `json:"RESPONSE"`
}

// WeatherData represents simplified weather data for the frontend
type WeatherData struct {
	WindSpeed        float64   `json:"wind_speed"`
	WindDirection    int       `json:"wind_direction"`
	WindSpeedMax     float64   `json:"wind_speed_max"`
	WindSpeedAverage float64   `json:"wind_speed_average"`
	Temperature      float64   `json:"temperature"`
	LastUpdated      time.Time `json:"last_updated"`
	Location         string    `json:"location"`
}

// NewWeatherService creates a new weather service
func NewWeatherService(db *database.DB) *WeatherService {
	return &WeatherService{
		apiURL:  "https://api.trafikinfo.trafikverket.se/v2/data.json",
		authKey: "44b7a85536ef4238bddd7d885976a1f0", // Default, will be overridden by settings
		db:      db,
	}
}

// GetWeatherData fetches current weather data from the configured weather station
func (ws *WeatherService) GetWeatherData() (*WeatherData, error) {
	// Get settings from database
	authKey, err := ws.db.GetSetting("weather_api_token")
	if err != nil || authKey == "" {
		authKey = ws.authKey // Fallback to default
	}

	location, err := ws.db.GetSetting("weather_location")
	if err != nil || location == "" {
		location = "Linköping" // Fallback to default
	}

	// Create the XML request body
	xmlRequest := `<REQUEST>
  <LOGIN authenticationkey="` + authKey + `"/>
    <QUERY objecttype="WeatherObservation" namespace="Road.WeatherInfo" schemaversion="2.1" limit="1">
    <FILTER>   <EQ name="Measurepoint.Name" value="` + location + `" />   </FILTER>
    <INCLUDE>Wind.Speed</INCLUDE>
    <INCLUDE>Wind.Direction</INCLUDE>
    <INCLUDE>Air.Temperature</INCLUDE>
    <INCLUDE>Aggregated10minutes.Wind</INCLUDE>
    <INCLUDE>ModifiedTime</INCLUDE>
    </QUERY>
</REQUEST>`

	// Create HTTP request
	req, err := http.NewRequest("POST", ws.apiURL, bytes.NewBufferString(xmlRequest))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Content-Type", "application/xml")

	// Make the request
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response: %w", err)
	}

	// Parse JSON response
	var weatherResp WeatherResponse
	if err := json.Unmarshal(body, &weatherResp); err != nil {
		return nil, fmt.Errorf("error parsing JSON response: %w", err)
	}

	// Extract weather data
	if len(weatherResp.RESPONSE.RESULT) == 0 ||
		len(weatherResp.RESPONSE.RESULT[0].WeatherObservation) == 0 ||
		len(weatherResp.RESPONSE.RESULT[0].WeatherObservation[0].Wind) == 0 {
		return nil, fmt.Errorf("no weather data found in response")
	}

	observation := weatherResp.RESPONSE.RESULT[0].WeatherObservation[0]
	wind := observation.Wind[0]

	// Parse the timestamp
	lastUpdated, err := time.Parse(time.RFC3339, observation.ModifiedTime)
	if err != nil {
		// If parsing fails, use current time
		lastUpdated = time.Now()
	}

	return &WeatherData{
		WindSpeed:        wind.Speed.Value,
		WindDirection:    wind.Direction.Value,
		WindSpeedMax:     observation.Aggregated10minutes.Wind.SpeedMax.Value,
		WindSpeedAverage: observation.Aggregated10minutes.Wind.SpeedAverage.Value,
		Temperature:      observation.Air.Temperature.Value,
		LastUpdated:      lastUpdated,
		Location:         location,
	}, nil
}

// GetWindDirectionText converts wind direction degrees to Swedish compass directions (short form)
func (wd *WeatherData) GetWindDirectionText() string {
	directions := []string{"N", "NNO", "NO", "ONO", "O", "OSO", "SO", "SSO", "S", "SSV", "SV", "VSV", "V", "VNV", "NV", "NNV"}
	index := int((float64(wd.WindDirection) + 11.25) / 22.5)
	return directions[index%16]
}

// GetWindDirectionFullName converts wind direction degrees to full Swedish compass direction names
func (wd *WeatherData) GetWindDirectionFullName() string {
	directions := []string{
		"Nord", "Nord-nordost", "Nordost", "Ost-nordost",
		"Ost", "Ost-sydost", "Sydost", "Syd-sydost",
		"Syd", "Syd-sydväst", "Sydväst", "Väst-sydväst",
		"Väst", "Väst-nordväst", "Nordväst", "Nord-nordväst",
	}
	index := int((float64(wd.WindDirection) + 11.25) / 22.5)
	return directions[index%16]
}
