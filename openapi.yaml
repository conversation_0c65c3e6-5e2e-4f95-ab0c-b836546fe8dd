openapi: 3.0.3
info:
  title: Segling API
  description: |
    API for the Segling application, a sailing competition management system.
    This API provides endpoints for managing sailors, boats, events, and competition results.
  version: 1.0.0
servers:
  - url: http://localhost:8080
    description: Local development server
paths:
  /:
    get:
      summary: Home page
      description: Returns the home page with a list of events
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
  /about:
    get:
      summary: About page
      description: Returns information about the application
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
  /help:
    get:
      summary: Help page
      description: Redirects to the About page
      responses:
        '301':
          description: Moved permanently
          headers:
            Location:
              schema:
                type: string
              example: /about
  /settings:
    get:
      summary: Get settings page
      description: Returns the settings page
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
    post:
      summary: Update settings
      description: Updates application settings
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                auto_backup_on_event_create:
                  type: string
                  enum: ['true', 'false']
      responses:
        '200':
          description: Settings updated successfully
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /settings/club:
    post:
      summary: Update club setting
      description: Updates the default club setting
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                default_club:
                  type: string
      responses:
        '200':
          description: Club setting updated successfully
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /settings/time-format:
    post:
      summary: Update time format setting
      description: Updates the time format setting (12h or 24h)
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                use_24h_time:
                  type: string
                  enum: ['true', 'false']
      responses:
        '200':
          description: Time format setting updated successfully
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /settings/competition-types:
    post:
      summary: Update competition types setting
      description: Updates the competition types and default competition type
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                competition_types:
                  type: string
                  description: Comma-separated list of competition types
                default_competition_type:
                  type: string
                  description: Default competition type
      responses:
        '200':
          description: Competition types updated successfully
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /settings/github-pages:
    post:
      summary: Update GitHub Pages settings
      description: Updates the GitHub Pages integration settings
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                github_pages_enabled:
                  type: string
                  enum: ['true', 'false']
                github_pages_repo:
                  type: string
                github_pages_branch:
                  type: string
                github_pages_token:
                  type: string
      responses:
        '200':
          description: GitHub Pages settings updated successfully
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /backups:
    get:
      summary: Get backups page
      description: Returns the backups management page
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
  /backups/restore:
    post:
      summary: Restore backup
      description: Restores a database backup
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                filename:
                  type: string
                  description: Backup filename to restore
      responses:
        '200':
          description: Backup restored successfully
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /sailors:
    get:
      summary: Get sailors page
      description: Returns the sailors management page with optional search
      parameters:
        - name: search
          in: query
          description: Search term for filtering sailors
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
    post:
      summary: Create or update sailor
      description: Creates a new sailor or updates an existing one
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Sailor ID (empty for new sailors)
                namn:
                  type: string
                  description: Sailor name
                telefon:
                  type: string
                  description: Sailor phone number
                klubb:
                  type: string
                  description: Sailor club
      responses:
        '303':
          description: Redirect to sailors page
          headers:
            Location:
              schema:
                type: string
              example: /sailors
        '400':
          description: Bad request
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /sailors/{id}:
    get:
      summary: Get sailor form
      description: Returns the form for editing a sailor
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Sailor ID
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
    delete:
      summary: Delete sailor
      description: Deletes a sailor by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Sailor ID
      responses:
        '200':
          description: Sailor deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /api/sailors/search:
    get:
      summary: Search sailors (API)
      description: Returns filtered sailors based on search term (for HTMX live search)
      parameters:
        - name: search
          in: query
          description: Search term
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /sailors/search:
    get:
      summary: Search sailors
      description: Returns filtered sailors based on search term (for HTMX live search)
      parameters:
        - name: search
          in: query
          description: Search term
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /boats:
    get:
      summary: Get boats page
      description: Returns the boats management page with optional search
      parameters:
        - name: search
          in: query
          description: Search term for filtering boats
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
    post:
      summary: Create or update boat
      description: Creates a new boat or updates an existing one
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Boat ID (empty for new boats)
                namn:
                  type: string
                  description: Boat name
                battyp:
                  type: string
                  description: Boat type
                matbrevs_nummer:
                  type: string
                  description: Measurement certificate number
                segelnummer:
                  type: string
                  description: Sail number
                nationality:
                  type: string
                  description: Nationality (defaults to SWE)
                srs:
                  type: number
                  format: float
                  description: SRS value
                srs_utan_undanvindsegel:
                  type: number
                  format: float
                  description: SRS value without spinnaker
                srs_shorthanded:
                  type: number
                  format: float
                  description: SRS value for shorthanded sailing
                srs_shorthanded_utan_undanvindsegel:
                  type: number
                  format: float
                  description: SRS value for shorthanded sailing without spinnaker
      responses:
        '303':
          description: Redirect to boats page
          headers:
            Location:
              schema:
                type: string
              example: /boats
        '400':
          description: Bad request
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /boats/{id}:
    get:
      summary: Get boat form
      description: Returns the form for editing a boat
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Boat ID
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
    delete:
      summary: Delete boat
      description: Deletes a boat by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Boat ID
      responses:
        '200':
          description: Boat deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /boats/{id}/json:
    get:
      summary: Get boat as JSON
      description: Returns boat information in JSON format
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Boat ID
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Boat'
        '404':
          description: Boat not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /api/boats/search:
    get:
      summary: Search boats (API)
      description: Returns filtered boats based on search term (for HTMX live search)
      parameters:
        - name: search
          in: query
          description: Search term
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /boats/search:
    get:
      summary: Search boats
      description: Returns filtered boats based on search term (for HTMX live search)
      parameters:
        - name: search
          in: query
          description: Search term
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /events:
    get:
      summary: Get events page
      description: Returns the events management page
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
    post:
      summary: Create or update event
      description: Creates a new event or updates an existing one
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Event ID (empty for new events)
                namn:
                  type: string
                  description: Event name
                datum:
                  type: string
                  format: date
                  description: Event date
                starttid:
                  type: string
                  description: Start time
                vind:
                  type: integer
                  description: Wind speed
                banlangd:
                  type: integer
                  description: Course length
                jaktstart:
                  type: boolean
                  description: Whether the event uses pursuit start
                tavlingstyp:
                  type: string
                  description: Type of competition
                beskrivning:
                  type: string
                  description: Event description
      responses:
        '303':
          description: Redirect to events page
          headers:
            Location:
              schema:
                type: string
              example: /events
        '400':
          description: Bad request
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /events/{id}:
    get:
      summary: Get event form
      description: Returns the form for editing an event
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Event ID
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '303':
          description: Redirect to events page
          headers:
            Location:
              schema:
                type: string
              example: /events
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
    delete:
      summary: Delete event
      description: Deletes an event by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Event ID
      responses:
        '200':
          description: Event deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /api/events:
    get:
      summary: Get events as JSON
      description: Returns all events in JSON format
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Event'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /api/events/{id}:
    get:
      summary: Get event as JSON
      description: Returns event information in JSON format
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Event ID
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Event'
        '404':
          description: Event not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /api/events/{id}/participant-count:
    get:
      summary: Get event participant count
      description: Returns the number of participants in an event
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Event ID
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  count:
                    type: integer
        '404':
          description: Event not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /events/{id}/participants:
    get:
      summary: Get event participants page
      description: Returns the participants management page for an event
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Event ID
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
    post:
      summary: Add participant to event
      description: Adds a sailor with a boat to an event
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Event ID
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                sailor_id:
                  type: integer
                  format: int64
                  description: Sailor ID
                boat_id:
                  type: integer
                  format: int64
                  description: Boat ID
                srs_type:
                  type: string
                  description: Type of SRS value to use
                custom_srs_value:
                  type: number
                  format: float
                  description: Custom SRS value
                use_custom_srs_value:
                  type: boolean
                  description: Whether to use the custom SRS value
                crew_count:
                  type: integer
                  description: Number of crew members
      responses:
        '200':
          description: Participant added successfully
          content:
            text/html:
              schema:
                type: string
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /events/{id}/jaktstart:
    get:
      summary: Get jaktstart times
      description: Returns the jaktstart (pursuit start) times for an event
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Event ID
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /events/{id}/jaktstart/print:
    get:
      summary: Get printable jaktstart times
      description: Returns a printer-friendly version of the jaktstart times
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Event ID
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /events/{id}/finish-times:
    get:
      summary: Get finish times page
      description: Returns the page for entering finish times
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Event ID
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /events/{id}/finish-times/{participant_id}:
    post:
      summary: Update finish time
      description: Updates the finish time for a participant
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Event ID
        - name: participant_id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Participant ID
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                finish_time:
                  type: string
                  description: Finish time in HH:MM:SS format
      responses:
        '200':
          description: Finish time updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /events/{id}/results:
    get:
      summary: Get results page
      description: Returns the results page for an event
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Event ID
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /events/{id}/results/print:
    get:
      summary: Get printable results
      description: Returns a printer-friendly version of the results
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Event ID
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /events/{id}/results/csv:
    get:
      summary: Export results as CSV
      description: Returns the results in CSV format
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Event ID
      responses:
        '200':
          description: Successful response
          content:
            text/csv:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /srs:
    get:
      summary: Get SRS management page
      description: Returns the SRS data management page
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /srs/boat-types:
    get:
      summary: Get SRS boat types page
      description: Returns the SRS boat types page with optional search
      parameters:
        - name: search
          in: query
          description: Search term for filtering boat types
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /srs/matbrev:
    get:
      summary: Get SRS matbrev page
      description: Returns the SRS matbrev (measurement certificates) page
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /srs/sync:
    post:
      summary: Sync SRS data
      description: |
        Synchronizes SRS data from external sources. This operation may take several minutes to complete.
        The UI displays a loading indicator to prevent double-clicking and shows progress to the user.
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                source:
                  type: string
                  enum: [srs_tabell, matbrev, all, boat_types]
                  description: Source to sync from
                  example: all
              required:
                - source
      responses:
        '303':
          description: Redirect to SRS management page after successful synchronization
          headers:
            Location:
              schema:
                type: string
              example: /srs
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /api/srs:
    get:
      summary: Get SRS data
      description: Returns SRS data for a boat type or measurement certificate
      parameters:
        - name: battyp
          in: query
          description: Boat type
          required: false
          schema:
            type: string
        - name: matbrevs_nummer
          in: query
          description: Measurement certificate number
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  battyp:
                    type: string
                  srs:
                    type: number
                    format: float
                  srs_utan_undanvindsegel:
                    type: number
                    format: float
                  srs_shorthanded:
                    type: number
                    format: float
                  srs_shorthanded_utan_undanvindsegel:
                    type: number
                    format: float
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /api/matbrev:
    get:
      summary: Get matbrev data
      description: Returns measurement certificate data for a boat
      parameters:
        - name: matbrevs_nummer
          in: query
          description: Measurement certificate number
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SRSMatbrev'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /srs/boat-types/export:
    get:
      summary: Export SRS boat types as CSV
      description: Returns all SRS boat types in CSV format
      responses:
        '200':
          description: Successful response
          content:
            text/csv:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /srs/matbrev/export:
    get:
      summary: Export SRS matbrev as CSV
      description: Returns all measurement certificates in CSV format
      responses:
        '200':
          description: Successful response
          content:
            text/csv:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /events/{id}/publish:
    post:
      summary: Publish results to GitHub Pages
      description: Publishes event results to GitHub Pages
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: Event ID
      responses:
        '200':
          description: Results published successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  url:
                    type: string
                    description: URL to the published page
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /github-pages:
    get:
      summary: Get GitHub Pages management page
      description: Returns the GitHub Pages management page
      responses:
        '200':
          description: Successful response
          content:
            text/html:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/html:
              schema:
                type: string
  /api/github-pages:
    get:
      summary: List GitHub Pages
      description: Returns a list of published GitHub Pages
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    path:
                      type: string
                      description: Path to the published page
                    url:
                      type: string
                      description: URL to the published page
                    date:
                      type: string
                      format: date-time
                      description: Publication date
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /api/github-pages/{path}:
    delete:
      summary: Delete from GitHub Pages
      description: Deletes a published page from GitHub Pages
      parameters:
        - name: path
          in: path
          required: true
          schema:
            type: string
          description: Path to the file to delete (including year and filename)
          example: 2025/Regatta/2025-05-29-the-cup.html
      responses:
        '200':
          description: Page deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /api/github-pages/check:
    get:
      summary: Check page availability
      description: Checks if a published page is available
      parameters:
        - name: url
          in: query
          required: true
          schema:
            type: string
          description: URL to check
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  available:
                    type: boolean
                  status:
                    type: integer
                  message:
                    type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
components:
  schemas:
    Sailor:
      type: object
      properties:
        id:
          type: integer
          format: int64
        namn:
          type: string
          description: Sailor name
        telefon:
          type: string
          description: Sailor phone number
        klubb:
          type: string
          description: Sailor club
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    Boat:
      type: object
      properties:
        id:
          type: integer
          format: int64
        namn:
          type: string
          description: Boat name
        battyp:
          type: string
          description: Boat type
        matbrevs_nummer:
          type: string
          description: Measurement certificate number
        segelnummer:
          type: string
          description: Sail number
        nationality:
          type: string
          description: Nationality (defaults to SWE)
        srs:
          type: number
          format: float
          description: SRS value
        srs_utan_undanvindsegel:
          type: number
          format: float
          description: SRS value without spinnaker
        srs_shorthanded:
          type: number
          format: float
          description: SRS value for shorthanded sailing
        srs_shorthanded_utan_undanvindsegel:
          type: number
          format: float
          description: SRS value for shorthanded sailing without spinnaker
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    Event:
      type: object
      properties:
        id:
          type: integer
          format: int64
        namn:
          type: string
          description: Event name
        datum:
          type: string
          format: date-time
          description: Event date
        starttid:
          type: string
          description: Start time
        vind:
          type: integer
          description: Wind speed
        banlangd:
          type: integer
          description: Course length
        jaktstart:
          type: boolean
          description: Whether the event uses pursuit start
        tavlingstyp:
          type: string
          description: Type of competition (e.g., Kvällssegling, Regatta)
        beskrivning:
          type: string
          description: Event description
        locked:
          type: boolean
          description: Whether the event is locked (results finalized)
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        participant_count:
          type: integer
          description: Number of participants in the event
    EventParticipant:
      type: object
      properties:
        id:
          type: integer
          format: int64
        event_id:
          type: integer
          format: int64
        sailor_id:
          type: integer
          format: int64
        boat_id:
          type: integer
          format: int64
        srs_type:
          type: string
          description: Type of SRS value used
        selected_srs_value:
          type: number
          format: float
          description: Selected SRS value
        custom_srs_value:
          type: number
          format: float
          description: Custom SRS value
        use_custom_srs_value:
          type: boolean
          description: Whether to use the custom SRS value
        finish_time:
          type: string
          description: Finish time
        crew_count:
          type: integer
          description: Number of crew members
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    SRSBoatType:
      type: object
      properties:
        id:
          type: integer
          format: int64
        battyp:
          type: string
          description: Boat type
        srs:
          type: number
          format: float
          description: SRS value
        srs_utan_undanvindsegel:
          type: number
          format: float
          description: SRS value without spinnaker
        srs_shorthanded:
          type: number
          format: float
          description: SRS value for shorthanded sailing
        srs_shorthanded_utan_undanvindsegel:
          type: number
          format: float
          description: SRS value for shorthanded sailing without spinnaker
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    SRSMatbrev:
      type: object
      properties:
        id:
          type: integer
          format: int64
        matbrevs_nummer:
          type: string
          description: Measurement certificate number
        battyp:
          type: string
          description: Boat type
        bat_namn:
          type: string
          description: Boat name
        agare:
          type: string
          description: Owner name
        segelnummer:
          type: string
          description: Sail number
        nationality:
          type: string
          description: Nationality
        srs:
          type: number
          format: float
          description: SRS value
        srs_utan_undanvindsegel:
          type: number
          format: float
          description: SRS value without spinnaker
        srs_shorthanded:
          type: number
          format: float
          description: SRS value for shorthanded sailing
        srs_shorthanded_utan_undanvindsegel:
          type: number
          format: float
          description: SRS value for shorthanded sailing without spinnaker
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    Result:
      type: object
      properties:
        id:
          type: integer
          format: int64
        event_id:
          type: integer
          format: int64
        sailor_id:
          type: integer
          format: int64
        boat_id:
          type: integer
          format: int64
        sailor:
          $ref: '#/components/schemas/Sailor'
        boat:
          $ref: '#/components/schemas/Boat'
        srs_type:
          type: string
          description: Type of SRS value used
        selected_srs_value:
          type: number
          format: float
          description: Selected SRS value
        custom_srs_value:
          type: number
          format: float
          description: Custom SRS value
        use_custom_srs_value:
          type: boolean
          description: Whether to use the custom SRS value
        finish_time:
          type: string
          description: Finish time
        start_time:
          type: string
          description: Start time
        elapsed_time:
          type: string
          description: Elapsed time
        corrected_time:
          type: string
          description: Corrected time
        time_to_previous:
          type: string
          description: Time difference to the previous boat
        time_to_winner:
          type: string
          description: Time difference to the winner
        corrected_seconds:
          type: integer
          description: Corrected time in seconds
        total_persons:
          type: integer
          description: Total number of persons (skipper + crew)
        crew_count:
          type: integer
          description: Number of crew members
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time