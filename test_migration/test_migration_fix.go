package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	"github.com/rb<PERSON><PERSON>gren/segling/pkg/database"
	_ "modernc.org/sqlite"
)

func main() {
	// Copy the test database to a temporary location for testing
	srcDB := "tmp/segling.db"
	testDB := "test_migration_data_preservation.db"

	// Remove test database if it exists
	os.Remove(testDB)

	// Copy the source database
	err := copyFile(srcDB, testDB)
	if err != nil {
		log.Fatalf("Failed to copy database: %v", err)
	}
	defer os.Remove(testDB) // Clean up after test

	fmt.Println("Testing migration data preservation...")

	// First, check the data before migration
	fmt.Println("\n=== BEFORE MIGRATION ===")
	beforeData, err := checkEventParticipantsData(testDB)
	if err != nil {
		log.Fatalf("Failed to check data before migration: %v", err)
	}

	// Open database and trigger migration
	fmt.Println("\n=== RUNNING MIGRATION ===")
	db, err := database.New(testDB)
	if err != nil {
		log.Fatalf("Failed to open database: %v", err)
	}

	err = db.MigrateDatabase()
	if err != nil {
		log.Fatalf("Migration failed: %v", err)
	}
	db.Close()

	// Check the data after migration
	fmt.Println("\n=== AFTER MIGRATION ===")
	afterData, err := checkEventParticipantsData(testDB)
	if err != nil {
		log.Fatalf("Failed to check data after migration: %v", err)
	}

	// Compare the data
	fmt.Println("\n=== COMPARISON ===")
	if len(beforeData) != len(afterData) {
		fmt.Printf("❌ FAIL: Row count mismatch. Before: %d, After: %d\n", len(beforeData), len(afterData))
		return
	}

	dataPreserved := true
	for i, before := range beforeData {
		after := afterData[i]
		if before.ID != after.ID {
			fmt.Printf("❌ FAIL: ID mismatch at row %d. Before: %d, After: %d\n", i, before.ID, after.ID)
			dataPreserved = false
		}
		if before.SRSType != after.SRSType {
			fmt.Printf("❌ FAIL: SRS Type mismatch for ID %d. Before: '%s', After: '%s'\n", before.ID, before.SRSType, after.SRSType)
			dataPreserved = false
		}
		if before.CrewCount != after.CrewCount {
			fmt.Printf("❌ FAIL: Crew Count mismatch for ID %d. Before: %d, After: %d\n", before.ID, before.CrewCount, after.CrewCount)
			dataPreserved = false
		}
		if before.SelectedSRSValue != after.SelectedSRSValue {
			fmt.Printf("❌ FAIL: Selected SRS Value mismatch for ID %d. Before: %f, After: %f\n", before.ID, before.SelectedSRSValue, after.SelectedSRSValue)
			dataPreserved = false
		}
	}

	if dataPreserved {
		fmt.Printf("✅ SUCCESS: All data preserved! Checked %d rows.\n", len(beforeData))
		fmt.Println("   - SRS types preserved")
		fmt.Println("   - Crew counts preserved")
		fmt.Println("   - Selected SRS values preserved")
	} else {
		fmt.Println("❌ FAIL: Data was not properly preserved during migration")
	}
}

type ParticipantData struct {
	ID               int64
	SRSType          string
	CrewCount        int
	SelectedSRSValue float64
}

func checkEventParticipantsData(dbPath string) ([]ParticipantData, error) {
	db, err := sql.Open("sqlite", dbPath)
	if err != nil {
		return nil, err
	}
	defer db.Close()

	// Check which columns exist
	var hasSRSType, hasCrewCount, hasSelectedSRS int
	db.QueryRow(`SELECT COUNT(*) FROM pragma_table_info('event_participants') WHERE name = 'srs_type'`).Scan(&hasSRSType)
	db.QueryRow(`SELECT COUNT(*) FROM pragma_table_info('event_participants') WHERE name = 'crew_count'`).Scan(&hasCrewCount)
	db.QueryRow(`SELECT COUNT(*) FROM pragma_table_info('event_participants') WHERE name = 'selected_srs_value'`).Scan(&hasSelectedSRS)

	fmt.Printf("Schema check - SRS Type: %v, Crew Count: %v, Selected SRS: %v\n",
		hasSRSType > 0, hasCrewCount > 0, hasSelectedSRS > 0)

	// Build query based on available columns
	query := "SELECT id"
	if hasSRSType > 0 {
		query += ", COALESCE(srs_type, 'srs')"
	} else {
		query += ", 'srs'"
	}
	if hasCrewCount > 0 {
		query += ", COALESCE(crew_count, 0)"
	} else {
		query += ", 0"
	}
	if hasSelectedSRS > 0 {
		query += ", COALESCE(selected_srs_value, 0)"
	} else {
		query += ", 0"
	}
	query += " FROM event_participants ORDER BY id"

	rows, err := db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var data []ParticipantData
	for rows.Next() {
		var p ParticipantData
		err := rows.Scan(&p.ID, &p.SRSType, &p.CrewCount, &p.SelectedSRSValue)
		if err != nil {
			return nil, err
		}
		data = append(data, p)
	}

	fmt.Printf("Found %d participant records\n", len(data))
	if len(data) > 0 {
		fmt.Printf("Sample data - ID: %d, SRS Type: '%s', Crew Count: %d, Selected SRS: %f\n",
			data[0].ID, data[0].SRSType, data[0].CrewCount, data[0].SelectedSRSValue)
	}

	return data, nil
}

func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = destFile.ReadFrom(sourceFile)
	return err
}
