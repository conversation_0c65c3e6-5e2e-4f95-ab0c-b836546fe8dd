package main

import (
	"fmt"
	"log"
	"os"

	"github.com/rbjoregren/segling/pkg/database"
	"github.com/rbjoregren/segling/pkg/models"
	_ "modernc.org/sqlite"
)

func main() {
	// Copy the test database to a temporary location for testing
	srcDB := "tmp/segling.db"
	testDB := "test_dns_dnf_fix.db"

	// Remove test database if it exists
	os.Remove(testDB)

	// Copy the source database
	err := copyFile(srcDB, testDB)
	if err != nil {
		log.Fatalf("Failed to copy database: %v", err)
	}
	defer os.Remove(testDB) // Clean up after test

	fmt.Println("Testing DNS/DNF point calculation fix...")

	// Open database
	db, err := database.New(testDB)
	if err != nil {
		log.Fatalf("Failed to open database: %v", err)
	}
	defer db.Close()

	// Run migration to ensure latest schema
	err = db.MigrateDatabase()
	if err != nil {
		log.Fatalf("Migration failed: %v", err)
	}

	// Test scenario: Create a simple event with DNS and DNF participants
	fmt.Println("\n=== Testing DNS/DNF Point Calculation ===")

	// Get an existing event to test with
	events, err := db.GetEvents()
	if err != nil || len(events) == 0 {
		log.Fatalf("No events found in test database")
	}

	event := events[0]
	fmt.Printf("Testing with event: %s (ID: %d)\n", event.Namn, event.ID)

	// Get participants for this event
	participants, err := db.GetEventParticipants(event.ID)
	if err != nil {
		log.Fatalf("Failed to get participants: %v", err)
	}

	if len(participants) < 3 {
		fmt.Printf("⚠️  Event has only %d participants, need at least 3 for meaningful test\n", len(participants))
		return
	}

	fmt.Printf("Event has %d participants\n", len(participants))

	// Test with heats if available
	heats, err := db.GetHeats(event.ID)
	if err != nil {
		log.Fatalf("Failed to get heats: %v", err)
	}

	if len(heats) == 0 {
		fmt.Println("No heats found - testing with single event results")
		testSingleEventResults(db, event, participants)
	} else {
		fmt.Printf("Found %d heats - testing heat results\n", len(heats))
		testHeatResults(db, event, heats[0], participants)
	}
}

func testSingleEventResults(db *database.DB, event models.Event, participants []models.EventParticipant) {
	// For single event, we would test the main results calculation
	// This is more complex as it involves the main results logic
	fmt.Println("Single event DNS/DNF testing not implemented in this test")
}

func testHeatResults(db *database.DB, event models.Event, heat models.Heat, participants []models.EventParticipant) {
	fmt.Printf("Testing heat %d results\n", heat.HeatNumber)

	// Get heat results
	results, err := db.GetHeatResultsComplete(heat.ID)
	if err != nil {
		log.Fatalf("Failed to get heat results: %v", err)
	}

	fmt.Printf("Heat has %d results\n", len(results))

	// Count different types of results
	var finishedCount, dnfCount, dnsCount int
	var dnfPoints, dnsPoints []float64

	for _, result := range results {
		if result.DNS {
			dnsCount++
			dnsPoints = append(dnsPoints, result.Points)
		} else if result.DNF {
			dnfCount++
			dnfPoints = append(dnfPoints, result.Points)
		} else {
			finishedCount++
		}
	}

	totalParticipants := finishedCount + dnfCount + dnsCount
	expectedPenaltyPoints := float64(totalParticipants + 1)

	fmt.Printf("\nResults breakdown:\n")
	fmt.Printf("- Finished boats: %d\n", finishedCount)
	fmt.Printf("- DNF boats: %d\n", dnfCount)
	fmt.Printf("- DNS boats: %d\n", dnsCount)
	fmt.Printf("- Total participants: %d\n", totalParticipants)
	fmt.Printf("- Expected penalty points: %.1f\n", expectedPenaltyPoints)

	// Check DNF points
	allDNFCorrect := true
	for i, points := range dnfPoints {
		if points != expectedPenaltyPoints {
			fmt.Printf("❌ DNF boat %d has incorrect points: %.1f (expected %.1f)\n", i+1, points, expectedPenaltyPoints)
			allDNFCorrect = false
		}
	}

	// Check DNS points
	allDNSCorrect := true
	for i, points := range dnsPoints {
		if points != expectedPenaltyPoints {
			fmt.Printf("❌ DNS boat %d has incorrect points: %.1f (expected %.1f)\n", i+1, points, expectedPenaltyPoints)
			allDNSCorrect = false
		}
	}

	// Check that DNS and DNF get the same points
	samePoints := true
	if len(dnfPoints) > 0 && len(dnsPoints) > 0 {
		for _, dnfPts := range dnfPoints {
			for _, dnsPts := range dnsPoints {
				if dnfPts != dnsPts {
					fmt.Printf("❌ DNS and DNF boats have different points: DNF=%.1f, DNS=%.1f\n", dnfPts, dnsPts)
					samePoints = false
				}
			}
		}
	}

	// Summary
	fmt.Printf("\n=== TEST RESULTS ===\n")
	if allDNFCorrect && allDNSCorrect && samePoints {
		fmt.Printf("✅ SUCCESS: DNS/DNF point calculation is correct!\n")
		fmt.Printf("   - All DNF boats receive %.1f points\n", expectedPenaltyPoints)
		fmt.Printf("   - All DNS boats receive %.1f points\n", expectedPenaltyPoints)
		fmt.Printf("   - DNS and DNF boats receive the same penalty points\n")
		fmt.Printf("   - Follows Racing Rules of Sailing A4.2\n")
	} else {
		fmt.Printf("❌ FAIL: DNS/DNF point calculation has issues\n")
		if !allDNFCorrect {
			fmt.Printf("   - DNF points are incorrect\n")
		}
		if !allDNSCorrect {
			fmt.Printf("   - DNS points are incorrect\n")
		}
		if !samePoints {
			fmt.Printf("   - DNS and DNF don't receive the same points\n")
		}
	}
}

func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = destFile.ReadFrom(sourceFile)
	return err
}
