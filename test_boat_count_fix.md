# Test Results for Participant Count Fix

## Issue Description
The main page was showing incorrect participant counts. The user wanted:
- **Boats**: Number of boats (participant records) - this was correct
- **Participants**: Total number of people (skipper + crew), like in the results pages - this was wrong

## Problem Identified
The `CountEventParticipants` function was counting participant records instead of total people (skipper + crew).

## Fix Applied
Updated the `CountEventParticipants` function in `pkg/database/database.go` to:
1. Check if `crew_count` column exists
2. If yes: Sum `crew_count + 1` (skipper) for each participant
3. If no: Count participants (1 person per participant for backward compatibility)

```sql
-- New query when crew_count exists:
SELECT COALESCE(SUM(COALESCE(crew_count, 0) + 1), 0)
FROM event_participants WHERE event_id = ?

-- Fallback query for older schema:
SELECT COUNT(*) FROM event_participants WHERE event_id = ?
```

## Test Results
After applying the fix, the debug logs show the correct counting:

| Event ID | Total People | Boats | Notes |
|----------|-------------|-------|-------|
| 34       | 16          | 9     | 16 people across 9 boats (some boats have crew) |
| 30       | 11          | 7     | 11 people across 7 boats (some boats have crew) |
| 32       | 12          | 7     | 12 people across 7 boats (some boats have crew) |
| 31       | 7           | 7     | 7 people across 7 boats (single-handed sailing) |

## Verification
The fix now correctly shows:
- **Participants**: Total number of people (skipper + crew), matching results page calculation
- **Boats**: Number of boats (participant records)

This matches the user's requirement and provides consistent counting with the results pages.

## Status
✅ **FIXED** - Participant count now shows total people (skipper + crew) as requested.
