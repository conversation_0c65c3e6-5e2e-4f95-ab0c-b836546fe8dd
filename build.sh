#!/bin/bash
# Build script for the Segling application
# This script compiles the application and packages all necessary files
# It uses the latest git tag for versioning (or falls back to a default version)

set -e  # Exit on error

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Define variables
APP_NAME="segling"
# Get the latest git tag for version, fallback to default if not available
DEFAULT_VERSION="1.9.1"  # Updated to match current version in README
# First try to get tag+hash if we're not exactly on a tag
VERSION=$(git describe --tags 2>/dev/null || echo "")
if [ -z "$VERSION" ]; then
    # If that fails, try to get just the latest tag
    VERSION=$(git describe --tags --abbrev=0 2>/dev/null || echo "${DEFAULT_VERSION}")
fi
echo -e "${YELLOW}Using version: ${VERSION}${NC}"
BUILD_DIR="build"
DIST_DIR="dist"
PACKAGE_NAME="${APP_NAME}-${VERSION}"
PACKAGE_DIR="${DIST_DIR}/${PACKAGE_NAME}"

# Print header
echo -e "${GREEN}=== Building Segling Application v${VERSION} ===${NC}"

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo -e "${RED}Error: Go is not installed. Please install Go to build this application.${NC}"
    exit 1
fi

# Create build and dist directories
echo -e "${YELLOW}Creating build directories...${NC}"
mkdir -p "${BUILD_DIR}"
mkdir -p "${PACKAGE_DIR}"
mkdir -p "${PACKAGE_DIR}/static"
mkdir -p "${PACKAGE_DIR}/pkg/templates"
mkdir -p "${PACKAGE_DIR}/backups"
mkdir -p "${PACKAGE_DIR}/certs"
mkdir -p "${PACKAGE_DIR}/certs/export"
mkdir -p "${PACKAGE_DIR}/certs/client"
mkdir -p "${PACKAGE_DIR}/credentials"
mkdir -p "${PACKAGE_DIR}/scripts"
mkdir -p "${PACKAGE_DIR}/docs"

# Build the application
echo -e "${YELLOW}Building application...${NC}"
go build -ldflags="-X main.Version=${VERSION}" -o "${BUILD_DIR}/${APP_NAME}" ./cmd/server

# Copy the executable
echo -e "${YELLOW}Copying executable...${NC}"
cp "${BUILD_DIR}/${APP_NAME}" "${PACKAGE_DIR}/"

# Copy static files
echo -e "${YELLOW}Copying static files...${NC}"
cp -r static/* "${PACKAGE_DIR}/static/"

# Copy templates
echo -e "${YELLOW}Copying templates...${NC}"
cp -r pkg/templates/* "${PACKAGE_DIR}/pkg/templates/"

# Create an empty database file
echo -e "${YELLOW}Creating initial database...${NC}"
touch "${PACKAGE_DIR}/segling.db"
echo -e "${YELLOW}Created empty database file${NC}"
echo -e "${YELLOW}Note: The database will be initialized on first run${NC}"

# Copy scripts
if [ -d "scripts" ]; then
    echo -e "${YELLOW}Copying scripts...${NC}"
    cp scripts/*.sh "${PACKAGE_DIR}/scripts/" 2>/dev/null || true
    cp scripts/*.go "${PACKAGE_DIR}/scripts/" 2>/dev/null || true
    # Make scripts executable
    chmod +x "${PACKAGE_DIR}/scripts/"*.sh 2>/dev/null || true
fi

# Copy documentation
echo -e "${YELLOW}Copying documentation...${NC}"
# Copy release notes to both root and docs directory for backward compatibility
cp RELEASE_NOTES.md "${PACKAGE_DIR}/" 2>/dev/null || true
cp RELEASE_NOTES.md "${PACKAGE_DIR}/docs/" 2>/dev/null || true
# Copy README
cp README.md "${PACKAGE_DIR}/" 2>/dev/null || true
cp README.md "${PACKAGE_DIR}/docs/" 2>/dev/null || true
# Copy additional documentation if it exists
if [ -d "docs" ]; then
    cp docs/*.md "${PACKAGE_DIR}/docs/" 2>/dev/null || true
fi
# Ensure ENTYPSEGLING.md is copied if it exists
if [ -f "docs/ENTYPSEGLING.md" ]; then
    cp docs/ENTYPSEGLING.md "${PACKAGE_DIR}/docs/" 2>/dev/null || true
fi

# Create a README file
echo -e "${YELLOW}Creating README file...${NC}"
cat > "${PACKAGE_DIR}/README.txt" << EOF
Segling Application v${VERSION}
==============================

This is a sailing competition management application.

Quick Start:
1. Ensure you have the necessary permissions to run the executable
   chmod +x segling
2. Run the application
   ./segling
3. Open your browser and navigate to:
   http://localhost:8080

Enabling HTTPS (Secure Connection):
1. Generate self-signed certificates (for development)
   ./scripts/generate_certs.sh
2. Run the application with TLS enabled
   ./segling -tls -cert certs/cert.pem -key certs/key.pem
3. Open your browser and navigate to:
   https://localhost:8080
   (Note: Browsers will show a warning about self-signed certificates)

Importing Certificates to Browser (to avoid warnings):
1. Export the certificate in browser-friendly formats
   ./scripts/export_cert_for_browser.sh
2. Follow the instructions provided by the script to import
   the certificate into your specific browser

Enabling Mutual TLS (Client Certificate Authentication):
1. Generate client certificates
   ./scripts/generate_client_certs.sh
2. Run the application with client certificate verification
   ./segling -tls -cert certs/cert.pem -key certs/key.pem -client-ca certs/client/ca.crt -require-client-cert
3. Import the client certificate to your browser
   (Password for the client certificate: segling)

Directory Structure:
- segling: The main executable
- pkg/templates/: HTML templates
- static/: CSS, JavaScript, and other static assets
- backups/: Directory for database backups
- certs/: Directory for TLS certificates
  - certs/export/: Exported certificates for browser import
  - certs/client/: Client certificates for mutual TLS
- credentials/: Directory for sensitive credentials (not included in backups)
- scripts/: Utility scripts
- docs/: Documentation
- segling.db: SQLite database file

Notes:
- The application will create and initialize the database on first run
- Backups will be stored in the backups/ directory
- The application runs on port 8080 by default
- Default club for new sailors can be configured in Settings
- Dark/light mode toggle is available in the top navigation bar

For more information, visit: https://github.com/rogge66/sailapp
EOF

# Create a simple start script
echo -e "${YELLOW}Creating start scripts...${NC}"
cat > "${PACKAGE_DIR}/start.sh" << EOF
#!/bin/bash
# Start script for Segling application

# Make sure the executable has the right permissions
chmod +x segling

# Run the application
./segling

# If the application exits, wait for user input before closing the terminal
echo "Application has stopped. Press Enter to exit."
read
EOF

# Create a secure start script
cat > "${PACKAGE_DIR}/start-secure.sh" << EOF
#!/bin/bash
# Start script for Segling application with HTTPS enabled

# Check if certificates exist
if [ ! -f "certs/cert.pem" ] || [ ! -f "certs/key.pem" ]; then
    echo "TLS certificates not found. Generating self-signed certificates..."
    ./scripts/generate_certs.sh
fi

# Make sure the executable has the right permissions
chmod +x segling

# Run the application with TLS enabled
./segling -tls -cert certs/cert.pem -key certs/key.pem

# If the application exits, wait for user input before closing the terminal
echo "Application has stopped. Press Enter to exit."
read
EOF

# Make the start scripts executable
chmod +x "${PACKAGE_DIR}/start.sh"
chmod +x "${PACKAGE_DIR}/start-secure.sh"

# Create a zip archive
echo -e "${YELLOW}Creating zip archive...${NC}"
cd "${DIST_DIR}"
zip -r "${PACKAGE_NAME}.zip" "${PACKAGE_NAME}"
cd ..

# Clean up
echo -e "${YELLOW}Cleaning up...${NC}"
rm -rf "${BUILD_DIR}"

# Create latest directory
echo -e "${YELLOW}Creating latest distribution directory...${NC}"
mkdir -p "${DIST_DIR}/latest"

# Detect platform for handling symlinks vs copies
if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
    # Windows - copy files
    echo -e "${YELLOW}Windows detected, copying files to latest directory...${NC}"
    rm -rf "${DIST_DIR}/latest/"*
    cp -r "${PACKAGE_DIR}/"* "${DIST_DIR}/latest/"
    cp "${DIST_DIR}/${PACKAGE_NAME}.zip" "${DIST_DIR}/latest/${APP_NAME}.zip"
    echo -e "${YELLOW}Files copied to ${DIST_DIR}/latest/${NC}"
else
    # Unix-like systems (macOS, Linux) - use symbolic links
    echo -e "${YELLOW}Unix-like system detected, creating symbolic links...${NC}"
    # Remove existing links/files in latest directory
    rm -rf "${DIST_DIR}/latest/"*

    # Create symbolic links to the latest build
    # First, create a symbolic link to the directory itself
    ln -sf "../${PACKAGE_NAME}" "${DIST_DIR}/latest/${APP_NAME}"
    # Then create a symbolic link to the zip file
    ln -sf "../${PACKAGE_NAME}.zip" "${DIST_DIR}/latest/${APP_NAME}.zip"
    echo -e "${YELLOW}Symbolic links created in ${DIST_DIR}/latest/${NC}"
fi

# Print success message
echo -e "${GREEN}Build completed successfully!${NC}"
echo -e "${GREEN}Package created at: ${DIST_DIR}/${PACKAGE_NAME}.zip${NC}"
echo -e "${GREEN}Latest version available at: ${DIST_DIR}/latest/${NC}"
echo -e "${GREEN}You can also run the application directly from: ${PACKAGE_DIR}/segling${NC}"
