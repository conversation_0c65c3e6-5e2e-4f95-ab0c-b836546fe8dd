- web server written in golang
- web ui written in bootstrap/htmx/google template
- database for sailers, Namn, Telefon
- data base for boats having Namn, Båttype and SRS data. It should be able to get the SRS data from https://matbrev.svensksegling.se/Home/BoatList where b<PERSON><PERSON><PERSON> is first column.
- creating sailing event with a set of sailors and boats
- main page in ui with menus to add sailors and boats
- Hämta SRS data från https://matbrev.svensksegling.se/Home/ApprovedList if boat i registered with "mätbrevs nummer", first column in http page.
- Use S/H from SRS data. which stands for short hand
- Searching on "mätbrevs nummer" search in option menu and make it filter on both on mätbrevnummer and Ägare
- Setting for automatic backup of database
- Setting for default för klubb (tex LSS), used when
- All templates shall have dark/light mode styles
- Export result to csv
- Statistics page
- L<PERSON>s tävling och spara all data i resultat i en resultat tabell. Låst tävling ska inte gå att ändra.