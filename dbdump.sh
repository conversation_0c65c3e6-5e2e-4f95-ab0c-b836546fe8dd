#!/bin/bash

# <PERSON><PERSON>t to dump the database in a formatted text format

# Change to the script directory
cd "$(dirname "$0")"

# Display usage information
usage() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -t, --table TABLE    Dump only the specified table"
    echo "  -o, --output FILE    Write output to FILE instead of stdout"
    echo "  -h, --help           Display this help message"
    exit 1
}

# Parse command-line arguments
TABLE=""
OUTPUT=""

while [[ $# -gt 0 ]]; do
    case "$1" in
        -t|--table)
            TABLE="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT="$2"
            shift 2
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo "Unknown option: $1"
            usage
            ;;
    esac
done

# Build the command
CMD="go run cmd/dbdump/main.go"
if [ -n "$TABLE" ]; then
    CMD="$CMD -table=$TABLE"
fi
if [ -n "$OUTPUT" ]; then
    CMD="$CMD -output=$OUTPUT"
fi

# Run the command
eval $CMD
