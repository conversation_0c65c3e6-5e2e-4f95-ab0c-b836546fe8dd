markdown
# Product Requirements Document: Segling Application

## 1. Introduction

### 1.1 Purpose
The Segling application is designed to manage sailing competitions, including boats, sailors, and race results. It provides functionality for organizing events, calculating race times, and maintaining a database of boats and sailors.

### 1.2 Scope
This document outlines the requirements for the Segling application, including user interface, functionality, result publishing via GitHub Pages, and technical specifications.

### 1.3 Glossary of Swedish Terms

- `SRS`: Swedish Rating System (handicap system for sailing boats)
- `Matbrev`: Measurement certificate for a boat
- `Battype`: Boat type
- `Jaktstart`: Pursuit start racing format
- `Tavling`: Competition/Event
- `Mätbrevsnummer`: Measurement certificate number
- `Segelnr`: Sail number
- `Nat`: Nationality
- `Klubb`: Club
- `Deltävling`: Heat/Sub-competition
- `DNF`: Did Not Finish — Status for boats that started but did not complete the race
- `DNS`: Did Not Start — Status for boats that were registered but did not start the race
- `GitHub Pages`: A static site hosting service that publishes HTML, CSS, and JavaScript files directly from a GitHub repository
- `Entypsegling`: One-design sailing competition where only placement at finish line matters (no SRS handicap or finish times)

## 2. User Interface Requirements

### 2.1 General UI

- Implement dark/light mode toggle with proper styling for headers in dark mode
- Use consistent icons across menu items
- Improve menu organization
- Use 24-hour time format instead of 12-hour format
- Ensure Safari compatibility
- Confirmation dialogs should follow the dark theme

### 2.2 Main Page

- Make the `competitions` section a scrollable list

### 2.3 Search Functionality

- Enhance search with live filtering on `measurement_certificate_number` and `owner` fields in boats and sailors sections
- Disable browser autocomplete in search fields
- Add descriptive text to boat forms
- Allow `measurement_certificate_number` search to match any part of the number, `boat_name`, or `owner` information

## 3. Boat and Sailor Management

### 3.1 Boat Management

- When selecting a `boat_type`, clear the `measurement_certificate_number` field
- Add `sail_number` and `nationality` fields to `boat` data with `SWE` as default
- When editing a `boat`, populate `boat_type` and `boat_name` fields from `measurement_certificate` data automatically

### 3.2 Sailor Management

- Add setting for default `club` for sailors with `LSS` as default instead of hardcoding it

## 4. SRS Data Management

### 4.1 Terminology

- Replace `approved_boats` with `matbrev` terminology
- Replace `boat_type/båttype` with `srs_tabell/SRS tabell` throughout
- Use `Med undanvind` instead of `Standard`, `S/H med undanvind` instead of `S/H` when displaying SRS types

### 4.2 Data Synchronization

- Add menu to fetch and store SRS data tables from Svensk Segling for offline access and searching
- Change 'Synkronisera SRS Data' button text to 'Uppdatera SRS Data från `${SRS_DATA_URL}`'
- Add settings for configurable URLs to fetch `measurement_certificate` and `SRS_tabell` data, e.g., `${SRS_DATA_URL}` and `${MATBREV_DATA_URL}`, with current values as defaults in the configuration
- Only fetch `measurement_certificate` data when format matches B1234 or E1234 pattern
- Update boats when SRS and `measurement_certificate` data changes, but preserve locked results and start boat information
- Add settings for configurable URLs to fetch `measurement_certificate` and `SRS_tabell` data, with current values as defaults

### 4.3 Import/Export

- Add export/import functionality for `measurement_certificate` and SRS-data from CSV files with sanity checks
- Combine Export/Import SRS Table and Export/Import Measurement Certificate into a single function with a dropdown selector
- Remove the alert message 'När SRS-data importeras kommer även alla båtar i databasen att uppdateras automatiskt med de nya SRS-värdena. Detta påverkar inte låsta tävlingsresultat.' from all sections

### 4.4 UI Layout

- Arrange SRS data boxes with 'SRS Data Status' on the left side and other elements on the right side
- Update help text about `measurement_certificate_number` as the system no longer fetches data from Svensk Seglings website if not found locally

## 5. Competition Management

### 5.1 Competition Menus

- Create competition menus with: Create competition, Competitions, Pursuit start times, Finish times, and Results
- Add year filter for events with current year as default
- Improve UI labels and button placement
- Add competition type (`competition_type`) setting with `Kvällssegling` and `Regatta` as options
- Use `competition_type` when creating events and organize GitHub Pages results by year/competition-type structure
- When creating a new event, default the event name to `<competition_type> <date>`

### 5.2 Competition Creation and Editing

- Create database backup when new events are created
- Redirect to edit page after saving
- Split 'edit competition' into separate 'edit competition' and 'edit participants' pages

### 5.3 Competition Features

- Add functionality to copy boats between competitions without duplicates
- Show participant count for each competition
- Implement crew count functionality
- Implement competition locking to preserve historical results by copying data rather than preventing updates
- Add confirmation dialog when unlocking an event

### 5.4 Heat/Sub-competition Management

- Implement heat (`sub-competition`) functionality for competitions
- Each competition (`event`) shall have at least one heat (default to 1)
- Each heat shall have its own set of finish times
- Display the heat number when entering finish times
- Allow selecting which heat to view/edit when managing finish times

### 5.5 Finish Times

- When entering finish times, allow skipping seconds and automatically store as '00'
- On the finish times page, allow selecting the heat (`sub-competition`) once for all boats instead of individually per boat
- Each heat shall have its own set of finish times
- When selecting a heat in finish times, update all boats' finish times UI to show only times for that heat
- Display '--:--:--' for boats without finish times in the selected heat
- Add optional `DNF` (Did Not Finish) or `DNS` (Did Not Start) flags for boats in finish times
- When `DNF` is checked, disable and clear the finish time input
- When `DNS` is checked, disable and clear the finish time input and uncheck `DNF` if it's checked
- When a finish time is entered, automatically uncheck both `DNF` and `DNS` flags

### 5.6 Entypsegling (One-Design) Competition Format

#### 5.6.1 Competition Format Setting

- Add `Entypsegling` checkbox/setting when creating events to enable placement-only competition format
- This setting can be applied to any competition type (`Kvällssegling`, `Regatta`, etc.)
- When enabled, the event uses placement-based results instead of time-based calculations
- Store entypsegling flag in event data to determine result calculation method

#### 5.6.2 Entypsegling Event Creation

- When `Entypsegling` is enabled, add a `Boat Type` text input field for the event
- This field is for informational purposes only (e.g., "Laser", "Optimist", "420")
- The boat type is not restricted to SRS database boats since all competitors use the same type
- Store boat type as event-level information, not participant-level

#### 5.6.3 Entypsegling Participant Management

- Participants in entypsegling events are **not connected to individual boats** from the boat database
- Instead, participants have **personal numbers** (sail numbers) within the competition
- Add `Personal Number` text field when adding participants to entypsegling events (e.g., "SWE 123", "FIN 456")
- Personal numbers are free-text input and not validated against any database
- Participants are still connected to sailors from the sailor database for name/club information
- Remove boat selection interface for entypsegling participant forms
- Display personal numbers instead of boat information in participant lists and results

#### 5.6.4 Entypsegling Finish Times Interface

- Create specialized finish times page for entypsegling events that only requires placement input
- Replace finish time input fields with placement number input (1st, 2nd, 3rd, etc.)
- Maintain heat selection functionality for multi-heat entypsegling events
- Keep `DNF` and `DNS` status options for participants that don't finish or start
- Validate that placement numbers are unique within each heat (no duplicate placements)
- Allow gaps in placement numbers to accommodate `DNF`/`DNS` participants
- Display participants by personal number instead of boat information

#### 5.6.5 Entypsegling Results Display

- Results pages for entypsegling events show placement-based results without times
- **Single Heat Entypsegling**: Display results table with columns:
  - Position (final placement)
  - Sailor
  - Personal Number (instead of boat information)
  - Crew count
  - Status (`DNF`/`DNS` badges if applicable)
- **Multi-Heat Entypsegling**: Display aggregated results with columns:
  - Overall position
  - Sailor
  - Personal Number (instead of boat information)
  - Crew count
  - Total points
  - One column per heat showing individual heat placements
- **Event Boat Type**: Display the event's boat type prominently at the top of results (e.g., "Boat Type: Laser")
- **No Time Columns**: Remove all time-related columns (start time, finish time, elapsed time, corrected time, time to previous, time to winner)
- **No SRS Values**: Remove SRS-related columns and calculations since handicap doesn't apply
- **Sorting**: Results sorted by placement (single heat) or total points (multi-heat)
- **Points System**: Same as regular multi-heat events (1st = 1 point, 2nd = 2 points, etc.)
- **Tied Finishes**: Entypsegling events support tied finishes using the same algorithm as regular sailing events:
  - When multiple boats are assigned the same placement, they receive the average points for those positions
  - Points are displayed with one decimal precision (e.g., 1.5p for a 2-way tie for 1st place)
  - Tied finishes are clearly indicated in results with identical placement numbers

#### 5.6.6 Entypsegling Export and Print

- All export formats (CSV, Google Drive, GitHub Pages) support entypsegling events
- Exported data excludes time and SRS columns for entypsegling events
- Exported data includes personal numbers instead of boat information
- Include event boat type in exported data headers/metadata
- Print views optimized for placement-only data without time information
- File naming follows same conventions as regular events with heat-specific naming
- GitHub Pages templates automatically detect entypsegling format and render appropriate layout

#### 5.6.7 Backward Compatibility

- Existing events remain unchanged and continue to use time-based calculations
- Entypsegling flag determines which interface and calculation method to use
- Database schema supports both time-based and placement-based data
- All existing functionality (heats, participants, exports) works with entypsegling events
- Competition types (`Kvällssegling`, `Regatta`) remain separate from entypsegling format setting

## 6. Participant Management and SRS Values

### 6.1 SRS Value Selection

- Add option to select which SRS value to use (from four boat values or custom override)
- Show boat's SRS value in readonly field when boat and SRS type are selected, with separate editable field for custom values
- **Note**: SRS values are not applicable for entypsegling events since no handicap calculations are performed

### 6.2 Participant Editing

- Make participants and their SRS values editable after adding them to a competition
- Add `crew_count` field to participant forms for consistency

## 7. Pursuit Start (Jaktstart) and Results

### 7.1 Pursuit Start Configuration

- Implement Pursuit Start (`jaktstart`) with editable `start_time`, `wind`, and `course_length` fields
- Use wind speed (m/s) and course length (nautical miles) parameters for calculating pursuit start times
- Calculate start times using lowest SRS value as reference boat, truncating to minutes
- **Note**: Pursuit start is not applicable for entypsegling events since all boats start simultaneously

### 7.2 Results Display

- Create printer-friendly versions for pursuit start times and starter boat information
- Remove the text 'kan resultaten behöva korrigeras' from the pursuit start help section
- Display sail number and nationality as 'NAT-SAIL_NUMBER' (e.g., SWE-1234) in results tables
- Show time to previous boat and time to winner using corrected time in results
- Use `Crew` instead of `Persons` and add crew count summary
- Add CSV export for competition results
- Include `measurement_certificate_number` in result tables

### 7.3 Heat Results and Single vs Multi-Heat Display

**Note**: The following sections apply to time-based sailing events. For entypsegling events, see section 5.6.3 for placement-based results display.

#### 7.3.1 Single Heat Events (1 Heat)

When an event has only one heat, the results page displays traditional sailing results:

- **Table Layout**: Standard results table with columns for:
  - Position
  - Sailor
  - Boat
  - SRS Value
  - Crew count
  - Start time
  - Finish time
  - Elapsed time
  - Corrected time
  - Time to previous
  - Time to winner
- **Sorting**: Results sorted by corrected time (ascending - fastest first)
- **Data Source**: Uses heat finish times from the `heat_finish_times` table
- **Display Logic**: Shows results exactly as the single heat, maintaining backward compatibility
- **Header Text**: "Sorterat efter korrigerad tid" (Sorted by corrected time)

#### 7.3.2 Multi-Heat Events (2+ Heats)

When an event has multiple heats, the results page displays aggregated results:

- **Table Layout**: Multi-heat results table with columns for:
  - Overall position
  - Sailor
  - Boat
  - SRS Value
  - Crew count
  - Total points
  - One column per heat showing individual heat results
- **Sorting**: Results sorted by total points (ascending - lowest points wins)
- **Points System**: Standard sailing points system:
  - 1st place = 1 point
  - 2nd place = 2 points
  - 3rd place = 3 points, etc.
  - `DNS`/`DNF` = number of participants + 1 points
- **Tied Finish in a Race (Same Placement)**: When two or more boats finish a race in a tie for the same position, they are each awarded the average of the points for those positions:
  - **Formula**: Points = (sum of the places they occupy) ÷ (number of boats tied)
  - **Examples**:
    - 2-way tie for 1st place: (1 + 2) ÷ 2 = 1.5 points each
    - 3-way tie for 2nd place: (2 + 3 + 4) ÷ 3 = 3.0 points each
    - 2-way tie for 4th place: (4 + 5) ÷ 2 = 4.5 points each
  - **Display**: Points are displayed with one decimal precision (e.g., 1.5p, 2.5p, 3.0p)
  - **Position Advancement**: After a tie, the next available position is used (e.g., after a 3-way tie for 2nd, the next boat gets 5th place)
- **Total Ties with Split Positions**: When boats have tied finishes that result in split points, they can end up with identical total points:
  - **Example**: Two boats both tied for 1st place in Heat 1 (1.5p each) and tied for 2nd place in Heat 2 (2.5p each)
  - **Result**: Both boats have 4.0 total points (1.5 + 2.5) with identical individual race results
  - **Outcome**: These boats share the same overall position since they are completely tied
- **Tiebreaker Rules**: When boats have equal total points, ties are broken following Racing Rules of Sailing A8.1 and A8.2:
  1. **Best individual race finishes (RRS A8.1)**: Compare tied boats' race scores listed from best to worst
     - At the first point where there is a difference, the tie is broken in favor of the boat(s) with the best score(s)
     - Example: Boat A [1,2,3] beats Boat B [1,3,2] because A has better second-best score (2 vs 3)
  2. **Most recent race result (RRS A8.2)**: If tie remains after A8.1, use scores from the last race
     - The boat with the better result in the last race is ranked higher
     - If still tied, use next-to-last race, and so on until all ties are broken
  3. **Complete tie (shared position)**: If boats have identical total points AND identical individual race results
     - Boats share the same overall position (e.g., both get 2nd place)
     - Next position is advanced by the number of tied boats (e.g., after two boats tied for 2nd, next boat gets 4th place)
     - This occurs when A8.1 and A8.2 cannot break the tie due to identical race results
  4. **Consistent ordering**: Final tiebreaker uses participant ID to ensure deterministic results across all platforms
- **Heat Columns**: Each heat column shows:
  - Position and points for boats that finished (e.g., "1 (1p)")
  - `DNS` badge for Did Not Start
  - `DNF` badge for Did Not Finish
  - "-" for boats that didn't participate in that heat
- **Header Text**: "Sammanlagda resultat från X deltävlingar (sorterat efter totala poäng)" (Combined results from X heats, sorted by total points)

#### 7.3.3 Automatic Detection

- **System Logic**: The application automatically detects single vs multi-heat events by counting heats
- **Heat Count**:
  - 1 heat = Single heat display
  - 2+ heats = Multi-heat display
- **Default Heat**: Every event has at least one heat (created automatically if none exists)

#### 7.3.4 Data Consistency

- **Heat-Based Data**: Both single and multi-heat events use the `heat_finish_times` table for consistency
- **Backward Compatibility**: Single heat events display exactly as before from user perspective
- **Template Logic**: Conditional rendering in results.html based on `isMultiHeat` flag

### 7.4 `DNF` and `DNS` Handling

- Add `DNF` (Did Not Finish) and `DNS` (Did Not Start) flags for boats in finish times
- When `DNF` is checked, disable finish time input and clear any existing finish time
- When `DNS` is checked, disable finish time input, clear any existing finish time, and uncheck `DNF`
- In results display, show `DNF` and `DNS` status with appropriate badges instead of finish times
- Don't show 'time after winner' for `DNF` and `DNS` entries
- Don't display start time for `DNS` entries
- Sort `DNF` entries after boats that finished, and `DNS` entries after `DNF` entries

### 7.5 Print Views

- Optimize print views with abbreviated headers, condensed information, and smaller fonts
- In result print page, make table stripes darker than rgba(0, 0, 0, 0.1) for better contrast, but not as dark as rgba(0, 0, 0, 0.25)

### 7.6 GitHub Pages Integration

- Add a publish button to the results page that creates and publishes results to GitHub Pages
- Add settings for GitHub Pages configuration in the settings page:
  - Enable/disable GitHub Pages integration
  - GitHub repository URL (in format username/repo)
  - GitHub branch (typically gh-pages)
  - GitHub personal access token with repo permissions
  - Template selection for published pages
- Display a link to the GitHub Pages site in the settings when configured
- Show a permanent link to the published page after successful publication
- Implement cache control headers and cache busting to prevent browser caching of published pages
- Generate an index page that lists all published events with the latest event highlighted
- Group GitHub Pages published results by year, with index.html linking to year directories
- Each year directory contains an index with links to competition type directories
- Each competition type directory contains an index with links to results
- Use responsive design for published pages to ensure proper display on all devices
- Ensure proper rendering of Swedish characters (å, ä, ö) in published pages
- Use landscape orientation for optimal table display

### 7.7 Google Drive Export

- Add functionality to export competition results to Google Drive workspace
- Export results as spreadsheet files (Google Sheets format) to a designated Google Drive folder
- Include all result data: boat information, sailor details, times, and calculated results
- Support exporting individual competitions or multiple competitions at once
- Add settings for Google Drive configuration in the settings page:
  - Enable/disable Google Drive integration
  - Import client_secrets.json configuration file for Google Drive API authentication
  - Select target folder in Google Drive for exported results
  - Configure export file naming convention
- Provide status feedback during export process
- Handle authentication flow for Google Drive API access
- Support re-authentication when tokens expire

### 7.8 Heat-Specific Export and Print Naming

- All export and print functions support heat selection for multi-heat events
- File names and page titles include heat information to clearly identify content:

#### 7.8.1 Multi-Heat Events

- **Overall results**: Include "totala" in filename/title
  - CSV: `results_EventName_2024-07-15_totala.csv`
  - Google Drive: `EventName_2024-07-15_totala`
  - GitHub Pages: `2024-07-15-eventname-totala.html`
  - Print page title: `Results - EventName - Overall results`
- **Individual heat results**: Include heat name in filename/title
  - CSV: `results_EventName_2024-07-15_Heat_1.csv`
  - Google Drive: `EventName_2024-07-15_Heat_1`
  - GitHub Pages: `2024-07-15-eventname-heat-1.html`
  - Print page title: `Results - EventName - Heat 1`

#### 7.8.2 Single-Heat Events

- **Standard naming**: No heat suffix needed since there's only one heat
  - CSV: `results_EventName_2024-07-15.csv`
  - Google Drive: `EventName_2024-07-15`
  - GitHub Pages: `2024-07-15-eventname.html`
  - Print page title: `Results - EventName`

#### 7.8.3 Export Content Consistency

- **Print pages**: Show heat selection buttons for multi-heat events
- **CSV exports**: Include heat parameter in export URLs
- **Google Drive exports**: Include heat parameter in export requests
- **GitHub Pages**: Support heat parameter for publishing specific heat results
- **Data consistency**: All export formats show identical data for the same heat selection
- **File safety**: Heat names are cleaned for safe filename usage (spaces → underscores, remove special characters)

## 8. Statistics and Reporting

- Add statistics page showing `participating_boats` and total crew numbers

## 9. Technical Requirements

### 9.1 Database

- Split database into separate files for `measurement_certificate`/SRS data and everything else
- Use SQLite implementation without CGO dependencies to avoid C compiler requirements
- Database schema must support both time-based and placement-based competition data for entypsegling events
- Add entypsegling flag/field to events table to determine result calculation method
- Add boat_type field to events table for entypsegling events (informational only)
- Add personal_number field to participants table for entypsegling events (replaces boat connection)
- Modify participant-boat relationship to be optional for entypsegling events
- Support decimal point precision for tied finish calculations:
  - Points fields (HeatResult.Points, TotalResult.TotalPoints) use float64 data type
  - Tied finish algorithm calculates exact average points without rounding during computation
  - Display formatting uses one decimal precision (%.1f) across all interfaces
  - CSV exports include decimal points with one decimal precision
  - All tie-breaking and sorting algorithms handle float64 point values

### 9.2 Build and Deployment

- Create build script using git tags for versioning
- Add commit hash when commits exist after tag
- Write commit messages in English

### 9.3 Version Control

- Push project to GitHub at https://github.com/rogge66/sailapp (email: <EMAIL>)

### 9.4 Testing

- All endpoints must have automated tests
- Create a test script that verifies all endpoints using curl
- Support testing with a separate test database
- Include option to populate test database with sample data
- Document testing procedures and requirements
- Require tests for all new features and changes
- Ensure test coverage for critical functionality