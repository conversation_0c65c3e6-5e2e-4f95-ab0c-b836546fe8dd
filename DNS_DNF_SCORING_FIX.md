# DNS/DNF Scoring Fix - Racing Rules of Sailing (RRS) Compliance

## Problem Description

The sailing application had **inconsistent DNS/DNF scoring** between regular sailing and entypsegling events, with regular sailing events not following the Racing Rules of Sailing (RRS) Appendix A standards.

### Issue Details

**Before the fix:**
- **Entypsegling events** ✅ **CORRECT**: Followed RRS Appendix A
  - DNS: `totalParticipants + 1` points
  - DNF: `len(results) + 1` points (last finisher + 1)

- **Regular sailing events** ❌ **INCORRECT**: Used sequential positioning
  - Both DNS and DNF got sequential positions starting from `len(results) + 1`
  - This violated RRS standards and created inconsistency

## Racing Rules of Sailing (RRS) Appendix A Standards

According to RRS Appendix A, the correct scoring should be:

- **DNF points** = antal startande + 1 (last finisher + 1)
- **DNS points** = antal anmälda + 1 (total participants + 1)

### Example Scenario
With 6 participants where 4 finished, 1 DNF, 1 DNS:
- **DNF points**: 4 + 1 = **5.0 points** (last finisher + 1)
- **DNS points**: 6 + 1 = **7.0 points** (total participants + 1)

## Solution

### Code Changes

**File:** `pkg/database/heats.go`

**Before (lines 1241-1253):**
```go
// Assign positions and points for DNF and DNS
nextPosition := len(results) + 1
for i := range dnfResults {
    dnfResults[i].Position = nextPosition
    dnfResults[i].Points = float64(nextPosition)
    nextPosition++
}

for i := range dnsResults {
    dnsResults[i].Position = nextPosition
    dnsResults[i].Points = float64(nextPosition)
    nextPosition++
}
```

**After (lines 1241-1255):**
```go
// Calculate DNS and DNF points according to Racing Rules of Sailing (RRS) Appendix A
totalParticipants := len(results) + len(dnfResults) + len(dnsResults)

// DNF boats get points equal to one more than the last boat that finished
dnfPoints := len(results) + 1
for i := range dnfResults {
    dnfResults[i].Position = 0 // DNF boats don't get a position number
    dnfResults[i].Points = float64(dnfPoints)
}

// DNS boats get points equal to total participants + 1
for i := range dnsResults {
    dnsResults[i].Position = 0 // DNS boats don't get a position number
    dnsResults[i].Points = float64(totalParticipants + 1)
}
```

### Key Changes

1. **Consistent RRS compliance**: Both regular sailing and entypsegling now follow the same RRS Appendix A standards
2. **Correct point calculation**: 
   - DNF = last finisher + 1
   - DNS = total participants + 1
3. **Position handling**: DNS/DNF boats get `Position = 0` instead of sequential positions
4. **Documentation**: Added clear comments explaining the RRS standards

## Testing

### New Comprehensive Test

**File:** `pkg/database/dns_dnf_scoring_test.go`

Created `TestDNSDNFScoringRRS` that verifies:
- ✅ Both regular sailing and entypsegling follow RRS standards
- ✅ DNF points = last finisher + 1
- ✅ DNS points = total participants + 1
- ✅ All DNF boats receive the same points
- ✅ All DNS boats receive the same points
- ✅ DNS boats receive higher penalty than DNF boats
- ✅ Multiple scenarios (6 participants, 8 participants, etc.)

### Test Results
```bash
=== RUN   TestDNSDNFScoringRRS
=== RUN   TestDNSDNFScoringRRS/Regular_sailing:_6_participants,_4_finished,_1_DNF,_1_DNS
=== RUN   TestDNSDNFScoringRRS/Entypsegling:_6_participants,_4_finished,_1_DNF,_1_DNS
=== RUN   TestDNSDNFScoringRRS/Regular_sailing:_8_participants,_5_finished,_2_DNF,_1_DNS
=== RUN   TestDNSDNFScoringRRS/Entypsegling:_8_participants,_5_finished,_2_DNF,_1_DNS
--- PASS: TestDNSDNFScoringRRS (0.02s)
```

## Impact

### Benefits
1. **RRS Compliance**: Now fully compliant with Racing Rules of Sailing Appendix A
2. **Consistency**: Both event types use identical scoring logic
3. **Correctness**: DNS/DNF penalties now reflect actual sailing rules
4. **Future-proof**: Comprehensive tests prevent regression

### Backward Compatibility
- ✅ Existing entypsegling events: **No change** (already correct)
- ⚠️ Existing regular sailing events: **Scoring will change** to be RRS-compliant
- 📝 This is a **bug fix**, not a breaking change - the previous behavior was incorrect

## Verification

To verify the fix works correctly:

1. **Run the new test:**
   ```bash
   go test ./pkg/database -v -run TestDNSDNFScoringRRS
   ```

2. **Run all scoring tests:**
   ```bash
   go test ./pkg/database -v -run "TestDNS|TestTied|TestShared|TestCompletely"
   ```

3. **Test with real data:** Create a test event with DNS/DNF participants and verify points match RRS standards

## References

- **Racing Rules of Sailing Appendix A**: Standard point system for sailing competitions
- **User specification**: DNS = antal anmälda + 1, DNF = antal startande + 1
- **Test documentation**: `test_scoring_scenarios.md` contains examples of correct scoring

---

**Fix completed:** ✅ DNS/DNF scoring now follows Racing Rules of Sailing (RRS) Appendix A standards consistently across all event types.
