URL: https://api.trafikinfo.trafikverket.se/v2/data.json

<REQUEST>
  <LOGIN authenticationkey="44b7a85536ef4238bddd7d885976a1f0"/>
    <QUERY objecttype="WeatherObservation" namespace="Road.WeatherInfo" schemaversion="2.1" limit="1">     
    <FILTER>   <EQ name="Measurepoint.Name" value="Linköping" />   </FILTER>     
    <INCLUDE>Wind.Speed</INCLUDE>     
    <INCLUDE>Wind.Direction</INCLUDE>
    <INCLUDE>Air.Temperature</INCLUDE>
    <INCLUDE>Aggregated10minutes.Wind</INCLUDE>        
    <INCLUDE>ModifiedTime</INCLUDE>     
    </QUERY> 
</REQUEST>

{
	"RESPONSE": {
		"RESULT": [
			{
				"WeatherObservation": [
					{
						"Air": {
							"Temperature": {
								"Origin": "measured",
								"SensorNames": "HMP155_1",
								"Value": 14.5
							}
						},
						"Wind": [
							{
								"Speed": {
									"Origin": "measured",
									"SensorNames": "WMT700_1",
									"Value": 1
								},
								"Direction": {
									"Origin": "measured",
									"SensorNames": "WMT700_1",
									"Value": 215
								}
							}
						],
						"Aggregated10minutes": {
							"Wind": {
								"SpeedMax": {
									"Origin": "measured",
									"SensorNames": "WMT700_1",
									"Value": 2.3
								},
								"SpeedAverage": {
									"Origin": "measured",
									"SensorNames": "WMT700_1",
									"Value": 1
								}
							}
						},
						"ModifiedTime": "2025-06-08T08:44:03.271Z"
					}
				]
			}
		]
	}
}