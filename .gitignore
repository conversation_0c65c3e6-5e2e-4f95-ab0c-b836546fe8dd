*.db-shm
*.db-wal

# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
segling

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Database files
*.db-journal

# Backup files
backups/

# Certificates
certs/

# Credentials
credentials/

# Build directory
build/

# macOS files
.DS_Store

# IDE files
.idea/
.vscode/
*.swp
*.swo

