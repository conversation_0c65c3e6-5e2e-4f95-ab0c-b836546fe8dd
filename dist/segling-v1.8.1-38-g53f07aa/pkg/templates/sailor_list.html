{{ if .sailors }}
<div class="table-responsive">
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Namn</th>
                <th>Telefon</th>
                <th>Klubb</th>
                <th>Åtg<PERSON>rder</th>
            </tr>
        </thead>
        <tbody>
            {{ range .sailors }}
            <tr>
                <td>{{ .Namn }}</td>
                <td>{{ .Telefon }}</td>
                <td>{{ .Klubb }}</td>
                <td>
                    <a href="/sailors/{{ .ID }}" class="btn btn-sm btn-outline-primary">Redigera</a>
                    <button class="btn btn-sm btn-outline-danger"
                        hx-delete="/sailors/{{ .ID }}"
                        hx-confirm="Är du säker på att du vill ta bort denna seglare?"
                        hx-target="closest tr"
                        hx-swap="delete swap:1s">
                        Ta bort
                    </button>
                </td>
            </tr>
            {{ end }}
        </tbody>
    </table>
</div>
{{ else }}
<p>Inga seglare hittades.</p>
{{ end }}
