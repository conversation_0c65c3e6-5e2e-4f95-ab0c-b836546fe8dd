<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#0d6efd">
    <title>{{ .title }} - Segling</title>
    <!-- Favicon -->
    <link rel="icon" href="/static/favicon.png" type="image/png">
    <link rel="shortcut icon" href="/static/favicon.png" type="image/png">
    <link rel="apple-touch-icon" href="/static/favicon.png">
    <!-- For IE -->
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <!-- Prevent flash of unstyled content in dark mode -->
    <script>
        // Apply theme immediately before page renders
        (function() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                document.documentElement.setAttribute('data-bs-theme', savedTheme);
            } else {
                const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
                const initialTheme = prefersDarkMode ? 'dark' : 'light';
                document.documentElement.setAttribute('data-bs-theme', initialTheme);
                localStorage.setItem('theme', initialTheme);
            }

            // Add a class to body to prevent transition effects on initial load
            document.addEventListener('DOMContentLoaded', function() {
                document.body.classList.add('theme-transition-ready');
            });
        })();
    </script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <script src="https://unpkg.com/htmx.org@1.9.2"></script>
    <script>
        // Debug HTMX requests
        document.addEventListener('htmx:beforeRequest', function(event) {
            console.log('HTMX Request:', {
                method: event.detail.requestConfig.method,
                url: event.detail.requestConfig.path,
                target: event.detail.target,
                triggeringEvent: event.detail.triggeringEvent,
                headers: event.detail.requestConfig.headers
            });
        });

        document.addEventListener('htmx:beforeSend', function(event) {
            console.log('HTMX Before Send:', {
                method: event.detail.xhr.method,
                url: event.detail.xhr.url,
                target: event.detail.target
            });
        });

        document.addEventListener('htmx:afterRequest', function(event) {
            console.log('HTMX After Request:', {
                successful: event.detail.successful,
                status: event.detail.xhr.status,
                statusText: event.detail.xhr.statusText,
                target: event.detail.target
            });
        });

        document.addEventListener('htmx:responseError', function(event) {
            console.error('HTMX Response Error:', {
                status: event.detail.xhr.status,
                statusText: event.detail.xhr.statusText,
                error: event.detail.error,
                target: event.detail.target
            });
        });

        // Safari-specific fix for time inputs to force 24-hour format
        document.addEventListener('DOMContentLoaded', function() {
            // Detect Safari browser
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

            // Apply to all time inputs with data-force-24h attribute
            const timeInputs = document.querySelectorAll('input[type="time"][data-force-24h="true"]');

            timeInputs.forEach(input => {
                // Set the input mode to 24h format (works in some browsers)
                input.setAttribute('data-time-format', '24h');

                if (isSafari) {
                    // For Safari, we need a more aggressive approach

                    // Store the original value
                    const originalValue = input.value;

                    // Create a wrapper div to contain our custom input
                    const wrapper = document.createElement('div');
                    wrapper.className = 'time-input-wrapper';
                    wrapper.style.position = 'relative';
                    wrapper.style.display = 'inline-block';
                    wrapper.style.width = '100%';

                    // Create a text input that will be visible to the user
                    const textInput = document.createElement('input');
                    textInput.type = 'text';
                    textInput.className = input.className;
                    textInput.id = input.id + '_text';
                    textInput.placeholder = 'HH:MM:SS';
                    textInput.pattern = '([01]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?';
                    textInput.title = 'Enter time in 24-hour format (HH:MM:SS)';
                    textInput.value = originalValue;
                    textInput.style.width = '100%';

                    // Hide the original input but keep it in the DOM for form submission
                    input.style.display = 'none';

                    // Add the text input to the wrapper
                    wrapper.appendChild(textInput);

                    // Insert the wrapper before the original input
                    input.parentNode.insertBefore(wrapper, input);

                    // Move the original input inside the wrapper
                    wrapper.appendChild(input);

                    // Add event listeners to sync the inputs
                    textInput.addEventListener('input', function() {
                        // Validate the input format (24-hour time with optional seconds)
                        const timeRegex = /^([01]?[0-9]|2[0-3]):([0-5][0-9])(:[0-5][0-9])?$/;
                        if (timeRegex.test(this.value)) {
                            // If seconds are not provided, add :00 for seconds
                            let value = this.value;
                            if (value.split(':').length === 2) {
                                value += ':00';
                            }
                            input.value = value;

                            // If this is a finish time input with HTMX attributes, trigger the HTMX request
                            if (input.hasAttribute('hx-post') && input.hasAttribute('hx-trigger')) {
                                // Manually trigger the HTMX request
                                const event = new Event('change');
                                input.dispatchEvent(event);
                            }
                        }
                    });

                    // Format validation on blur
                    textInput.addEventListener('blur', function() {
                        // If the input is not empty but doesn't match the format, reset it
                        const timeRegex = /^([01]?[0-9]|2[0-3]):([0-5][0-9])(:[0-5][0-9])?$/;
                        if (this.value && !timeRegex.test(this.value)) {
                            this.value = '';
                            input.value = '';
                        } else if (this.value && this.value.split(':').length === 2) {
                            // If seconds are not provided, add them and update the display
                            this.value = this.value + ':00';
                            input.value = this.value;
                        }
                    });
                } else {
                    // For non-Safari browsers, just add focus event
                    input.addEventListener('focus', function() {
                        this.setAttribute('data-time-format', '24h');
                    });
                }
            });
        });
    </script>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">Segling</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Hem</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/sailors">Seglare</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/boats">Båtar</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/events">Tävlingar</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/srs">SRS Data</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/github-pages">
                            <i class="bi bi-github"></i> GitHub Pages
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/settings">Inställningar</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/about">Om</a>
                    </li>
                </ul>
                <div class="theme-toggle nav-link text-white" id="theme-toggle" aria-label="Toggle dark/light mode">
                    <i class="bi bi-sun-fill" id="theme-icon"></i>
                </div>
            </div>
        </div>
    </nav>
