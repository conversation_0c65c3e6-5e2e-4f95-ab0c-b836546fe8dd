{{ template "header.html" . }}

<div class="container mt-4">
    <h1>J<PERSON><PERSON><PERSON>ttider - {{ .event.Namn }}</h1>

    <div class="row">
        <div class="col-md-3">
            {{ template "event_nav.html" . }}
        </div>
        <div class="col-md-9">
            <!-- Heat selection -->
            {{if gt (len .heats) 1}}
            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>Välj deltävling</h5>
                        </div>
                        <div class="card-body">
                            <div class="btn-group" role="group" aria-label="Heat selection">
                                {{range .heats}}
                                <a href="/events/{{$.event.ID}}/jaktstart?heat={{.ID}}"
                                   class="btn {{if eq .ID $.selectedHeat.ID}}btn-primary{{else}}btn-outline-primary{{end}}">
                                    {{.Name}}
                                    {{if .StartTime}}<br><small>{{.StartTime}}</small>{{end}}
                                </a>
                                {{end}}
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">
                                    Visar jaktstarttider för: <strong>{{.selectedHeat.Name}}</strong>
                                    {{if .selectedHeat.StartTime}}(Starttid: {{.selectedHeat.StartTime}}){{end}}
                                    <a href="/events/{{.event.ID}}/heats" class="ms-2">
                                        <i class="fas fa-cog"></i> Hantera deltävlingar
                                    </a>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {{else if .selectedHeat.ID}}
            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Visar jaktstarttider för: <strong>{{.selectedHeat.Name}}</strong>
                        {{if .selectedHeat.StartTime}}(Starttid: {{.selectedHeat.StartTime}}){{end}}
                        <a href="/events/{{.event.ID}}/heats" class="ms-2">
                            <i class="fas fa-cog"></i> Hantera deltävlingar
                        </a>
                    </div>
                </div>
            </div>
            {{end}}

            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        <strong>Datum:</strong> {{ .event.Datum.Format "2006-01-02" }} |
                        <strong>Starttid:</strong> {{if .selectedHeat.StartTime}}{{.selectedHeat.StartTime}}{{else}}{{ .event.Starttid }}{{end}} |
                        <strong>Vind:</strong> {{ .event.Vind }} m/s |
                        <strong>Banlängd:</strong> {{ .event.Banlangd }} nm
                    </p>
                </div>
            </div>
            <div class="card">
                <div class="card-header">
                    <h2>Starttider</h2>
                    <p class="text-muted mb-0">Första båt startar kl {{if .selectedHeat.StartTime}}{{.selectedHeat.StartTime}}{{else}}{{ .event.Starttid }}{{end}} (båt med lägst SRS-tal)</p>
                    <p class="text-muted mb-0">Alla tider är avrundade till närmaste minut</p>
                </div>
                <div class="card-body">
                    {{ if .participants }}
                    <div class="mb-3">
                        <a href="/events/{{ .event.ID }}/jaktstart/print" class="btn btn-outline-secondary" target="_blank">
                            <i class="bi bi-printer"></i> Utskriftsversion (individuella startkort)
                        </a>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Seglare</th>
                                    <th>Båt</th>
                                    <th>SRS-värde</th>
                                    <th>Starttid (kl)</th>
                                    <th>Offset</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{ range .participants }}
                                <tr>
                                    <td>{{ .Sailor.Namn }}</td>
                                    <td>
                                        {{ .Boat.Namn }} ({{ .Boat.Battyp }})
                                        {{ if or .Boat.Segelnummer .Boat.Nationality }}
                                            <br>
                                            <small class="text-muted">
                                                {{ if and .Boat.Nationality .Boat.Segelnummer }}
                                                    {{ .Boat.Nationality }}-{{ .Boat.Segelnummer }}
                                                {{ else if .Boat.Segelnummer }}
                                                    {{ .Boat.Segelnummer }}
                                                {{ else if .Boat.Nationality }}
                                                    {{ .Boat.Nationality }}
                                                {{ end }}
                                            </small>
                                        {{ end }}
                                    </td>
                                    <td>
                                        {{ if .UseCustomSRSValue }}
                                            {{ .CustomSRSValue }} (anpassad)
                                        {{ else }}
                                            {{ .SelectedSRSValue }}
                                            {{ if eq .SRSType "srs" }}
                                                (SRS)
                                            {{ else if eq .SRSType "srs_utan_undanvindsegel" }}
                                                (SRS utan undanvindsegel)
                                            {{ else if eq .SRSType "srs_shorthanded" }}
                                                (SRS S/H)
                                            {{ else if eq .SRSType "srs_shorthanded_utan_undanvindsegel" }}
                                                (SRS S/H utan undanvindsegel)
                                            {{ end }}
                                        {{ end }}
                                    </td>
                                    <td>{{ .AbsoluteStartTime }}</td>
                                    <td>{{ .StartTimeOffset }}</td>
                                </tr>
                                {{ end }}
                            </tbody>
                        </table>
                    </div>
                    {{ else }}
                    <p>Inga deltagare hittades.</p>
                    {{ end }}
                </div>
            </div>
        </div>
    </div>
</div>

{{ template "footer.html" . }}
