{{ template "header.html" . }}

<div class="container mt-4">
    <h1>{{ .title }}</h1>

    <div class="row">
        <div class="col-md-3">
            {{ template "event_nav.html" . }}
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2>{{ if .event }}Redigera tävlingsdetaljer{{ else }}Ny tävling{{ end }}</h2>
                    </div>
                    {{ if .event }}
                    <div class="d-flex align-items-center">
                        <p class="text-muted mb-0 me-2">{{ .event.Namn }} - {{ .event.Datum.Format "2006-01-02" }}</p>
                        <!-- TEMPORARILY DISABLED: Lock status badge -->
                        <!-- {{ if .event.Locked }}
                        <span class="badge bg-danger">Låst</span>
                        {{ end }} -->
                    </div>
                    {{ end }}
                </div>
                <div class="card-body">
                    <!-- TEMPORARILY DISABLED: Lock warning alert -->
                    <!-- {{ if .event.Locked }}
                    <div class="alert alert-warning mb-4">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        För att låsa upp, gå till <a href="/events/{{ .event.ID }}/results">Resultat</a>
                    </div>
                    {{ end }} -->
                    <form action="/events" method="POST">
                        <input type="hidden" name="id" value="{{ if .event }}{{ .event.ID }}{{ end }}">

                        <div class="mb-3">
                            <label for="tavlingstyp" class="form-label">Tävlingstyp</label>
                            <select class="form-select" id="tavlingstyp" name="tavlingstyp">
                                {{ $selectedType := "" }}
                                {{ if .event }}
                                    {{ $selectedType = .event.Tavlingstyp }}
                                {{ end }}
                                {{ if eq $selectedType "" }}
                                    {{ $selectedType = .defaultCompetitionType }}
                                {{ end }}
                                {{ range .competitionTypesList }}
                                <option value="{{ . }}" {{ if eq . $selectedType }}selected{{ end }}>{{ . }}</option>
                                {{ end }}
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="namn" class="form-label">Namn</label>
                            <input type="text" class="form-control" id="namn" name="namn" value="{{ if .event }}{{ .event.Namn }}{{ end }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="datum" class="form-label">Datum</label>
                            <input type="date" class="form-control" id="datum" name="datum" value="{{ if .event }}{{ .event.Datum.Format "2006-01-02" }}{{ else }}{{ now.Format "2006-01-02" }}{{ end }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="starttid" class="form-label">Starttid</label>
                            <input type="time" class="form-control" id="starttid" name="starttid" value="{{ if .event }}{{ .event.Starttid }}{{ end }}" {{ if .use24hTime }}data-force-24h="true"{{ end }}>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="vind" class="form-label">Vind (m/s)</label>
                                    <input type="number" class="form-control" id="vind" name="vind" min="0" value="{{ if .event }}{{ .event.Vind }}{{ end }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="banlangd" class="form-label">Banlängd (nm)</label>
                                    <input type="number" class="form-control" id="banlangd" name="banlangd" min="0" value="{{ if .event }}{{ .event.Banlangd }}{{ end }}">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="jaktstart_type" class="form-label">Tävlingsformat</label>
                            <select class="form-select" id="jaktstart_type" name="jaktstart_type" required>
                                <option value="none" {{ if eq .event.JaktstartType "none" }}selected{{ end }}>Vanlig segling</option>
                                <option value="regular" {{ if eq .event.JaktstartType "regular" }}selected{{ end }}>Hel jaktstart</option>
                                <option value="half" {{ if eq .event.JaktstartType "half" }}selected{{ end }}>Halv jaktstart</option>
                                <option value="entypsegling" {{ if eq .event.JaktstartType "entypsegling" }}selected{{ end }}>Entypsegling</option>
                            </select>
                            <div id="jaktstart-description" class="form-text text-muted"></div>
                        </div>

                        <div class="mb-3" id="boat-type-field" style="display: none;">
                            <label for="boat_type" class="form-label">Båttyp</label>
                            <input type="text" class="form-control" id="boat_type" name="boat_type" value="{{ if .event }}{{ .event.BoatType }}{{ end }}" placeholder="t.ex. Laser, Optimist, 420">
                            <small class="form-text text-muted">Ange vilken båttyp som används i entypsegling</small>
                        </div>

                        <div class="mb-3">
                            <label for="beskrivning" class="form-label">Beskrivning</label>
                            <textarea class="form-control" id="beskrivning" name="beskrivning" rows="3">{{ if .event }}{{ .event.Beskrivning }}{{ end }}</textarea>
                        </div>

                        <div class="mb-3 d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">Spara</button>
                                <a href="/events" class="btn btn-outline-secondary">Avbryt</a>
                            </div>
                            {{ if .event }}
                            <button type="button" class="btn btn-outline-danger"
                                onclick="deleteEvent({{ .event.ID }})">
                                Ta bort tävling
                            </button>
                            {{ end }}
                        </div>
                    </form>
                </div>
            </div>


        </div>
    </div>
</div>

<script>
    // Function to handle event deletion
    function deleteEvent(eventId) {
        if (confirm('Är du säker på att du vill ta bort denna tävling? Alla deltagare och resultat kommer också att tas bort.')) {
            // Clear the selected event from localStorage
            localStorage.removeItem('selectedEventID');

            // Send the delete request
            fetch('/events/' + eventId, {
                method: 'DELETE',
            })
            .then(response => {
                if (response.ok) {
                    // Redirect to the events list page
                    window.location.href = '/events';
                } else {
                    // Show error message
                    return response.text().then(text => {
                        try {
                            const data = JSON.parse(text);
                            throw new Error(data.error || 'Ett fel uppstod vid borttagning av tävlingen');
                        } catch (e) {
                            throw new Error('Ett fel uppstod vid borttagning av tävlingen');
                        }
                    });
                }
            })
            .catch(error => {
                alert(error.message);
            });
        }
    }

    // Function to update event name based on competition type and date
    function updateEventName() {
        // Only update if this is a new event (no event ID)
        const eventIdField = document.querySelector('input[name="id"]');
        if (eventIdField && eventIdField.value) {
            return; // This is an existing event, don't update the name
        }

        const namnField = document.getElementById('namn');
        const datumField = document.getElementById('datum');
        const tavlingstypField = document.getElementById('tavlingstyp');

        // Only update if the name field is empty or matches the previous auto-generated name
        if (!namnField.value || namnField.dataset.autoGenerated === 'true') {
            if (datumField.value && tavlingstypField.value) {
                // Format the date as YYYY-MM-DD
                const dateParts = datumField.value.split('-');
                if (dateParts.length === 3) {
                    const formattedDate = dateParts[0] + '-' + dateParts[1] + '-' + dateParts[2];
                    const newName = tavlingstypField.value + ' ' + formattedDate;
                    namnField.value = newName;
                    namnField.dataset.autoGenerated = 'true';
                }
            }
        }
    }

    // Function to handle jaktstart type changes and update descriptions
    function updateJaktstartTypeDisplay() {
        const jaktstartTypeSelect = document.getElementById('jaktstart_type');
        const boatTypeField = document.getElementById('boat-type-field');
        const descriptionDiv = document.getElementById('jaktstart-description');

        if (!jaktstartTypeSelect || !boatTypeField || !descriptionDiv) {
            return;
        }

        const selectedType = jaktstartTypeSelect.value;

        // Update description based on selected type
        switch (selectedType) {
            case 'none':
                descriptionDiv.textContent = 'Vanlig segling utan jaktstart. Alla båtar startar samtidigt.';
                boatTypeField.style.display = 'none';
                // Clear boat type when hiding
                const boatTypeInput = document.getElementById('boat_type');
                if (boatTypeInput) {
                    boatTypeInput.value = '';
                }
                break;
            case 'regular':
                descriptionDiv.textContent = 'Hel jaktstart - båtar startar vid olika tidpunkter baserat på SRS-tal så att alla båtar når mållinjen samtidigt vid perfekt segling.';
                boatTypeField.style.display = 'none';
                // Clear boat type when hiding
                const boatTypeInputRegular = document.getElementById('boat_type');
                if (boatTypeInputRegular) {
                    boatTypeInputRegular.value = '';
                }
                break;
            case 'half':
                descriptionDiv.textContent = 'Halv jaktstart - båtar startar med halva jaktstartstiden så att de möts vid halva banlängden. Används för att jämna ut väderförhållanden under dagen.';
                boatTypeField.style.display = 'none';
                // Clear boat type when hiding
                const boatTypeInputHalf = document.getElementById('boat_type');
                if (boatTypeInputHalf) {
                    boatTypeInputHalf.value = '';
                }
                break;
            case 'entypsegling':
                descriptionDiv.textContent = 'Entypsegling - alla deltagare använder samma båttyp, endast placering räknas (inga SRS-tal eller tider).';
                boatTypeField.style.display = 'block';
                break;
        }
    }

    // Add event listeners when the document is loaded
    document.addEventListener('DOMContentLoaded', function() {
        const datumField = document.getElementById('datum');
        const tavlingstypField = document.getElementById('tavlingstyp');
        const namnField = document.getElementById('namn');


        // Set up jaktstart type dropdown
        const jaktstartTypeSelect = document.getElementById('jaktstart_type');
        if (jaktstartTypeSelect) {
            // Set initial state
            updateJaktstartTypeDisplay();

            // Add event listener for changes
            jaktstartTypeSelect.addEventListener('change', updateJaktstartTypeDisplay);
        }

        // Only set up auto-generation for new events
        const eventIdField = document.querySelector('input[name="id"]');
        if (!eventIdField || !eventIdField.value) {
            // This is a new event

            // Set initial name if date and competition type are already set
            updateEventName();

            // Update name when date or competition type changes
            if (datumField) {
                datumField.addEventListener('change', updateEventName);
            }

            if (tavlingstypField) {
                tavlingstypField.addEventListener('change', updateEventName);
            }

            // When user manually edits the name, stop auto-generation
            if (namnField) {
                namnField.addEventListener('input', function() {
                    // If the user is typing, it's no longer auto-generated
                    namnField.dataset.autoGenerated = 'false';
                });
            }
        }
    });
</script>

{{ template "footer.html" . }}
