{{ template "header.html" . }}

<div class="container mt-4">
    <h1>{{ .title }}</h1>

    <div class="card">
        <div class="card-header">
            <h2>{{ if .sailor }}Redigera seglare{{ else }}Ny seglare{{ end }}</h2>
        </div>
        <div class="card-body">
            <form action="/sailors" method="POST">
                <input type="hidden" name="id" value="{{ if .sailor }}{{ .sailor.ID }}{{ end }}">

                <div class="mb-3">
                    <label for="namn" class="form-label">Namn</label>
                    <input type="text" class="form-control" id="namn" name="namn" value="{{ if .sailor }}{{ .sailor.Namn }}{{ end }}" required>
                </div>

                <div class="mb-3">
                    <label for="telefon" class="form-label">Telefon</label>
                    <input type="tel" class="form-control" id="telefon" name="telefon" value="{{ if .sailor }}{{ .sailor.Telefon }}{{ end }}">
                </div>

                <div class="mb-3">
                    <label for="klubb" class="form-label">Klubb</label>
                    <input type="text" class="form-control" id="klubb" name="klubb" value="{{ if .sailor }}{{ .sailor.Klubb }}{{ else }}{{ .defaultClub }}{{ end }}">
                </div>

                <div class="mb-3">
                    <button type="submit" class="btn btn-primary">Spara</button>
                    <a href="/sailors" class="btn btn-outline-secondary">Avbryt</a>
                </div>
            </form>
        </div>
    </div>
</div>

{{ template "footer.html" . }}
