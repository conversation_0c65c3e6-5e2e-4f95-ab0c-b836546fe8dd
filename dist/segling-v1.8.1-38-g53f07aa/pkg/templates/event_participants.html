{{ template "header.html" . }}

<style>
    .disabled-form {
        opacity: 0.7;
        pointer-events: none;
    }

    .disabled-form input,
    .disabled-form select,
    .disabled-form textarea,
    .disabled-form button {
        pointer-events: none;
    }

    a[disabled] {
        pointer-events: none;
        opacity: 0.65;
    }
</style>

<div class="container mt-4">
    <h1>{{ .title }}</h1>

    <div class="row">
        <div class="col-md-3">
            {{ template "event_nav.html" . }}
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2>Hantera deltagare</h2>
                    </div>
                    <div class="d-flex align-items-center">
                        <p class="text-muted mb-0 me-2">{{ .event.Namn }} - {{ .event.Datum.Format "2006-01-02" }}</p>
                        <!-- TEMPORARILY DISABLED: Lock status badge -->
                        <!-- {{ if .event.Locked }}
                        <span class="badge bg-danger">Låst</span>
                        {{ end }} -->
                    </div>
                </div>
                <div class="card-body">
                    <!-- TEMPORARILY DISABLED: Lock warning alert -->
                    <!-- {{ if .event.Locked }}
                    <div class="alert alert-warning mb-4">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        För att låsa upp, gå till <a href="/events/{{ .event.ID }}/results">Resultat</a>
                    </div>
                    {{ end }} -->
                    {{ if .event.Entypsegling }}
                    <!-- Entypsegling participant form -->
                    <div class="alert alert-info mb-3">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Entypsegling:</strong> Båttyp för denna tävling är <strong>{{ .event.BoatType }}</strong>. Deltagare använder personliga nummer istället för specifika båtar.
                    </div>
                    <form id="add-participant-form" hx-post="/events/{{ .event.ID }}/participants" hx-target="#participants-list" hx-swap="beforeend">
                        <input type="hidden" name="event_id" value="{{ .event.ID }}">

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="sailor_id" class="form-label">Seglare</label>
                                    <select class="form-select" id="sailor_id" name="sailor_id" required>
                                        <option value="">Välj seglare</option>
                                        {{ range .sailors }}
                                        <option value="{{ .ID }}">{{ .Namn }}</option>
                                        {{ end }}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="personal_number" class="form-label">Segelnummer</label>
                                    <input type="text" class="form-control" id="personal_number" name="personal_number" placeholder="t.ex. SWE 123" required>
                                    <small class="form-text text-muted">Segelnummer för tävlingen</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="crew_count" class="form-label">Antal gastar</label>
                                    <div class="input-group">
                                        <input type="number" min="0" class="form-control" id="crew_count" name="crew_count" value="0">
                                        <div class="input-group-text">
                                            <small>Exklusive skeppare</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <button type="submit" class="btn btn-primary" {{ if .event.Locked }}disabled{{ end }}>Lägg till deltagare</button>
                                </div>
                            </div>
                        </div>
                    </form>
                    {{ else }}
                    <!-- Regular participant form -->
                    <form id="add-participant-form" hx-post="/events/{{ .event.ID }}/participants" hx-target="#participants-list" hx-swap="beforeend">
                        <input type="hidden" name="event_id" value="{{ .event.ID }}">

                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="sailor_id" class="form-label">Seglare</label>
                                    <select class="form-select" id="sailor_id" name="sailor_id" required>
                                        <option value="">Välj seglare</option>
                                        {{ range .sailors }}
                                        <option value="{{ .ID }}">{{ .Namn }}</option>
                                        {{ end }}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="boat_id" class="form-label">Båt</label>
                                    <select class="form-select" id="boat_id" name="boat_id" required>
                                        <option value="">Välj båt</option>
                                        {{ range .boats }}
                                        <option value="{{ .ID }}">{{ .Namn }} ({{ .Battyp }})</option>
                                        {{ end }}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="srs_type" class="form-label">SRS-typ</label>
                                    <select class="form-select" id="srs_type" name="srs_type">
                                        <option value="srs">SRS</option>
                                        <option value="srs_utan_undanvindsegel">SRS utan undanvindsegel</option>
                                        <option value="srs_shorthanded">SRS S/H</option>
                                        <option value="srs_shorthanded_utan_undanvindsegel">SRS S/H utan undanvindsegel</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="boat_srs_value" class="form-label">SRS-värde från båt</label>
                                    <input type="number" class="form-control" id="boat_srs_value" step="0.001" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="custom_srs_value" class="form-label">Anpassat SRS-värde</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="custom_srs_value" name="custom_srs_value" step="0.001" readonly>
                                        <div class="input-group-text">
                                            <input class="form-check-input mt-0" type="checkbox" id="use_custom_srs_value" name="use_custom_srs_value" onchange="toggleCustomSRSValue(this)">
                                            <label class="form-check-label ms-2" for="use_custom_srs_value">Använd</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="crew_count" class="form-label">Antal gastar</label>
                                    <div class="input-group">
                                        <input type="number" min="0" class="form-control" id="crew_count" name="crew_count" value="0">
                                        <div class="input-group-text">
                                            <small>Exklusive skeppare</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <button type="submit" class="btn btn-primary" {{ if .event.Locked }}disabled{{ end }}>Lägg till deltagare</button>
                                </div>
                            </div>
                        </div>
                    </form>
                    {{ end }}

                    <!-- Copy participants from another event -->
                    <div class="mt-4 mb-4">
                        <h4>Kopiera deltagare från annan tävling</h4>
                        <p class="text-muted">Deltagare som redan finns i denna tävling kommer att ignoreras.</p>
                        <form id="copy-participants-form" hx-post="/participants/copy" hx-target="#participants-list" hx-swap="innerHTML" {{ if .event.Locked }}class="disabled-form"{{ end }}>
                            <input type="hidden" name="target_event_id" value="{{ .event.ID }}">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="source_event_id" class="form-label">Välj tävling att kopiera från</label>
                                        <select class="form-select" id="source_event_id" name="source_event_id" required>
                                            <option value="">Välj tävling</option>
                                            {{ range .events }}
                                                {{ if ne .ID $.event.ID }}
                                                <option value="{{ .ID }}">{{ .Namn }} ({{ .Datum.Format "2006-01-02" }})</option>
                                                {{ end }}
                                            {{ end }}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-secondary d-block" {{ if .event.Locked }}disabled{{ end }}>Kopiera deltagare</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="mt-4">
                        <div class="mb-3">
                            <h4>Deltagarlista</h4>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Seglare</th>
                                        {{ if .event.Entypsegling }}
                                        <th>Segelnummer</th>
                                        <th>Gastar</th>
                                        {{ else }}
                                        <th>Båt</th>
                                        <th>SRS-värde</th>
                                        <th>Besättning</th>
                                        {{ end }}
                                        <th>Åtgärder</th>
                                    </tr>
                                </thead>
                                <tbody id="participants-list">
                                    {{ if .participants }}
                                        {{ range .participants }}
                                        <tr id="participant-{{ .ID }}">
                                            <td>{{ .Sailor.Namn }}</td>
                                            {{ if $.event.Entypsegling }}
                                            <td>
                                                <strong>{{ .PersonalNumber }}</strong>
                                            </td>
                                            {{ else }}
                                            <td>
                                                {{ .Boat.Namn }} ({{ .Boat.Battyp }})
                                                {{ if or .Boat.Segelnummer .Boat.Nationality }}
                                                    <br>
                                                    <small class="text-muted">
                                                        {{ if and .Boat.Nationality .Boat.Segelnummer }}
                                                            {{ .Boat.Nationality }}-{{ .Boat.Segelnummer }}
                                                        {{ else if .Boat.Segelnummer }}
                                                            {{ .Boat.Segelnummer }}
                                                        {{ else if .Boat.Nationality }}
                                                            {{ .Boat.Nationality }}
                                                        {{ end }}
                                                    </small>
                                                {{ end }}
                                            </td>
                                            <td>
                                                {{ if .SelectedSRSValue }}
                                                    {{ if .UseCustomSRSValue }}
                                                        {{ .CustomSRSValue }} (anpassad)
                                                    {{ else }}
                                                        {{ .SelectedSRSValue }}
                                                        {{ if eq .SRSType "srs" }}
                                                            (SRS)
                                                        {{ else if eq .SRSType "srs_utan_undanvindsegel" }}
                                                            (SRS utan undanvindsegel)
                                                        {{ else if eq .SRSType "srs_shorthanded" }}
                                                            (SRS S/H)
                                                        {{ else if eq .SRSType "srs_shorthanded_utan_undanvindsegel" }}
                                                            (SRS S/H utan undanvindsegel)
                                                        {{ end }}
                                                    {{ end }}
                                                {{ else }}
                                                    {{ .Boat.SRS }} (SRS)
                                                {{ end }}
                                            </td>
                                            {{ end }}
                                            <td>
                                                {{ if $.event.Entypsegling }}
                                                    {{ .CrewCount }}
                                                {{ else }}
                                                    {{ if .CrewCount }}
                                                        {{ add .CrewCount 1 }}
                                                    {{ else }}
                                                        1
                                                    {{ end }}
                                                {{ end }}
                                            </td>
                                            <td>
                                                <a href="/participants/{{ .ID }}" class="btn btn-sm btn-outline-primary"><!-- TEMPORARILY DISABLED: {{ if $.event.Locked }}disabled{{ end }} -->Redigera</a>
                                                <button class="btn btn-sm btn-outline-danger delete-participant-btn"
                                                    data-participant-id="{{ .ID }}"><!-- TEMPORARILY DISABLED: {{ if $.event.Locked }}disabled{{ end }} -->
                                                    Ta bort
                                                </button>
                                            </td>
                                        </tr>
                                        {{ end }}
                                    {{ else }}
                                        <tr>
                                            <td colspan="{{ if .event.Entypsegling }}4{{ else }}5{{ end }}" class="text-center">Inga deltagare registrerade</td>
                                        </tr>
                                    {{ end }}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Initialize the SRS value fields on page load
    document.addEventListener('DOMContentLoaded', function() {
        const customSRSValueInput = document.getElementById('custom_srs_value');
        const boatSRSValueInput = document.getElementById('boat_srs_value');
        const checkbox = document.getElementById('use_custom_srs_value');

        // Load participants if we're on the edit page
        const eventId = {{ .event.ID }};
        if (eventId > 0) {
            loadParticipants(eventId);
        }

        // Add click event listeners to delete buttons
        document.addEventListener('click', function(event) {
            if (event.target.closest('.delete-participant-btn')) {
                event.preventDefault(); // Prevent any default action
                event.stopPropagation(); // Stop event propagation

                const button = event.target.closest('.delete-participant-btn');
                console.log('Delete button clicked:', button);

                // Always use our custom handler instead of relying on HTMX
                handleDeleteParticipant(button);
            }
        });

        // Clear the custom value initially
        if (customSRSValueInput) {
            customSRSValueInput.value = '';
        }

        // Clear the boat SRS value initially
        if (boatSRSValueInput) {
            boatSRSValueInput.value = '';
        }

        // Set the readonly state based on checkbox
        if (checkbox) {
            toggleCustomSRSValue(checkbox);
        }
    });

    document.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.target.id === 'participants-list' && event.detail.successful) {
            // Reset the form after successful submission
            if (event.detail.requestConfig.parameters.sailor_id) {
                // This was an add participant request
                document.getElementById('add-participant-form').reset();
                // Reset the SRS value fields
                const customSRSValueInput = document.getElementById('custom_srs_value');
                const boatSRSValueInput = document.getElementById('boat_srs_value');
                if (customSRSValueInput) {
                    customSRSValueInput.value = '';
                }
                if (boatSRSValueInput) {
                    boatSRSValueInput.value = '';
                }

                // Reset the checkbox state
                const checkbox = document.getElementById('use_custom_srs_value');
                if (checkbox) {
                    checkbox.checked = false;
                    toggleCustomSRSValue(checkbox);
                }
            } else if (event.detail.requestConfig.parameters.source_event_id) {
                // This was a copy participants request
                document.getElementById('copy-participants-form').reset();
                // Show a success message
                const count = event.detail.xhr.getResponseHeader('X-Copied-Count');
                if (count) {
                    if (count === "0") {
                        alert('Inga nya deltagare att kopiera. Alla deltagare finns redan i denna tävling.');
                    } else if (count === "1") {
                        alert('1 deltagare har kopierats! Dubbletter har ignorerats.');
                    } else {
                        alert(`${count} deltagare har kopierats! Dubbletter har ignorerats.`);
                    }
                } else {
                    alert('Deltagare har kopierats! Dubbletter har ignorerats.');
                }
            }
        }
    });

    // Handle errors in HTMX requests
    document.addEventListener('htmx:responseError', function(event) {
        // Show an error message
        alert('Ett fel uppstod: ' + (event.detail.xhr.responseText || 'Okänt fel'));
    });

    // Fetch boat SRS data when a boat is selected
    document.getElementById('boat_id')?.addEventListener('change', function() {
        const boatId = this.value;
        if (!boatId) return;

        // Find the selected boat in the options
        const boatOption = Array.from(this.options).find(option => option.value === boatId);
        if (!boatOption) return;

        // Make an AJAX request to get the boat details
        fetch('/boats/' + boatId + '/json')
            .then(response => response.json())
            .then(boat => {
                // Update the custom SRS value field with the selected SRS type value
                updateSRSValueFromType(boat);

                // Store the boat data globally for later use
                window.currentBoatData = boat;

                // Add event listener to update the custom SRS value when SRS type changes
                document.getElementById('srs_type')?.addEventListener('change', function() {
                    if (window.currentBoatData) {
                        updateSRSValueFromType(window.currentBoatData);
                    }
                });
            })
            .catch(error => {
                console.error('Error fetching boat data:', error);
            });
    });

    // Function to update the boat SRS value based on the selected SRS type
    function updateBoatSRSValue(boat) {
        const srsType = document.getElementById('srs_type')?.value;
        const boatSRSValueInput = document.getElementById('boat_srs_value');

        if (!srsType || !boatSRSValueInput || !boat) return;

        let srsValue = 0;
        switch (srsType) {
            case 'srs':
                srsValue = boat.srs;
                break;
            case 'srs_utan_undanvindsegel':
                srsValue = boat.srs_utan_undanvindsegel;
                break;
            case 'srs_shorthanded':
                srsValue = boat.srs_shorthanded;
                break;
            case 'srs_shorthanded_utan_undanvindsegel':
                srsValue = boat.srs_shorthanded_utan_undanvindsegel;
                break;
        }

        // Update the boat SRS value field
        boatSRSValueInput.value = srsValue;
    }

    // Function to update both SRS value fields based on the selected SRS type
    function updateSRSValueFromType(boat) {
        if (!boat) return;

        // Update the boat SRS value field
        updateBoatSRSValue(boat);

        // Update the custom SRS value field if needed
        const useCustomSRSValue = document.getElementById('use_custom_srs_value')?.checked;
        const customSRSValueInput = document.getElementById('custom_srs_value');

        if (useCustomSRSValue && customSRSValueInput && !customSRSValueInput.value) {
            // If custom value is enabled but empty, initialize it with the boat SRS value
            const boatSRSValueInput = document.getElementById('boat_srs_value');
            if (boatSRSValueInput) {
                customSRSValueInput.value = boatSRSValueInput.value;
            }
        }

        // Toggle readonly based on checkbox state
        toggleCustomSRSValue(document.getElementById('use_custom_srs_value'));
    }

    // Toggle the custom SRS value field based on the checkbox
    function toggleCustomSRSValue(checkbox) {
        if (!checkbox) return;

        const customSRSValueInput = document.getElementById('custom_srs_value');
        const boatSRSValueInput = document.getElementById('boat_srs_value');

        if (!customSRSValueInput) return;

        if (checkbox.checked) {
            // If checked, make the field editable (not readonly)
            customSRSValueInput.readOnly = false;

            // If the custom field is empty, initialize it with the boat SRS value
            if (!customSRSValueInput.value && boatSRSValueInput && boatSRSValueInput.value) {
                customSRSValueInput.value = boatSRSValueInput.value;
            }
        } else {
            // If unchecked, make the field readonly and clear its value
            customSRSValueInput.readOnly = true;
            customSRSValueInput.value = '';
        }
    }

    // Function to load participants for an event
    function loadParticipants(eventId) {
        const participantsList = document.getElementById('participants-list');
        if (!participantsList) return;

        // Make an AJAX request to get the participants
        fetch('/events/' + eventId + '/participants/list')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch participants');
                }
                return response.text();
            })
            .then(html => {
                // Update the participants list
                participantsList.innerHTML = html;
            })
            .catch(error => {
                console.error('Error fetching participants:', error);
            });
    }

    // Function to handle participant deletion
    function handleDeleteParticipant(button) {
        const participantId = button.getAttribute('data-participant-id');
        if (!participantId) {
            console.error('No participant ID found on button');
            return;
        }

        console.log('Preparing to delete participant with ID:', participantId);

        // Confirm deletion
        if (!confirm('Är du säker på att du vill ta bort denna deltagare?')) {
            console.log('Deletion cancelled by user');
            return;
        }

        console.log('User confirmed deletion. Sending DELETE request for participant:', participantId);

        // Show loading state
        button.disabled = true;
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Tar bort...';

        // Send DELETE request manually
        fetch('/participants/' + participantId, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'HX-Request': 'true',
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            console.log('Delete response status:', response.status);
            console.log('Delete response headers:', {
                'X-Updated-Participant-Count': response.headers.get('X-Updated-Participant-Count'),
                'X-Event-ID': response.headers.get('X-Event-ID')
            });

            if (response.ok) {
                // Remove the row from the table
                const row = button.closest('tr');
                if (row) {
                    console.log('Removing row from table');
                    row.remove();
                }

                // Get the updated count from the response headers
                const count = response.headers.get('X-Updated-Participant-Count');
                const eventId = response.headers.get('X-Event-ID');

                console.log('Delete successful. Updated count:', count, 'Event ID:', eventId);

                // If there are no more participants, show the "no participants" message
                if (count === "0") {
                    console.log('No more participants, updating table');
                    const participantsList = document.getElementById('participants-list');
                    if (participantsList) {
                        participantsList.innerHTML = '<tr><td colspan="5" class="text-center">Inga deltagare registrerade</td></tr>';
                    }
                }
            } else {
                // Reset button state
                button.disabled = false;
                button.innerHTML = 'Ta bort';

                // Try to get error details
                response.text().then(text => {
                    console.error('Failed to delete participant:', response.status, response.statusText);
                    console.error('Error response:', text);

                    let errorMessage = 'Ett fel uppstod vid borttagning av deltagaren';
                    try {
                        const errorData = JSON.parse(text);
                        if (errorData.error) {
                            errorMessage += ': ' + errorData.error;
                        }
                    } catch (e) {
                        errorMessage += ': ' + response.statusText;
                    }

                    alert(errorMessage);
                }).catch(err => {
                    console.error('Error parsing error response:', err);
                    alert('Ett fel uppstod vid borttagning av deltagaren: ' + response.statusText);
                });
            }
        })
        .catch(error => {
            // Reset button state
            button.disabled = false;
            button.innerHTML = 'Ta bort';

            console.error('Network error deleting participant:', error);
            alert('Ett nätverksfel uppstod vid borttagning av deltagaren: ' + error.message);
        });
    }

    // Function to update participant count after deletion
    function updateParticipantCount(event) {
        console.log('updateParticipantCount called with event:', event);

        // Check if the request was successful
        if (event.detail.successful) {
            console.log('Request was successful');

            // Get the updated count from the response headers
            const count = event.detail.xhr.getResponseHeader('X-Updated-Participant-Count');
            const eventId = event.detail.xhr.getResponseHeader('X-Event-ID');

            console.log('Response headers:', {
                'X-Updated-Participant-Count': count,
                'X-Event-ID': eventId
            });

            if (count && eventId) {
                console.log(`Updated participant count for event ${eventId}: ${count}`);

                // If there are no more participants, show the "no participants" message
                if (count === "0") {
                    const participantsList = document.getElementById('participants-list');
                    if (participantsList) {
                        participantsList.innerHTML = '<tr><td colspan="5" class="text-center">Inga deltagare registrerade</td></tr>';
                    }
                }
            } else {
                console.warn('Missing count or eventId in response headers');
            }
        } else {
            console.error('Request failed:', event.detail.xhr.status, event.detail.xhr.statusText);
            console.error('Response:', event.detail.xhr.responseText);
        }
    }
</script>

{{ template "footer.html" . }}
