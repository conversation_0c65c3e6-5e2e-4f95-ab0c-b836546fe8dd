<div class="card mb-4">
    <div class="card-header">
        <h4 class="mb-0">Tävlingsmeny</h4>
    </div>
    <div class="card-body">
        <div class="list-group mb-3">
            <a href="/events/new" class="list-group-item list-group-item-action {{ if eq .activeMenu "create" }}active{{ end }}" onclick="clearSelectedEvent(event)">
                <i class="bi bi-plus-circle"></i> Skapa ny tävling
            </a>
            <a href="/events" class="list-group-item list-group-item-action {{ if eq .activeMenu "list" }}active{{ end }}" onclick="clearSelectedEvent(event)">
                <i class="bi bi-list"></i> Tävlingar
            </a>
        </div>

        <div class="list-group">
            {{ if .selectedEventID }}
            <a href="/events/{{ .selectedEventID }}" class="list-group-item list-group-item-action {{ if eq .activeMenu "edit" }}active{{ end }}">
                <i class="bi bi-pencil"></i> Redigera tävling
            </a>
            <a href="/events/{{ .selectedEventID }}/participants" class="list-group-item list-group-item-action {{ if eq .activeMenu "participants" }}active{{ end }}">
                <i class="bi bi-people"></i> Hantera deltagare
            </a>
            <a href="/events/{{ .selectedEventID }}/heats" class="list-group-item list-group-item-action {{ if eq .activeMenu "heats" }}active{{ end }}">
                <i class="fas fa-layer-group"></i> Deltävlingar
            </a>
            {{ if .selectedEventJaktstart }}
            <a href="/events/{{ .selectedEventID }}/jaktstart" class="list-group-item list-group-item-action {{ if eq .activeMenu "jaktstart" }}active{{ end }}">
                <i class="bi bi-stopwatch"></i> Jaktstartstider
            </a>
            {{ end }}
            <a href="/events/{{ .selectedEventID }}/finish-times" class="list-group-item list-group-item-action {{ if eq .activeMenu "finish" }}active{{ end }}" id="finish-times-link">
                <i class="bi bi-flag"></i> <span id="finish-times-text">{{ if and .event .event.Entypsegling }}Placeringar{{ else }}Måltider{{ end }}</span>
            </a>
            <a href="/events/{{ .selectedEventID }}/results" class="list-group-item list-group-item-action {{ if eq .activeMenu "results" }}active{{ end }}">
                <i class="bi bi-trophy"></i> Resultat
            </a>
            <a href="/events/{{ .selectedEventID }}/starter-boat" class="list-group-item list-group-item-action {{ if eq .activeMenu "starter-boat" }}active{{ end }}">
                <i class="bi bi-printer"></i> Startbåtsinfo
            </a>
            {{ else }}
            <span class="list-group-item list-group-item-action disabled" data-event-id="{{if .selectedEventID}}{{ .selectedEventID }}{{else}}0{{end}}" data-href-template="/events/%eventId%">
                <i class="bi bi-pencil"></i> Redigera tävling
            </span>
            <span class="list-group-item list-group-item-action disabled" data-event-id="{{if .selectedEventID}}{{ .selectedEventID }}{{else}}0{{end}}" data-href-template="/events/%eventId%/participants">
                <i class="bi bi-people"></i> Hantera deltagare
            </span>
            <span class="list-group-item list-group-item-action disabled" data-event-id="{{if .selectedEventID}}{{ .selectedEventID }}{{else}}0{{end}}" data-href-template="/events/%eventId%/heats">
                <i class="fas fa-layer-group"></i> Deltävlingar
            </span>
            <span class="list-group-item list-group-item-action disabled" data-event-id="{{if .selectedEventID}}{{ .selectedEventID }}{{else}}0{{end}}" data-href-template="/events/%eventId%/jaktstart">
                <i class="bi bi-stopwatch"></i> Jaktstartstider
            </span>
            <span class="list-group-item list-group-item-action disabled" data-event-id="{{if .selectedEventID}}{{ .selectedEventID }}{{else}}0{{end}}" data-href-template="/events/%eventId%/finish-times" id="finish-times-disabled">
                <i class="bi bi-flag"></i> <span id="finish-times-disabled-text">{{ if and .event .event.Entypsegling }}Placeringar{{ else }}Måltider{{ end }}</span>
            </span>
            <span class="list-group-item list-group-item-action disabled" data-event-id="{{if .selectedEventID}}{{ .selectedEventID }}{{else}}0{{end}}" data-href-template="/events/%eventId%/results">
                <i class="bi bi-trophy"></i> Resultat
            </span>
            <span class="list-group-item list-group-item-action disabled" data-event-id="{{if .selectedEventID}}{{ .selectedEventID }}{{else}}0{{end}}" data-href-template="/events/%eventId%/starter-boat">
                <i class="bi bi-printer"></i> Startbåtsinfo
            </span>
            {{ end }}
        </div>
    </div>
</div>

<script>
    function clearSelectedEvent(e) {
        // Clear the selected event ID from localStorage
        localStorage.removeItem('selectedEventID');
        // Let the default navigation happen
    }

    function changeSelectedEvent(eventId) {
        if (eventId) {
            // Verify the event exists before navigating
            fetch(`/api/events/${eventId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Event not found');
                    }
                    return response.json();
                })
                .then(event => {
                    // Store the selected event ID in localStorage
                    localStorage.setItem('selectedEventID', eventId);

                    // Enable menu items immediately
                    enableMenuItems(eventId);

                    // Redirect to the event page
                    window.location.href = '/events/' + eventId;
                })
                .catch(error => {
                    console.error('Error verifying event:', error);

                    // Remove the invalid event from the dropdown
                    const selectedEvent = document.getElementById('selected-event');
                    if (selectedEvent) {
                        for (let i = 0; i < selectedEvent.options.length; i++) {
                            if (selectedEvent.options[i].value === eventId) {
                                selectedEvent.remove(i);
                                break;
                            }
                        }
                        selectedEvent.value = '';
                    }

                    // Clear localStorage
                    localStorage.removeItem('selectedEventID');

                    // Show error message
                    alert('Det valda evenemanget kunde inte hittas. Det kan ha tagits bort.');

                    // Refresh the events list to get the updated dropdown
                    window.location.href = '/events';
                });
        } else {
            // If no event is selected, clear localStorage and go to events list
            localStorage.removeItem('selectedEventID');
            window.location.href = '/events';
        }
    }

    // Initialize the selected event from localStorage if not already set
    document.addEventListener('DOMContentLoaded', function() {
        const selectedEvent = document.getElementById('selected-event');

        // Add custom change event listener to the dropdown
        if (selectedEvent) {
            selectedEvent.addEventListener('change', function(e) {
                e.preventDefault(); // Prevent default behavior
                const eventId = this.value;

                if (eventId) {
                    console.log('Selected event ID:', eventId);

                    // Verify the event exists before navigating
                    fetch(`/api/events/${eventId}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Event not found');
                            }
                            return response.json();
                        })
                        .then(event => {
                            console.log('Event verified:', event);

                            // Store the selected event ID in localStorage
                            localStorage.setItem('selectedEventID', eventId);

                            // Redirect to the event page
                            window.location.href = '/events/' + eventId;
                        })
                        .catch(error => {
                            console.error('Error verifying event:', error);

                            // Remove the invalid event from the dropdown
                            for (let i = 0; i < selectedEvent.options.length; i++) {
                                if (selectedEvent.options[i].value === eventId) {
                                    selectedEvent.remove(i);
                                    break;
                                }
                            }

                            // Reset the dropdown selection
                            selectedEvent.value = '';

                            // Clear localStorage
                            localStorage.removeItem('selectedEventID');

                            // Show error message
                            alert('Det valda evenemanget kunde inte hittas. Det kan ha tagits bort.');

                            // Refresh the events list to get the updated dropdown
                            window.location.href = '/events';
                        });
                } else {
                    // If no event is selected, clear localStorage and go to events list
                    localStorage.removeItem('selectedEventID');
                    window.location.href = '/events';
                }
            });
        }

        const storedEventID = localStorage.getItem('selectedEventID');

        // Check if we have a preselected event in the template
        const hasPreselectedEvent = {{ if .selectedEventID }}true{{ else }}false{{ end }};

        // Check if we're on the events page
        const isEventsPage = window.location.pathname === '/events';

        // Check if we're on a specific event page
        const eventIdMatch = window.location.pathname.match(/\/events\/(\d+)/);
        const isSpecificEventPage = eventIdMatch !== null;

        // If we're on a specific event page, verify the event exists
        if (isSpecificEventPage && eventIdMatch[1]) {
            const pageEventId = eventIdMatch[1];

            // Verify this event exists
            fetch(`/api/events/${pageEventId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Event not found');
                    }
                    return response.json();
                })
                .then(event => {
                    // Event exists, update localStorage
                    localStorage.setItem('selectedEventID', pageEventId);

                    // Update menu text based on event type
                    updateMenuTextForEvent(event);
                })
                .catch(error => {
                    console.error('Error verifying event on page:', error);

                    // If we're on a specific event page but the event doesn't exist,
                    // redirect to the events list
                    if (isSpecificEventPage) {
                        alert('Det valda evenemanget kunde inte hittas. Det kan ha tagits bort.');
                        window.location.href = '/events';
                    }
                });
        }

        // Update the dropdown and menu items
        if (selectedEvent) {
            // If we have a preselected event in the template, update localStorage
            if (hasPreselectedEvent) {
                const selectedEventID = {{ .selectedEventID }};

                // Verify this event exists
                fetch(`/api/events/${selectedEventID}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Event not found');
                        }
                        return response.json();
                    })
                    .then(event => {
                        // Event exists, update localStorage and UI
                        localStorage.setItem('selectedEventID', selectedEventID.toString());

                        // Set the dropdown value
                        selectedEvent.value = selectedEventID.toString();

                        // If we're on the events page, we need to manually enable the menu items
                        if (isEventsPage) {
                            // Force enable the menu items by directly replacing the spans with links
                            forceEnableMenuItems(selectedEventID, event.jaktstart, event.entypsegling);
                        }
                    })
                    .catch(error => {
                        console.error('Error verifying preselected event:', error);

                        // Clear localStorage
                        localStorage.removeItem('selectedEventID');

                        // If we're on the events page, refresh to get updated dropdown
                        if (isEventsPage) {
                            window.location.reload();
                        }
                    });
            }
            // If we have a stored event ID and no selection yet in the template
            else if (storedEventID) {
                // Check if the stored ID exists in the dropdown options
                let optionExists = false;
                for (let i = 0; i < selectedEvent.options.length; i++) {
                    if (selectedEvent.options[i].value === storedEventID) {
                        optionExists = true;
                        break;
                    }
                }

                // Only set the value if the option exists
                if (optionExists) {
                    // Verify this event exists
                    fetch(`/api/events/${storedEventID}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Event not found');
                            }
                            return response.json();
                        })
                        .then(event => {
                            // Event exists, update UI
                            selectedEvent.value = storedEventID;

                            // If we're on the events page, we need to manually enable the menu items
                            if (isEventsPage) {
                                // Force enable the menu items
                                forceEnableMenuItems(storedEventID, event.jaktstart, event.entypsegling);
                            }

                            // Only trigger change if we're not on a specific event context
                            // AND we're not on the events list page
                            if (!isSpecificEventPage && !isEventsPage) {
                                selectedEvent.dispatchEvent(new Event('change'));
                            }
                        })
                        .catch(error => {
                            console.error('Error verifying stored event:', error);

                            // Remove the invalid event from the dropdown
                            for (let i = 0; i < selectedEvent.options.length; i++) {
                                if (selectedEvent.options[i].value === storedEventID) {
                                    selectedEvent.remove(i);
                                    break;
                                }
                            }

                            // Clear localStorage
                            localStorage.removeItem('selectedEventID');
                            selectedEvent.value = '';

                            // If we're on the events page, refresh to get updated dropdown
                            if (isEventsPage) {
                                window.location.reload();
                            }
                        });
                } else {
                    // If the stored event doesn't exist anymore, clear it
                    localStorage.removeItem('selectedEventID');
                }
            }

            // Synchronize dropdown with server data
            fetch('/api/events')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to fetch events');
                    }
                    return response.json();
                })
                .then(events => {
                    // Get current selection
                    const currentSelection = selectedEvent.value;

                    // Clear all options except the first one (Välj tävling...)
                    while (selectedEvent.options.length > 1) {
                        selectedEvent.remove(1);
                    }

                    // Add the updated events to the dropdown
                    events.forEach(event => {
                        const option = document.createElement('option');
                        option.value = event.id.toString();
                        option.text = `${event.namn} (${new Date(event.datum).toISOString().split('T')[0]})`;

                        // If this was the selected event, select it
                        if (currentSelection && currentSelection === event.id.toString()) {
                            option.selected = true;
                        }

                        selectedEvent.add(option);
                    });

                    // If the current selection is not in the updated list, clear it
                    let selectionExists = false;
                    for (let i = 0; i < selectedEvent.options.length; i++) {
                        if (selectedEvent.options[i].value === currentSelection) {
                            selectionExists = true;
                            break;
                        }
                    }

                    if (!selectionExists && currentSelection) {
                        localStorage.removeItem('selectedEventID');
                        selectedEvent.value = '';
                    }
                })
                .catch(error => {
                    console.error('Error updating events dropdown:', error);
                });
        }
    });

    // Helper function to enable menu items for a selected event
    function enableMenuItems(eventId) {
        if (!eventId) return;

        // First, verify the event exists by fetching it from the API
        fetch(`/api/events/${eventId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch event data');
                }
                return response.json();
            })
            .then(event => {
                // Now we have the event data, enable the appropriate menu items
                enableMenuItemsWithEventData(eventId, event);

                // Store the verified event ID in localStorage
                localStorage.setItem('selectedEventID', eventId.toString());

                // Update the dropdown to show the selected event
                const selectedEvent = document.getElementById('selected-event');
                if (selectedEvent) {
                    selectedEvent.value = eventId.toString();
                }
            })
            .catch(error => {
                console.error('Error fetching event data:', error);
                // If we can't fetch the event data, the event might not exist
                // Clear the selected event and disable all menu items
                localStorage.removeItem('selectedEventID');

                // Update the dropdown to show no selected event
                const selectedEvent = document.getElementById('selected-event');
                if (selectedEvent) {
                    selectedEvent.value = '';
                }

                // Show an error message
                alert('Det valda evenemanget kunde inte hittas. Välj ett annat evenemang.');
            });
    }

    // Helper function to update menu text based on event type
    function updateMenuTextForEvent(event) {
        // Update finish times menu text based on event type
        const finishTimesText = document.getElementById('finish-times-text');
        const finishTimesDisabledText = document.getElementById('finish-times-disabled-text');

        if (event.entypsegling) {
            if (finishTimesText) finishTimesText.textContent = 'Placeringar';
            if (finishTimesDisabledText) finishTimesDisabledText.textContent = 'Placeringar';
        } else {
            // Template now handles this conditionally, no need to update dynamically
            // Template now handles this conditionally, no need to update dynamically
        }
    }

    // Helper function to enable menu items with event data
    function enableMenuItemsWithEventData(eventId, event) {
        // Update menu text based on event type
        updateMenuTextForEvent(event);

        // Get all disabled menu items
        const menuItems = document.querySelectorAll('.list-group-item.disabled');
        menuItems.forEach(item => {
            // Skip the jaktstart item if jaktstart is not enabled for this event
            const isJaktstartItem = item.textContent.trim().includes('Jaktstartstider');
            if (isJaktstartItem && !event.jaktstart) {
                return;
            }

            // Replace disabled spans with active links
            if (item.tagName === 'SPAN') {
                const icon = item.querySelector('i').outerHTML;
                let text = item.textContent.trim();

                // For finish times, use the appropriate text based on event type
                if (item.id === 'finish-times-disabled') {
                    // Template now handles this conditionally
                }

                // Get the href template and replace %eventId% with the actual event ID
                const hrefTemplate = item.getAttribute('data-href-template');
                const href = hrefTemplate ? hrefTemplate.replace('%eventId%', eventId) : `/events/${eventId}`;

                // Update the data-event-id attribute with the current event ID
                item.setAttribute('data-event-id', eventId);

                const link = document.createElement('a');
                link.href = href;
                link.className = 'list-group-item list-group-item-action';
                link.innerHTML = icon + ' ' + text;

                // Preserve the ID for finish times link
                if (item.id === 'finish-times-disabled') {
                    link.id = 'finish-times-link';
                    link.innerHTML = icon + ' <span id="finish-times-text">' + text + '</span>';
                }

                item.parentNode.replaceChild(link, item);
            } else {
                // Just remove the disabled class if it's already a link
                item.classList.remove('disabled');
            }
        });

        // If we're on the events page, add a parameter to the URL to maintain the selected event
        if (window.location.pathname === '/events' && !window.location.search.includes('selectedEventID')) {
            // Add the parameter without reloading the page
            const url = new URL(window.location);
            url.searchParams.set('selectedEventID', eventId);
            window.history.replaceState({}, '', url);
        }
    }

    // Helper function to force enable menu items without API verification
    function forceEnableMenuItems(eventId, hasJaktstart, isEntypsegling) {
        // Update menu text if we know the event type
        if (isEntypsegling !== undefined) {
            updateMenuTextForEvent({ entypsegling: isEntypsegling });
        }

        // Get all disabled menu items
        const menuItems = document.querySelectorAll('.list-group-item.disabled');
        menuItems.forEach(item => {
            // Skip the jaktstart item if jaktstart is not enabled for this event
            const isJaktstartItem = item.textContent.trim().includes('Jaktstartstider');
            if (isJaktstartItem && !hasJaktstart) {
                return;
            }

            // Replace disabled spans with active links
            if (item.tagName === 'SPAN') {
                const icon = item.querySelector('i').outerHTML;
                let text = item.textContent.trim();

                // For finish times, use the appropriate text based on event type
                if (item.id === 'finish-times-disabled' && isEntypsegling !== undefined) {
                    // Template now handles this conditionally
                }

                // Get the href template and replace %eventId% with the actual event ID
                const hrefTemplate = item.getAttribute('data-href-template');
                const href = hrefTemplate ? hrefTemplate.replace('%eventId%', eventId) : `/events/${eventId}`;

                // Update the data-event-id attribute with the current event ID
                item.setAttribute('data-event-id', eventId);

                const link = document.createElement('a');
                link.href = href;
                link.className = 'list-group-item list-group-item-action';
                link.innerHTML = icon + ' ' + text;

                // Preserve the ID for finish times link
                if (item.id === 'finish-times-disabled') {
                    link.id = 'finish-times-link';
                    link.innerHTML = icon + ' <span id="finish-times-text">' + text + '</span>';
                }

                item.parentNode.replaceChild(link, item);
            } else {
                // Just remove the disabled class if it's already a link
                item.classList.remove('disabled');
            }
        });

        // If we're on the events page, add a parameter to the URL to maintain the selected event
        if (window.location.pathname === '/events' && !window.location.search.includes('selectedEventID')) {
            // Add the parameter without reloading the page
            const url = new URL(window.location);
            url.searchParams.set('selectedEventID', eventId);
            window.history.replaceState({}, '', url);
        }
    }

    // Helper function to verify an event exists and then enable menu items
    function verifyAndEnableMenuItems(eventId) {
        if (!eventId) return;

        // Verify the event exists by fetching it from the API
        fetch(`/api/events/${eventId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch event data');
                }
                return response.json();
            })
            .then(event => {
                // Now we have the event data, force enable the menu items
                forceEnableMenuItems(eventId, event.jaktstart);
            })
            .catch(error => {
                console.error('Error fetching event data:', error);
                // If we can't fetch the event data, the event might not exist
                // Clear the selected event
                localStorage.removeItem('selectedEventID');

                // Update the dropdown to show no selected event
                const selectedEvent = document.getElementById('selected-event');
                if (selectedEvent) {
                    selectedEvent.value = '';
                }

                // Show an error message
                alert('Det valda evenemanget kunde inte hittas. Välj ett annat evenemang.');
            });
    }

    // Function to refresh the dropdown with the latest events
    function refreshEventDropdown() {
        const selectedEvent = document.getElementById('selected-event');
        if (!selectedEvent) return;

        // Get current selection
        const currentSelection = selectedEvent.value;

        // Fetch the latest events from the server
        fetch('/api/events')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch events');
                }
                return response.json();
            })
            .then(events => {
                console.log('Refreshing dropdown with events:', events);

                // Clear all options except the first one (Välj tävling...)
                while (selectedEvent.options.length > 1) {
                    selectedEvent.remove(1);
                }

                // Add the updated events to the dropdown
                events.forEach(event => {
                    const option = document.createElement('option');
                    option.value = event.id.toString();
                    option.text = `${event.namn} (${new Date(event.datum).toISOString().split('T')[0]})`;

                    // If this was the selected event, select it
                    if (currentSelection && currentSelection === event.id.toString()) {
                        option.selected = true;
                    }

                    selectedEvent.add(option);
                });

                // If the current selection is not in the updated list, clear it
                let selectionExists = false;
                for (let i = 0; i < selectedEvent.options.length; i++) {
                    if (selectedEvent.options[i].value === currentSelection) {
                        selectionExists = true;
                        break;
                    }
                }

                if (!selectionExists && currentSelection) {
                    localStorage.removeItem('selectedEventID');
                    selectedEvent.value = '';
                }
            })
            .catch(error => {
                console.error('Error refreshing events dropdown:', error);
            });
    }
</script>
