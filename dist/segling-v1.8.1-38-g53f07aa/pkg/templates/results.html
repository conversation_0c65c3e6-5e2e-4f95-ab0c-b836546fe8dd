{{ template "header.html" . }}

<div class="container mt-4">
    <h1>Resultat - {{ .event.Namn }}</h1>

    <div class="row">
        <div class="col-md-3">
            {{ template "event_nav.html" . }}
        </div>
        <div class="col-md-9">
            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        <strong>Datum:</strong> {{ .event.Datum.Format "2006-01-02" }} |
                        <strong>Starttid:</strong> {{ .event.Starttid }} |
                        <strong>Vind:</strong> {{ .event.Vind }} m/s |
                        <strong>Banlängd:</strong> {{ .event.Banlangd }} nm
                    </p>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>Resultat</h2>
                            {{ if .isMultiHeat }}
                                <p class="text-muted mb-0">Sammanlagda resultat från {{ len .heats }} deltävlingar (sorterat efter totala poäng)</p>
                            {{ else }}
                                <p class="text-muted mb-0">Sorterat efter korrigerad tid</p>
                            {{ end }}
                            <!-- TEMPORARILY DISABLED: Lock status display -->
                            <!-- {{ if .event.Locked }}
                                <span class="badge bg-danger">Låst</span>
                                <small class="text-muted">Tävlingen är låst och resultaten är finala</small>
                            {{ else }}
                                <span class="badge bg-success">Öppen</span>
                                <small class="text-muted">Tävlingen är öppen för ändringar</small>
                            {{ end }} -->
                        </div>
                        <div>
                            <!-- TEMPORARILY DISABLED: Lock/unlock buttons -->
                            <!-- {{ if .event.Locked }}
                                <form action="/events/{{ .event.ID }}/unlock" method="post" class="d-inline">
                                    <button type="submit" class="btn btn-warning btn-sm" onclick="return confirm('Är du säker på att du vill låsa upp tävlingen? Detta kommer att ta bort de sparade resultaten.')">
                                        <i class="bi bi-unlock"></i> Lås upp tävling
                                    </button>
                                </form>
                            {{ else }}
                                <form action="/events/{{ .event.ID }}/lock" method="post" class="d-inline">
                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Är du säker på att du vill låsa tävlingen? Detta kommer att spara resultaten permanent. Båtdata kan fortfarande uppdateras, men det påverkar inte de sparade resultaten.')">
                                        <i class="bi bi-lock"></i> Lås tävling
                                    </button>
                                </form>
                            {{ end }} -->
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {{ if or .results .savedResults .totalResults }}
                    <div class="mb-3">
                        <a id="print-btn" href="/events/{{ .event.ID }}/results/print{{ if .isMultiHeat }}?heat=overall{{ end }}" class="btn btn-outline-secondary" target="_blank">
                            <i class="bi bi-printer"></i> Utskriftsversion
                        </a>
                        <a id="csv-export-btn" href="/events/{{ .event.ID }}/results/csv{{ if .isMultiHeat }}?heat=overall{{ end }}" class="btn btn-outline-success">
                            <i class="bi bi-file-earmark-spreadsheet"></i> Exportera CSV
                        </a>
                        {{ if .googleDriveEnabled }}
                        <button id="google-drive-export-btn" class="btn btn-outline-info" data-event-id="{{ .event.ID }}" data-heat="{{ if .isMultiHeat }}overall{{ end }}">
                            <i class="bi bi-google"></i> Exportera till Google Drive
                        </button>
                        {{ end }}
                        {{ if .githubPagesEnabled }}
                        <button id="publish-btn" class="btn btn-outline-primary" data-event-id="{{ .event.ID }}" data-heat="{{ if .isMultiHeat }}overall{{ end }}">
                            <i class="bi bi-github"></i> Publicera till GitHub Pages
                        </button>
                        <button id="delete-published-btn" class="btn btn-outline-danger {{ if not .isPublished }}d-none{{ end }}" data-event-id="{{ .event.ID }}" data-filename="{{ .publishedFilename }}">
                            <i class="bi bi-trash"></i> Ta bort publicerad sida
                        </button>
                        <div id="github-pages-link" class="{{ if not .isPublished }}d-none{{ end }} mt-2">
                            <a href="#" onclick="openPublishedPage('{{ .publishedUrl }}'); return false;" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-box-arrow-up-right"></i> Visa publicerad sida
                            </a>
                        </div>
                        {{ end }}
                    </div>
                    {{ if .usingSavedResults }}
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> Visar sparade resultat från låst tävling. Ändringar i båtdata påverkar inte dessa resultat.
                        </div>
                    {{ end }}

                    {{ if .isMultiHeat }}
                        <!-- Multi-heat results with tabs -->
                        <ul class="nav nav-tabs" id="resultsTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="overall-tab" data-bs-toggle="tab" data-bs-target="#overall" type="button" role="tab" aria-controls="overall" aria-selected="true">
                                    Sammanlagt
                                </button>
                            </li>
                            {{ range $index, $heat := .heats }}
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="heat-{{ $heat.ID }}-tab" data-bs-toggle="tab" data-bs-target="#heat-{{ $heat.ID }}" type="button" role="tab" aria-controls="heat-{{ $heat.ID }}" aria-selected="false">
                                    {{ $heat.Name }}
                                </button>
                            </li>
                            {{ end }}
                        </ul>

                        <div class="tab-content" id="resultsTabContent">
                            <!-- Overall Results Tab -->
                            <div class="tab-pane fade show active" id="overall" role="tabpanel" aria-labelledby="overall-tab">
                                <div class="table-responsive mt-3">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Placering</th>
                                                <th>Seglare</th>
                                                {{ if .event.Entypsegling }}
                                                <th>Segelnummer</th>
                                                {{ else }}
                                                <th>Båt</th>
                                                <th>SRS-värde</th>
                                                {{ end }}
                                                <th>Besättning</th>
                                                <th>Totala poäng</th>
                                                {{ range .heats }}
                                                <th>{{ .Name }}</th>
                                                {{ end }}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {{ range .totalResults }}
                                            <tr>
                                                <td>{{ .TotalPosition }}</td>
                                                <td>
                                                    {{ .Sailor.Namn }}
                                                    {{ if .Sailor.Klubb }}
                                                        <br>
                                                        <small class="text-muted">{{ .Sailor.Klubb }}</small>
                                                    {{ end }}
                                                </td>
                                                {{ if $.event.Entypsegling }}
                                                <td>{{ .EventParticipant.PersonalNumber }}</td>
                                                {{ else }}
                                                <td>
                                                    {{ .Boat.Namn }} ({{ .Boat.Battyp }})
                                                    {{ if or .Boat.Segelnummer .Boat.Nationality }}
                                                        <br>
                                                        <small class="text-muted">
                                                            {{ if and .Boat.Nationality .Boat.Segelnummer }}
                                                                {{ .Boat.Nationality }}-{{ .Boat.Segelnummer }}
                                                            {{ else if .Boat.Segelnummer }}
                                                                {{ .Boat.Segelnummer }}
                                                            {{ else if .Boat.Nationality }}
                                                                {{ .Boat.Nationality }}
                                                            {{ end }}
                                                        </small>
                                                    {{ end }}
                                                </td>
                                                <td>
                                                    {{ if .EventParticipant.UseCustomSRSValue }}
                                                        {{ .EventParticipant.CustomSRSValue }} (anpassad)
                                                    {{ else }}
                                                        {{ .EventParticipant.SelectedSRSValue }}
                                                        {{ if eq .EventParticipant.SRSType "srs" }}
                                                            (SRS)
                                                        {{ else if eq .EventParticipant.SRSType "srs_utan_undanvindsegel" }}
                                                            (SRS utan undanvindsegel)
                                                        {{ else if eq .EventParticipant.SRSType "srs_shorthanded" }}
                                                            (SRS S/H)
                                                        {{ else if eq .EventParticipant.SRSType "srs_shorthanded_utan_undanvindsegel" }}
                                                            (SRS S/H utan undanvindsegel)
                                                        {{ end }}
                                                    {{ end }}
                                                    {{ if .Boat.MatbrevsNummer }}
                                                        <br>
                                                        <small class="text-muted">Mätbrev: {{ .Boat.MatbrevsNummer }}</small>
                                                    {{ end }}
                                                </td>
                                                {{ end }}
                                                <td>{{ add .EventParticipant.CrewCount 1 }}</td>
                                                <td><strong>{{ printf "%.1f" .TotalPoints }}</strong></td>
                                                {{ $currentResult := . }}
                                                {{ range $heat := $.heats }}
                                                    {{ $found := false }}
                                                    {{ range $currentResult.HeatResults }}
                                                        {{ if eq .Heat.ID $heat.ID }}
                                                            {{ $found = true }}
                                                            <td>
                                                                {{ if .DNS }}
                                                                    <span class="badge bg-warning">DNS ({{ printf "%.1f" .Points }}p)</span>
                                                                {{ else if .DNF }}
                                                                    <span class="badge bg-danger">DNF ({{ printf "%.1f" .Points }}p)</span>
                                                                {{ else }}
                                                                    {{ .Position }} ({{ printf "%.1f" .Points }}p)
                                                                {{ end }}
                                                            </td>
                                                        {{ end }}
                                                    {{ end }}
                                                    {{ if not $found }}
                                                        <td>-</td>
                                                    {{ end }}
                                                {{ end }}
                                            </tr>
                                            {{ end }}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Individual Heat Tabs -->
                            {{ range $index, $heat := .heats }}
                            {{ $heatResults := index $.heatResultsMap $heat.ID }}
                            <div class="tab-pane fade" id="heat-{{ $heat.ID }}" role="tabpanel" aria-labelledby="heat-{{ $heat.ID }}-tab">
                                <div class="table-responsive mt-3">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Plats</th>
                                                <th>Seglare</th>
                                                <th>Klubb</th>
                                                {{ if $.event.Entypsegling }}
                                                <th>Segelnummer</th>
                                                <th>Besättning</th>
                                                <th>Poäng</th>
                                                {{ else }}
                                                <th>Båt</th>
                                                <th>Båttyp</th>
                                                <th>Segelnr</th>
                                                <th>SRS</th>
                                                <th>Besättning</th>
                                                {{ if $.event.Jaktstart }}
                                                <th>Starttid</th>
                                                {{ end }}
                                                <th>Måltid</th>
                                                <th>Seglad tid</th>
                                                <th>Korrigerad tid</th>
                                                <th>Efter föregående</th>
                                                <th>Efter vinnare</th>
                                                <th>Poäng</th>
                                                {{ end }}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {{ range $heatResults }}
                                            <tr>
                                                <td>
                                                    {{ if .DNS }}
                                                        <span class="badge bg-warning">DNS</span>
                                                    {{ else if .DNF }}
                                                        <span class="badge bg-danger">DNF</span>
                                                    {{ else }}
                                                        {{ .Position }}
                                                    {{ end }}
                                                </td>
                                                <td>{{ .Sailor.Namn }}</td>
                                                <td>{{ .Sailor.Klubb }}</td>
                                                {{ if $.event.Entypsegling }}
                                                <td>{{ .EventParticipant.PersonalNumber }}</td>
                                                <td>{{ .TotalPersons }}</td>
                                                <td>{{ printf "%.1f" .Points }}</td>
                                                {{ else }}
                                                <td>{{ .Boat.Namn }}</td>
                                                <td>{{ .Boat.Battyp }}</td>
                                                <td>
                                                    {{ if .Boat.Nationality }}{{ .Boat.Nationality }}-{{ end }}{{ .Boat.Segelnummer }}
                                                </td>
                                                <td>
                                                    {{ if .EventParticipant.UseCustomSRSValue }}
                                                        {{ .EventParticipant.CustomSRSValue }} (anpassad)
                                                    {{ else }}
                                                        {{ printf "%.2f" .EventParticipant.SelectedSRSValue }}
                                                        {{ if eq .EventParticipant.SRSType "srs" }}
                                                            (SRS)
                                                        {{ else if eq .EventParticipant.SRSType "srs_utan_undanvindsegel" }}
                                                            (SRS utan undanvindsegel)
                                                        {{ else if eq .EventParticipant.SRSType "srs_shorthanded" }}
                                                            (SRS S/H)
                                                        {{ else if eq .EventParticipant.SRSType "srs_shorthanded_utan_undanvindsegel" }}
                                                            (SRS S/H utan undanvindsegel)
                                                        {{ end }}
                                                    {{ end }}
                                                    {{ if .Boat.MatbrevsNummer }}
                                                        <br>
                                                        <small class="text-muted">Mätbrev: {{ .Boat.MatbrevsNummer }}</small>
                                                    {{ end }}
                                                </td>
                                                <td>{{ .TotalPersons }}</td>
                                                {{ if $.event.Jaktstart }}
                                                <td>{{ .StartTime }}</td>
                                                {{ end }}
                                                <td>{{ .FinishTime }}</td>
                                                <td>{{ .ElapsedTime }}</td>
                                                <td>{{ .CorrectedTime }}</td>
                                                <td>{{ .TimeToPrevious }}</td>
                                                <td>{{ .TimeToWinner }}</td>
                                                <td>{{ .Points }}</td>
                                                {{ end }}
                                            </tr>
                                            {{ end }}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {{ end }}
                        </div>
                    {{ else }}
                        <!-- Single heat results table -->
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Placering</th>
                                        <th>Seglare</th>
                                        {{ if .event.Entypsegling }}
                                        <th>Segelnummer</th>
                                        <th>Besättning</th>
                                        <th>Poäng</th>
                                        {{ else }}
                                        <th>Båt</th>
                                        <th>SRS-värde</th>
                                        <th>Besättning</th>
                                        <th>Starttid</th>
                                        <th>Måltid</th>
                                        <th>Seglad tid</th>
                                        <th>Korrigerad tid</th>
                                        <th>Efter föregående</th>
                                        <th>Efter vinnare</th>
                                        {{ end }}
                                    </tr>
                                </thead>
                                <tbody>
                                    {{ if .event.Entypsegling }}
                                        {{ range $result := .results }}
                                        <tr>
                                            <td>{{ $result.Position }}</td>
                                            <td>
                                                {{ $result.Sailor.Namn }}
                                                {{ if $result.Sailor.Klubb }}
                                                    <br>
                                                    <small class="text-muted">{{ $result.Sailor.Klubb }}</small>
                                                {{ end }}
                                            </td>
                                            <td>{{ $result.EventParticipant.PersonalNumber }}</td>
                                            <td>{{ add $result.EventParticipant.CrewCount 1 }}</td>
                                            <td>
                                                {{ if $result.DNS }}
                                                    <span class="badge bg-warning">DNS ({{ printf "%.1f" $result.Points }}p)</span>
                                                {{ else if $result.DNF }}
                                                    <span class="badge bg-danger">DNF ({{ printf "%.1f" $result.Points }}p)</span>
                                                {{ else }}
                                                    {{ printf "%.1f" $result.Points }}p
                                                {{ end }}
                                            </td>
                                        </tr>
                                        {{ end }}
                                    {{ else }}
                                        {{ if .usingSavedResults }}
                                            {{ range $result := .savedResults }}
                                            <tr>
                                                <td>{{ $result.Position }}</td>
                                                <td>
                                                    {{ $result.SailorName }}
                                                    {{ if $result.SailorClub }}
                                                        <br>
                                                        <small class="text-muted">{{ $result.SailorClub }}</small>
                                                    {{ end }}
                                                </td>
                                                <td>
                                                    {{ $result.BoatName }} ({{ $result.BoatType }})
                                                    {{ if or $result.Segelnummer $result.Nationality }}
                                                        <br>
                                                        <small class="text-muted">
                                                            {{ if and $result.Nationality $result.Segelnummer }}
                                                                {{ $result.Nationality }}-{{ $result.Segelnummer }}
                                                            {{ else if $result.Segelnummer }}
                                                                {{ $result.Segelnummer }}
                                                            {{ else if $result.Nationality }}
                                                                {{ $result.Nationality }}
                                                            {{ end }}
                                                        </small>
                                                    {{ end }}
                                                </td>
                                                <td>
                                                    {{ $result.SRSValue }}
                                                    {{ if $result.UseCustomSRSValue }}
                                                        (anpassad)
                                                    {{ else if eq $result.SRSType "srs" }}
                                                        (SRS)
                                                    {{ else if eq $result.SRSType "srs_utan_undanvindsegel" }}
                                                        (SRS utan undanvindsegel)
                                                    {{ else if eq $result.SRSType "srs_shorthanded" }}
                                                        (SRS S/H)
                                                    {{ else if eq $result.SRSType "srs_shorthanded_utan_undanvindsegel" }}
                                                        (SRS S/H utan undanvindsegel)
                                                    {{ end }}
                                                    {{ if $result.MatbrevsNummer }}
                                                        <br>
                                                        <small class="text-muted">Mätbrev: {{ $result.MatbrevsNummer }}</small>
                                                    {{ end }}
                                                </td>
                                                <td>{{ add $result.CrewCount 1 }}</td>
                                                <td>{{ if $result.DNS }}-{{ else }}{{ $result.StartTime }}{{ end }}</td>
                                                <td>{{ if $result.DNS }}<span class="badge bg-warning">DNS</span>{{ else if $result.DNF }}<span class="badge bg-danger">DNF</span>{{ else }}{{ $result.FinishTime }}{{ end }}</td>
                                                <td>{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.ElapsedTime }}{{ end }}</td>
                                                <td>{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.CorrectedTime }}{{ end }}</td>
                                                <td>{{ if or $result.DNS $result.DNF }}-{{ else if $result.TimeToPrevious }}{{ $result.TimeToPrevious }}{{ else }}-{{ end }}</td>
                                                <td>{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.TimeToWinner }}{{ end }}</td>
                                            </tr>
                                            {{ end }}
                                        {{ else }}
                                            {{ range $index, $result := .results }}
                                            <tr>
                                                <td>{{ add $index 1 }}</td>
                                                <td>
                                                    {{ $result.Sailor.Namn }}
                                                    {{ if $result.Sailor.Klubb }}
                                                        <br>
                                                        <small class="text-muted">{{ $result.Sailor.Klubb }}</small>
                                                    {{ end }}
                                                </td>
                                                <td>
                                                    {{ $result.Boat.Namn }} ({{ $result.Boat.Battyp }})
                                                    {{ if or $result.Boat.Segelnummer $result.Boat.Nationality }}
                                                        <br>
                                                        <small class="text-muted">
                                                            {{ if and $result.Boat.Nationality $result.Boat.Segelnummer }}
                                                                {{ $result.Boat.Nationality }}-{{ $result.Boat.Segelnummer }}
                                                            {{ else if $result.Boat.Segelnummer }}
                                                                {{ $result.Boat.Segelnummer }}
                                                            {{ else if $result.Boat.Nationality }}
                                                                {{ $result.Boat.Nationality }}
                                                            {{ end }}
                                                        </small>
                                                    {{ end }}
                                                </td>
                                                <td>
                                                    {{ if $result.UseCustomSRSValue }}
                                                        {{ $result.CustomSRSValue }} (anpassad)
                                                    {{ else }}
                                                        {{ $result.SelectedSRSValue }}
                                                        {{ if eq $result.SRSType "srs" }}
                                                            (SRS)
                                                        {{ else if eq $result.SRSType "srs_utan_undanvindsegel" }}
                                                            (SRS utan undanvindsegel)
                                                        {{ else if eq $result.SRSType "srs_shorthanded" }}
                                                            (SRS S/H)
                                                        {{ else if eq $result.SRSType "srs_shorthanded_utan_undanvindsegel" }}
                                                            (SRS S/H utan undanvindsegel)
                                                        {{ end }}
                                                    {{ end }}
                                                    {{ if $result.Boat.MatbrevsNummer }}
                                                        <br>
                                                        <small class="text-muted">Mätbrev: {{ $result.Boat.MatbrevsNummer }}</small>
                                                    {{ end }}
                                                </td>
                                                <td>{{ $result.TotalPersons }}</td>
                                                <td>{{ if $result.DNS }}-{{ else }}{{ $result.StartTime }}{{ end }}</td>
                                                <td>{{ if $result.DNS }}<span class="badge bg-warning">DNS</span>{{ else if $result.DNF }}<span class="badge bg-danger">DNF</span>{{ else }}{{ $result.FinishTime }}{{ end }}</td>
                                                <td>{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.ElapsedTime }}{{ end }}</td>
                                                <td>{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.CorrectedTime }}{{ end }}</td>
                                                <td>{{ if or $result.DNS $result.DNF }}-{{ else if $result.TimeToPrevious }}{{ $result.TimeToPrevious }}{{ else }}-{{ end }}</td>
                                                <td>{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.TimeToWinner }}{{ end }}</td>
                                            </tr>
                                            {{ end }}
                                        {{ end }}
                                    {{ end }}
                                </tbody>
                            </table>
                        </div>
                    {{ end }}
                    {{ else }}
                    {{ if .event.Entypsegling }}
                    <p>Inga resultat hittades. Kontrollera att placeringar har registrerats.</p>
                    <div class="mt-3">
                        <a href="/events/{{ .event.ID }}/finish-times" class="btn btn-primary">Registrera placeringar</a>
                    </div>
                    {{ else }}
                    <p>Inga resultat hittades. Kontrollera att måltider har registrerats.</p>
                    <div class="mt-3">
                        <a href="/events/{{ .event.ID }}/finish-times" class="btn btn-primary">Registrera måltider</a>
                    </div>
                    {{ end }}
                    {{ end }}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Track current heat selection for exports
let currentHeat = '{{ if .isMultiHeat }}overall{{ end }}';
const eventId = '{{ .event.ID }}';
const isMultiHeat = {{ .isMultiHeat }};

// Update export URLs when tab changes
function updateExportUrls(heat) {
    currentHeat = heat;

    // Update print button
    const printBtn = document.getElementById('print-btn');
    if (printBtn) {
        if (isMultiHeat) {
            printBtn.href = `/events/${eventId}/results/print?heat=${heat}`;
        } else {
            printBtn.href = `/events/${eventId}/results/print`;
        }
    }

    // Update CSV export button
    const csvBtn = document.getElementById('csv-export-btn');
    if (csvBtn) {
        if (isMultiHeat) {
            csvBtn.href = `/events/${eventId}/results/csv?heat=${heat}`;
        } else {
            csvBtn.href = `/events/${eventId}/results/csv`;
        }
    }

    // Update Google Drive export button data
    const googleDriveBtn = document.getElementById('google-drive-export-btn');
    if (googleDriveBtn) {
        googleDriveBtn.setAttribute('data-heat', heat);
    }

    // Update GitHub Pages publish button data
    const publishBtn = document.getElementById('publish-btn');
    if (publishBtn) {
        publishBtn.setAttribute('data-heat', heat);
    }
}

// Listen for tab changes
{{ if .isMultiHeat }}
document.addEventListener('DOMContentLoaded', function() {
    // Listen for Bootstrap tab events
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(event) {
            const targetId = event.target.getAttribute('data-bs-target');
            if (targetId === '#overall') {
                updateExportUrls('overall');
            } else {
                // Extract heat ID from target (e.g., #heat-123 -> 123)
                const heatId = targetId.replace('#heat-', '');
                updateExportUrls(heatId);
            }
        });
    });
});
{{ end }}

// Handle Google Drive export
{{ if .googleDriveEnabled }}
document.getElementById('google-drive-export-btn').addEventListener('click', function() {
    const eventId = this.getAttribute('data-event-id');
    const heat = this.getAttribute('data-heat');
    const button = this;

    // Disable button and show loading state
    button.disabled = true;
    button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Exporterar...';

    // Build URL with heat parameter
    let url = `/events/${eventId}/export-google-drive`;
    if (isMultiHeat && heat) {
        url += `?heat=${heat}`;
    }

    fetch(url, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`Resultat exporterade till Google Drive!\n\nFil: ${data.filename}\nURL: ${data.spreadsheet_url}`);
            // Open the spreadsheet in a new tab
            window.open(data.spreadsheet_url, '_blank');
        } else {
            alert('Fel vid export: ' + data.error);
        }
    })
    .catch(error => {
        alert('Fel vid export: ' + error);
    })
    .finally(() => {
        // Re-enable button
        button.disabled = false;
        button.innerHTML = '<i class="bi bi-google"></i> Exportera till Google Drive';
    });
});
{{ end }}

// Handle GitHub Pages publish
{{ if .githubPagesEnabled }}
document.getElementById('publish-btn').addEventListener('click', function() {
    const eventId = this.getAttribute('data-event-id');
    const heat = this.getAttribute('data-heat');
    const button = this;

    // Disable button and show loading state
    button.disabled = true;
    button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Publicerar...';

    // Build URL with heat parameter
    let url = `/events/${eventId}/publish-github-pages`;
    if (isMultiHeat && heat) {
        url += `?heat=${heat}`;
    }

    fetch(url, {
        method: 'POST'
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', [...response.headers.entries()]);

        // Check if response is ok
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Get the response text first to debug
        return response.text().then(text => {
            console.log('Response text:', text);
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('Failed to parse JSON:', e);
                console.error('Response text was:', text);
                throw new Error('Server returned invalid JSON: ' + text.substring(0, 100));
            }
        });
    })
    .then(data => {
        if (data.success) {
            alert(`Resultat publicerade till GitHub Pages!\n\nURL: ${data.url}`);

            // Show the delete button
            const deleteButton = document.getElementById('delete-published-btn');
            if (deleteButton) {
                deleteButton.classList.remove('d-none');
                // Extract the full path from the URL for deletion
                const urlParts = data.url.split('/');
                if (urlParts.length >= 6) {
                    const year = urlParts[urlParts.length - 3];
                    const competitionType = urlParts[urlParts.length - 2];
                    const filename = urlParts[urlParts.length - 1];
                    const fullPath = `${year}/${competitionType}/${filename}`;
                    deleteButton.setAttribute('data-filename', fullPath);
                } else {
                    deleteButton.setAttribute('data-filename', data.url.split('/').pop());
                }
                deleteButton.setAttribute('data-event-id', eventId);
            }

            // Show the link div with a waiting indicator initially
            const linkDiv = document.getElementById('github-pages-link');
            if (linkDiv) {
                linkDiv.classList.remove('d-none');
                // Show waiting indicator instead of active button
                linkDiv.innerHTML = `
                    <div class="waiting-message">
                        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                        <span class="ms-2">Väntar på att sidan ska bli tillgänglig...</span>
                    </div>
                `;
            }

            // Check if the page is available with the correct content
            if (data.url && data.checksum) {
                checkPageAvailability(data.url, data.checksum);
            }
        } else {
            alert('Fel vid publicering: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Publishing error:', error);
        alert('Fel vid publicering: ' + error);
    })
    .finally(() => {
        // Re-enable button
        button.disabled = false;
        button.innerHTML = '<i class="bi bi-github"></i> Publicera till GitHub Pages';
    });
});
{{ end }}
</script>

{{ template "footer.html" . }}
