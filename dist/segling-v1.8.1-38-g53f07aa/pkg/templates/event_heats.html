{{template "header.html" .}}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            {{template "event_nav.html" .}}
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2>Deltävlingar - {{.event.Namn}}</h2>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addHeatModal">
                        <i class="fas fa-plus"></i> Lägg till deltävling
                    </button>
                </div>
                <div class="card-body">
                    {{if .heats}}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Nummer</th>
                                        <th>Namn</th>
                                        {{ if not .event.Entypsegling }}
                                        <th>Starttid</th>
                                        {{ end }}
                                        <th>Skapad</th>
                                        <th><PERSON>t<PERSON><PERSON><PERSON>r</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {{range .heats}}
                                    <tr>
                                        <td>{{.HeatNumber}}</td>
                                        <td>{{.Name}}</td>
                                        {{ if not $.event.Entypsegling }}
                                        <td>
                                            <input type="time"
                                                   class="form-control form-control-sm heat-start-time"
                                                   value="{{.StartTime}}"
                                                   data-heat-id="{{.ID}}"
                                                   style="width: 120px;">
                                        </td>
                                        {{ end }}
                                        <td>{{.CreatedAt.Format "2006-01-02 15:04"}}</td>
                                        <td>
                                            {{ if $.event.Entypsegling }}
                                            <a href="/events/{{$.event.ID}}/finish-times?heat={{.ID}}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-sort-numeric-down"></i> Placeringar
                                            </a>
                                            {{ else }}
                                            <a href="/events/{{$.event.ID}}/finish-times?heat={{.ID}}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-clock"></i> Måltider
                                            </a>
                                            {{ end }}
                                            <a href="/heats/{{.ID}}/results" class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-trophy"></i> Resultat
                                            </a>
                                            {{if gt (len $.heats) 1}}
                                            <form method="POST" action="/heats/{{.ID}}" style="display: inline;">
                                                <input type="hidden" name="_method" value="DELETE">
                                                <button type="submit" class="btn btn-sm btn-outline-danger"
                                                        onclick="return confirm('Är du säker på att du vill ta bort denna deltävling?')">
                                                    <i class="fas fa-trash"></i> Ta bort
                                                </button>
                                            </form>
                                            {{end}}
                                        </td>
                                    </tr>
                                    {{end}}
                                </tbody>
                            </table>
                        </div>
                    {{else}}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Inga deltävlingar hittades. Lägg till en deltävling för att komma igång.
                        </div>
                    {{end}}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Heat Modal -->
<div class="modal fade" id="addHeatModal" tabindex="-1" aria-labelledby="addHeatModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="/events/{{.event.ID}}/heats">
                <div class="modal-header">
                    <h5 class="modal-title" id="addHeatModalLabel">Lägg till deltävling</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="heat_number" class="form-label">Nummer</label>
                        <input type="number" class="form-control" id="heat_number" name="heat_number"
                               value="{{add (len .heats) 1}}" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label for="name" class="form-label">Namn</label>
                        <input type="text" class="form-control" id="name" name="name"
                               value="Heat {{add (len .heats) 1}}" required>
                    </div>
                    {{ if not .event.Entypsegling }}
                    <div class="mb-3">
                        <label for="start_time" class="form-label">Starttid</label>
                        <input type="time" class="form-control" id="start_time" name="start_time"
                               value="{{if .event.Starttid}}{{.event.Starttid}}{{else}}10:00{{end}}" required>
                        <div class="form-text">Starttid för denna deltävling</div>
                    </div>
                    {{ end }}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Avbryt</button>
                    <button type="submit" class="btn btn-primary">Lägg till</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle start time changes
    document.querySelectorAll('.heat-start-time').forEach(function(input) {
        input.addEventListener('change', function() {
            const heatId = this.getAttribute('data-heat-id');
            const startTime = this.value;

            if (!startTime) {
                return;
            }

            // Create form data
            const formData = new FormData();
            formData.append('start_time', startTime);

            // Send update request
            fetch(`/heats/${heatId}/start-time`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert('Fel vid uppdatering av starttid: ' + data.error);
                    // Revert the input value
                    this.value = this.defaultValue;
                } else {
                    // Show success feedback
                    this.style.backgroundColor = '#d4edda';
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                    }, 1000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Fel vid uppdatering av starttid');
                // Revert the input value
                this.value = this.defaultValue;
            });
        });
    });
});
</script>

{{template "footer.html" .}}
