#!/bin/bash
# Script to generate client certificates for mutual TLS authentication

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print header
echo -e "${GREEN}=== Generating Client Certificates for Mutual TLS Authentication ===${NC}"

# Check if OpenSSL is installed
if ! command -v openssl &> /dev/null; then
    echo -e "${RED}Error: OpenSSL is not installed. Please install OpenSSL to generate certificates.${NC}"
    exit 1
fi

# Define variables
CERT_DIR="certs"
CLIENT_DIR="${CERT_DIR}/client"
CA_KEY="${CLIENT_DIR}/ca.key"
CA_CERT="${CLIENT_DIR}/ca.crt"
CLIENT_KEY="${CLIENT_DIR}/client.key"
CLIENT_CSR="${CLIENT_DIR}/client.csr"
CLIENT_CERT="${CLIENT_DIR}/client.crt"
CLIENT_P12="${CLIENT_DIR}/client.p12"
CONFIG_FILE="${CLIENT_DIR}/openssl.cnf"
DAYS=3650  # 10 years validity
CLIENT_NAME="segling-client"

# Create directories if they don't exist
echo -e "${YELLOW}Creating certificate directories...${NC}"
mkdir -p "${CLIENT_DIR}"

# Create OpenSSL config file for client certificates
echo -e "${YELLOW}Creating OpenSSL configuration...${NC}"
cat > "${CONFIG_FILE}" << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
CN = ${CLIENT_NAME}
O = Segling Client
OU = Development

[v3_req]
basicConstraints = CA:FALSE
keyUsage = digitalSignature, keyEncipherment
extendedKeyUsage = clientAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

# Step 1: Generate Client CA key and certificate
echo -e "${YELLOW}Generating Client CA key and certificate...${NC}"
openssl genrsa -out "${CA_KEY}" 4096
openssl req -x509 -new -nodes -key "${CA_KEY}" -sha256 -days "${DAYS}" \
    -out "${CA_CERT}" -subj "/CN=Segling Client CA/O=Segling/OU=Development"

# Step 2: Generate client key and CSR
echo -e "${YELLOW}Generating client key and CSR...${NC}"
openssl genrsa -out "${CLIENT_KEY}" 4096
openssl req -new -key "${CLIENT_KEY}" -out "${CLIENT_CSR}" -config "${CONFIG_FILE}"

# Step 3: Sign client certificate with our CA
echo -e "${YELLOW}Signing client certificate with our CA...${NC}"
openssl x509 -req -in "${CLIENT_CSR}" -CA "${CA_CERT}" -CAkey "${CA_KEY}" \
    -CAcreateserial -out "${CLIENT_CERT}" -days "${DAYS}" \
    -extensions v3_req -extfile "${CONFIG_FILE}"

# Step 4: Create PKCS#12 file for browser import
echo -e "${YELLOW}Creating PKCS#12 file for browser import...${NC}"
openssl pkcs12 -export -out "${CLIENT_P12}" -inkey "${CLIENT_KEY}" -in "${CLIENT_CERT}" \
    -certfile "${CA_CERT}" -passout pass:segling

# Check if generation was successful
if [ -f "${CLIENT_CERT}" ] && [ -f "${CLIENT_KEY}" ] && [ -f "${CA_CERT}" ]; then
    echo -e "${GREEN}Certificate generation successful!${NC}"
    echo -e "${GREEN}Client CA Certificate: ${CA_CERT}${NC}"
    echo -e "${GREEN}Client Certificate: ${CLIENT_CERT}${NC}"
    echo -e "${GREEN}Client Key: ${CLIENT_KEY}${NC}"
    echo -e "${GREEN}Client PKCS#12 (for browser): ${CLIENT_P12}${NC}"
    
    echo -e "${YELLOW}To use mutual TLS, run the server with:${NC}"
    echo -e "${YELLOW}  ./segling -tls -cert certs/cert.pem -key certs/key.pem -client-ca ${CA_CERT}${NC}"
    
    echo -e "${YELLOW}To import the client certificate to your browser:${NC}"
    echo -e "${YELLOW}  1. Double-click ${CLIENT_P12}${NC}"
    echo -e "${YELLOW}  2. Enter the password: segling${NC}"
    echo -e "${YELLOW}  3. Follow the browser-specific instructions to complete the import${NC}"
    
    chmod 600 "${CLIENT_KEY}"  # Set proper permissions for the private key
    chmod 600 "${CA_KEY}"      # Set proper permissions for the CA key
else
    echo -e "${RED}Certificate generation failed.${NC}"
    exit 1
fi
