#!/bin/bash
# Script to generate self-signed certificates for development

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print header
echo -e "${GREEN}=== Generating Self-Signed Certificates for Segling ===${NC}"

# Check if OpenSSL is installed
if ! command -v openssl &> /dev/null; then
    echo -e "${RED}Error: OpenSSL is not installed. Please install OpenSSL to generate certificates.${NC}"
    exit 1
fi

# Define variables
CERT_DIR="certs"
CERT_FILE="${CERT_DIR}/cert.pem"
KEY_FILE="${CERT_DIR}/key.pem"
CONFIG_FILE="${CERT_DIR}/openssl.cnf"
DAYS=3650  # 10 years validity
COMMON_NAME="localhost"

# Create certificates directory if it doesn't exist
echo -e "${YELLOW}Creating certificates directory...${NC}"
mkdir -p "${CERT_DIR}"

# Create OpenSSL config file with enhanced settings
echo -e "${YELLOW}Creating OpenSSL configuration...${NC}"
cat > "${CONFIG_FILE}" << EOF
[req]
distinguished_name = req_distinguished_name
x509_extensions = v3_req
prompt = no

[req_distinguished_name]
CN = ${COMMON_NAME}
O = Segling Development
OU = Development

[v3_req]
basicConstraints = critical, CA:true
keyUsage = critical, digitalSignature, keyEncipherment, keyCertSign
extendedKeyUsage = serverAuth, clientAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = ${COMMON_NAME}
DNS.2 = *.${COMMON_NAME}
DNS.3 = localhost.localdomain
DNS.4 = 127.0.0.1.nip.io
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

# Generate private key and certificate with enhanced settings
echo -e "${YELLOW}Generating private key and certificate...${NC}"
openssl req -x509 -newkey rsa:4096 -nodes \
    -keyout "${KEY_FILE}" \
    -out "${CERT_FILE}" \
    -days "${DAYS}" \
    -config "${CONFIG_FILE}" \
    -extensions v3_req

# Check if generation was successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Certificate generation successful!${NC}"
    echo -e "${GREEN}Certificate: ${CERT_FILE}${NC}"
    echo -e "${GREEN}Private key: ${KEY_FILE}${NC}"

    # Display certificate information
    echo -e "${YELLOW}Certificate details:${NC}"
    openssl x509 -in "${CERT_FILE}" -noout -text | grep -E "Subject:|Issuer:|DNS:|IP Address:"

    echo -e "${YELLOW}To use these certificates, run:${NC}"
    echo -e "${YELLOW}  ./segling -tls -cert ${CERT_FILE} -key ${KEY_FILE}${NC}"
    echo -e "${YELLOW}To import this certificate to your browser, run:${NC}"
    echo -e "${YELLOW}  ./scripts/export_cert_for_browser.sh${NC}"
    echo -e "${RED}NOTE: These are self-signed certificates and will generate browser warnings.${NC}"
    echo -e "${RED}      For production use, obtain certificates from a trusted certificate authority.${NC}"
    chmod 600 "${KEY_FILE}"  # Set proper permissions for the private key
else
    echo -e "${RED}Certificate generation failed.${NC}"
    exit 1
fi
