// Custom JavaScript for the Segling application

// Make the current page's nav link active and initialize theme
document.addEventListener('DOMContentLoaded', function() {
    // Set active nav link
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (currentPath === href || (href !== '/' && currentPath.startsWith(href))) {
            link.classList.add('active');
        }
    });

    // Theme toggle functionality
    initTheme();

    // Initialize delete button for GitHub Pages
    const deleteButton = document.getElementById('delete-published-btn');
    if (deleteButton) {
        console.log('Found delete button, adding event listener');
        deleteButton.addEventListener('click', function() {
            const eventId = this.getAttribute('data-event-id');
            const filename = this.getAttribute('data-filename');
            console.log('Delete button clicked with:', eventId, filename);
            deletePublishedPage(eventId, filename);
        });
    }

    // Initialize SRS data update form
    initSRSUpdateForm();
});

// Initialize theme based on localStorage or system preference
function initTheme() {
    const themeToggle = document.getElementById('theme-toggle');
    const themeIcon = document.getElementById('theme-icon');
    const htmlElement = document.documentElement;

    // Get the current theme (already set by the inline script in header)
    const currentTheme = htmlElement.getAttribute('data-bs-theme') || 'light';

    // Update the icon to match the current theme
    updateThemeIcon(currentTheme, themeIcon);

    // Add click event listener to theme toggle button
    themeToggle.addEventListener('click', function() {
        const currentTheme = htmlElement.getAttribute('data-bs-theme');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';

        // Update theme
        htmlElement.setAttribute('data-bs-theme', newTheme);
        updateThemeIcon(newTheme, themeIcon);
        localStorage.setItem('theme', newTheme);

        // Update theme-color meta tag for mobile browsers
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (metaThemeColor) {
            metaThemeColor.setAttribute('content', newTheme === 'dark' ? '#212529' : '#0d6efd');
        }
    });

    // Update theme-color meta tag on initial load
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor && currentTheme === 'dark') {
        metaThemeColor.setAttribute('content', '#212529');
    }
}

// Update the theme icon based on current theme
function updateThemeIcon(theme, iconElement) {
    if (theme === 'dark') {
        iconElement.classList.remove('bi-sun-fill');
        iconElement.classList.add('bi-moon-fill');
    } else {
        iconElement.classList.remove('bi-moon-fill');
        iconElement.classList.add('bi-sun-fill');
    }
}

// This function has been replaced by server-side checksum calculation

// Check if a published page is available with the correct content
function checkPageAvailability(url, expectedChecksum) {
    console.log(`Checking if page ${url} is available with checksum ${expectedChecksum}`);

    // Set a maximum number of attempts
    const maxAttempts = 150; // 150 attempts with 2-second intervals = up to 5 minutes of waiting
    let attempts = 0;

    // Start the polling
    const startTime = new Date();
    const checkInterval = setInterval(async () => {
        attempts++;

        // Calculate elapsed time
        const elapsedSeconds = Math.floor((new Date() - startTime) / 1000);
        const minutes = Math.floor(elapsedSeconds / 60);
        const seconds = elapsedSeconds % 60;
        const timeString = `${minutes}m ${seconds}s`;

        console.log(`Attempt ${attempts} to fetch ${url} (waited ${timeString})`);

        // Update the waiting message
        const linkDiv = document.getElementById('github-pages-link');
        if (linkDiv) {
            const waitingMessage = linkDiv.querySelector('.waiting-message');
            if (waitingMessage) {
                waitingMessage.textContent = `Väntar på att sidan ska bli tillgänglig... (${timeString})`;
            }
        }

        try {
            // Use the server-side proxy to check page availability
            // Add a timestamp to bypass browser caching
            const timestamp = Date.now();
            const proxyUrl = `/api/github-pages/check?url=${encodeURIComponent(url)}&checksum=${encodeURIComponent(expectedChecksum)}&_=${timestamp}`;

            console.log(`Checking URL via proxy: ${proxyUrl}`);
            const response = await fetch(proxyUrl, {
                cache: 'no-store', // Ensure browser doesn't cache this request
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });

            // Check if the request was successful
            if (response.ok) {
                // Parse the response
                const data = await response.json();
                console.log('Server response:', data);

                // Check if the page is available with the correct content
                if (data.success) {
                    console.log('Server confirms page is available with the correct content.');

                    // Stop polling
                    clearInterval(checkInterval);

                    // Update the UI
                    const linkDiv = document.getElementById('github-pages-link');
                    if (linkDiv) {
                        // Add a function to generate a URL with a fresh timestamp each time
                        linkDiv.innerHTML = `
                            <a href="#" onclick="openPublishedPage('${url}'); return false;" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-box-arrow-up-right"></i> Visa publicerad sida
                            </a>
                        `;

                        // Don't automatically open the page in a new tab with a timestamp
                        // The user can click the "Visa publicerad sida" button instead
                    }
                } else {
                    console.log('Server reports page is not yet updated:', data.message);
                }
            } else {
                console.log(`Error checking page availability. Status: ${response.status}`);
            }
        } catch (error) {
            console.error('Error checking page availability:', error);
        }

        // If we've reached the maximum number of attempts, stop polling
        if (attempts >= maxAttempts) {
            console.log('Maximum number of attempts reached. Stopping polling.');
            clearInterval(checkInterval);

            // Calculate total wait time
            const totalSeconds = Math.floor((new Date() - startTime) / 1000);
            const totalMinutes = Math.floor(totalSeconds / 60);
            const remainingSeconds = totalSeconds % 60;
            const totalTimeString = `${totalMinutes}m ${remainingSeconds}s`;

            // Update the UI to show that we couldn't verify the page
            const linkDiv = document.getElementById('github-pages-link');
            if (linkDiv) {
                linkDiv.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i> Kunde inte verifiera att sidan är tillgänglig efter ${totalTimeString}.
                        <p>GitHub Pages kan ta upp till 10 minuter att uppdatera. <a href="#" onclick="openPublishedPage('${url}'); return false;">Försök öppna sidan manuellt</a></p>
                    </div>
                `;
            }
        }
    }, 2000); // Check every 2 seconds
}

// Open a published page with a timestamp parameter to bypass caching
function openPublishedPage(url) {
    // Add a timestamp parameter to bypass caching
    const timestamp = Date.now();
    let urlWithTimestamp = url;

    // Add the timestamp parameter
    if (url.includes('?')) {
        urlWithTimestamp = `${url}&t=${timestamp}`;
    } else {
        urlWithTimestamp = `${url}?t=${timestamp}`;
    }

    console.log(`Opening published page with timestamp: ${urlWithTimestamp}`);
    window.open(urlWithTimestamp, '_blank');
}

// Copy GitHub Pages URL to clipboard
function copyGitHubPagesUrl() {
    const urlInput = document.getElementById('github-pages-url');
    const url = urlInput.value;

    // Use the modern Clipboard API if available
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(url)
            .then(() => showCopySuccess())
            .catch(err => {
                console.error('Could not copy text: ', err);
                // Fallback to the old method
                fallbackCopy();
            });
    } else {
        // Fallback for browsers that don't support the Clipboard API
        fallbackCopy();
    }

    function fallbackCopy() {
        urlInput.select();
        try {
            document.execCommand('copy');
            showCopySuccess();
        } catch (err) {
            console.error('Could not copy text: ', err);
        }
    }

    function showCopySuccess() {
        // Show a tooltip or some feedback
        const button = document.querySelector('button[onclick="copyGitHubPagesUrl()"]');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i> Kopierad';

        setTimeout(() => {
            button.innerHTML = originalText;
        }, 2000);
    }
}

// Delete a published page
function deletePublishedPage(eventId, filename) {
    console.log('deletePublishedPage called with:', eventId, filename);

    if (!filename) {
        console.error('No filename provided for deletion');
        alert('Fel: Inget filnamn angivet för borttagning');
        return;
    }

    if (confirm(`Är du säker på att du vill ta bort den publicerade sidan? Detta kan inte ångras.`)) {
        console.log('User confirmed deletion');

        // Get the button that triggered this event
        // Use the event target if available, otherwise try to find it by selector
        let deleteBtn = event && event.currentTarget;

        // If we don't have the button from the event, try to find it
        if (!deleteBtn) {
            // First try the specific button with ID
            deleteBtn = document.getElementById('delete-published-btn');

            // If not found, try to find by data-filename attribute
            if (!deleteBtn) {
                deleteBtn = document.querySelector(`button.github-pages-delete-btn[data-filename="${filename}"]`);
            }
        }

        console.log('Delete button element:', deleteBtn);

        if (deleteBtn) {
            deleteBtn.disabled = true;
            deleteBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Tar bort...';

            // Log the request URL and filename
            console.log('Filename to delete:', filename);

            // Properly encode the filename for the URL
            // For paths with multiple segments (like year/competitionType/filename.html),
            // we need to ensure each segment is properly encoded
            let encodedFilename = filename;
            if (filename.includes('/')) {
                // Split the path and encode each segment separately
                const pathSegments = filename.split('/');
                encodedFilename = pathSegments.map(segment => encodeURIComponent(segment)).join('/');
            } else {
                encodedFilename = encodeURIComponent(filename);
            }

            const requestUrl = `/api/github-pages/${encodedFilename}`;
            console.log('Sending DELETE request to:', requestUrl);

            // Send delete request
            fetch(requestUrl, {
                method: 'DELETE',
            })
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', [...response.headers.entries()]);

                    if (!response.ok) {
                        throw new Error(`Failed to delete page: ${response.status} ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);

                    if (!data.success) {
                        throw new Error(data.error || 'Failed to delete page');
                    }

                    // Show success message
                    alert('Publicerad sida har tagits bort.');

                    // Reset button if we found it
                    if (deleteBtn) {
                        deleteBtn.disabled = false;
                        deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';

                        // Hide the delete button
                        deleteBtn.classList.add('d-none');

                        // Hide the link to the published page
                        const linkDiv = document.getElementById('github-pages-link');
                        if (linkDiv) {
                            linkDiv.classList.add('d-none');
                        }
                    }
                })
                .catch(error => {
                    console.error('Error deleting page:', error);
                    alert(`Kunde inte ta bort sidan: ${error.message}`);

                    // Reset button if we found it
                    if (deleteBtn) {
                        deleteBtn.disabled = false;
                        deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
                    }
                });
        } else {
            console.error('Delete button not found in the DOM');
            alert('Fel: Kunde inte hitta knappen för borttagning');
        }
    } else {
        console.log('User cancelled deletion');
    }
}

// Copy GitHub Pages URL to clipboard
function copyGitHubPagesUrl() {
    const urlInput = document.getElementById('github-pages-url');
    if (!urlInput) return;

    // Use the modern Clipboard API if available
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(urlInput.value)
            .then(() => {
                // Show a temporary tooltip or message
                const copyBtn = urlInput.nextElementSibling;
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="bi bi-check"></i> Kopierad!';

                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                }, 2000);
            })
            .catch(err => {
                console.error('Could not copy text: ', err);
                // Fallback to the old method
                urlInput.select();
                document.execCommand('copy');
            });
    } else {
        // Fallback for older browsers
        urlInput.select();
        document.execCommand('copy');

        // Show a temporary tooltip or message
        const copyBtn = urlInput.nextElementSibling;
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="bi bi-check"></i> Kopierad!';

        setTimeout(() => {
            copyBtn.innerHTML = originalText;
        }, 2000);
    }
}

// Render GitHub Pages list with year grouping
function renderGitHubPagesList(container, data) {
    if (!container) {
        console.error('Container element not found');
        return;
    }

    // Clear the container
    container.innerHTML = '';

    if (!data || !data.years || data.years.length === 0) {
        container.innerHTML = '<p>Inga publicerade sidor hittades.</p>';
        return;
    }

    // Create an accordion for the years
    const accordion = document.createElement('div');
    accordion.className = 'accordion';
    accordion.id = 'githubPagesAccordion';

    // Add each year as an accordion item
    data.years.forEach((yearData, index) => {
        const year = yearData.year;
        const pages = yearData.pages;

        const accordionItem = document.createElement('div');
        accordionItem.className = 'accordion-item';

        const headerId = `heading-${year}`;
        const collapseId = `collapse-${year}`;

        // Create the accordion header
        const header = document.createElement('h2');
        header.className = 'accordion-header';
        header.id = headerId;

        const button = document.createElement('button');
        button.className = 'accordion-button';
        if (index !== 0) {
            button.className += ' collapsed';
        }
        button.type = 'button';
        button.setAttribute('data-bs-toggle', 'collapse');
        button.setAttribute('data-bs-target', `#${collapseId}`);
        button.setAttribute('aria-expanded', index === 0 ? 'true' : 'false');
        button.setAttribute('aria-controls', collapseId);
        button.innerHTML = `${year} <span class="badge bg-primary ms-2">${pages.length}</span>`;

        header.appendChild(button);
        accordionItem.appendChild(header);

        // Create the accordion body
        const collapseDiv = document.createElement('div');
        collapseDiv.id = collapseId;
        collapseDiv.className = 'accordion-collapse collapse';
        if (index === 0) {
            collapseDiv.className += ' show';
        }
        collapseDiv.setAttribute('aria-labelledby', headerId);
        collapseDiv.setAttribute('data-bs-parent', '#githubPagesAccordion');

        const accordionBody = document.createElement('div');
        accordionBody.className = 'accordion-body';

        // Create a list of pages for this year
        const list = document.createElement('ul');
        list.className = 'list-group';

        pages.forEach(page => {
            const listItem = document.createElement('li');
            listItem.className = 'list-group-item d-flex justify-content-between align-items-center';

            // Create the page link
            const pageLink = document.createElement('a');
            pageLink.href = page.url;
            pageLink.target = '_blank';
            pageLink.className = 'text-decoration-none';

            // Format the date if available
            let dateText = '';
            if (page.date) {
                const dateParts = page.date.split('-');
                if (dateParts.length >= 3) {
                    dateText = `${dateParts[2]}/${dateParts[1]}`;
                }
            }

            pageLink.innerHTML = `
                <div>
                    <strong>${page.event_name}</strong>
                    ${dateText ? `<small class="text-muted d-block">${dateText}</small>` : ''}
                </div>
            `;

            // Create the delete button
            const deleteButton = document.createElement('button');
            deleteButton.className = 'btn btn-sm btn-outline-danger';
            deleteButton.innerHTML = '<i class="bi bi-trash"></i>';
            deleteButton.onclick = function() {
                deletePublishedPage(null, page.filename);
            };

            listItem.appendChild(pageLink);
            listItem.appendChild(deleteButton);
            list.appendChild(listItem);
        });

        accordionBody.appendChild(list);
        collapseDiv.appendChild(accordionBody);
        accordionItem.appendChild(collapseDiv);

        accordion.appendChild(accordionItem);
    });

    container.appendChild(accordion);
}

// Initialize SRS data update form with loading indicator
function initSRSUpdateForm() {
    const srsUpdateForm = document.querySelector('form[action="/srs/sync"]');
    if (srsUpdateForm) {
        srsUpdateForm.addEventListener('submit', function() {
            // Get the submit button
            const submitButton = this.querySelector('button[type="submit"]');
            if (submitButton) {
                // Disable the button and show loading spinner
                submitButton.disabled = true;
                submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Uppdaterar...';

                // Add a message below the form
                const messageDiv = document.createElement('div');
                messageDiv.className = 'alert alert-info mt-3';
                messageDiv.innerHTML = '<i class="bi bi-info-circle"></i> Uppdaterar SRS data. Detta kan ta några minuter, stäng inte webbläsaren.';

                // Insert the message after the form
                this.parentNode.appendChild(messageDiv);

                // Let the form submit normally
            }
        });
    }
}
