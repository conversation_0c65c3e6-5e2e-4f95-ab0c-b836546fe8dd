{{ template "header.html" . }}

<div class="container mt-4">
    <h1>Om Segling</h1>

    <div class="row">
        <div class="col-md-3">
            <div class="list-group mb-4 sticky-top" style="top: 20px;">
                <a href="#version" class="list-group-item list-group-item-action">Version</a>
                <a href="#release-notes" class="list-group-item list-group-item-action">Release Notes</a>
            </div>
        </div>

        <div class="col-md-9">

            <div class="card mb-4" id="version">
                <div class="card-header">
                    <h2>Version</h2>
                </div>
                <div class="card-body">
                    <p>Aktuell version: <strong>{{ .version }}</strong></p>
                    <p>Byggdatum: <strong>{{ .buildDate }}</strong></p>
                    <p>Commit: <strong>{{ .commitHash }}</strong></p>
                    <p>Repository: <a href="https://github.com/rogge66/sailapp" target="_blank">https://github.com/rogge66/sailapp</a></p>
                </div>
            </div>

            <div class="card mb-4" id="release-notes">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2>Release Notes</h2>
                    <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#releaseNotesContent" aria-expanded="false" aria-controls="releaseNotesContent">
                        <span class="collapse-toggle-text">Visa</span>
                    </button>
                </div>
                <div class="collapse" id="releaseNotesContent">
                    <div class="card-body">
                        <div class="accordion" id="releaseNotesAccordion">
                            {{ range .releaseNotes }}
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading-{{ .Version }}">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-{{ .Version }}" aria-expanded="false" aria-controls="collapse-{{ .Version }}">
                                        {{ .Version }} ({{ .Date }})
                                    </button>
                                </h2>
                                <div id="collapse-{{ .Version }}" class="accordion-collapse collapse" aria-labelledby="heading-{{ .Version }}" data-bs-parent="#releaseNotesAccordion">
                                    <div class="accordion-body">
                                        <div class="release-notes-content">
                                            {{ .Content }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{ end }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Toggle button text between "Visa" and "Dölj" when the collapse state changes
    document.addEventListener('DOMContentLoaded', function() {
        const releaseNotesContent = document.getElementById('releaseNotesContent');
        const toggleButton = document.querySelector('[data-bs-target="#releaseNotesContent"]');
        const toggleText = toggleButton.querySelector('.collapse-toggle-text');

        releaseNotesContent.addEventListener('hidden.bs.collapse', function () {
            toggleText.textContent = 'Visa';
        });

        releaseNotesContent.addEventListener('shown.bs.collapse', function () {
            toggleText.textContent = 'Dölj';
        });
    });
</script>

{{ template "footer.html" . }}
