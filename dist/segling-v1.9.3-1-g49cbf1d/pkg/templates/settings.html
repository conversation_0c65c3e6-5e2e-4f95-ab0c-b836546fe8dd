{{ template "header.html" . }}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>Inställningar</h1>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Databas</h5>
                </div>
                <div class="card-body">
                    <form action="/settings" method="post">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="auto_backup" name="auto_backup" {{ if .autoBackup }}checked{{ end }}>
                            <label class="form-check-label" for="auto_backup">
                                Skapa automatisk säkerhetskopia av databasen när en ny tävling skapas
                            </label>
                            <div class="form-text text-muted">
                                Säkerhetskopior sparas i mappen "backups" i samma katalog som databasen.
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">Spara inställningar</button>
                        <a href="/backups" class="btn btn-outline-secondary ms-2">Hantera säkerhetskopior</a>
                        <button type="button" class="btn btn-outline-info ms-2" onclick="checkpointWAL()">
                            <i class="bi bi-arrow-clockwise"></i> Synkronisera databas
                        </button>
                    </form>
                    <div class="form-text text-muted mt-2">
                        <small><i class="bi bi-info-circle"></i> Synkronisera databas flyttar alla ändringar från WAL-filen till huvuddatabasfilen.</small>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Seglare</h5>
                </div>
                <div class="card-body">
                    <form action="/settings/club" method="post">
                        <div class="mb-3">
                            <label for="default_club" class="form-label">Standard klubb för nya seglare</label>
                            <input type="text" class="form-control" id="default_club" name="default_club" value="{{ .defaultClub }}">
                            <div class="form-text text-muted">
                                Detta värde används som standard när nya seglare skapas.
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">Spara klubbinställning</button>
                    </form>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Tävlingstyper</h5>
                </div>
                <div class="card-body">
                    <form action="/settings/competition-types" method="post">
                        <div class="mb-3">
                            <label for="competition_types" class="form-label">Tävlingstyper</label>
                            <input type="text" class="form-control" id="competition_types" name="competition_types" value="{{ .competitionTypes }}">
                            <div class="form-text text-muted">
                                Kommaseparerad lista med tävlingstyper (t.ex. "Kvällssegling,Regatta").
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="default_competition_type" class="form-label">Standard tävlingstyp</label>
                            <select class="form-select" id="default_competition_type" name="default_competition_type">
                                {{ range .competitionTypesList }}
                                <option value="{{ . }}" {{ if eq . $.defaultCompetitionType }}selected{{ end }}>{{ . }}</option>
                                {{ end }}
                            </select>
                            <div class="form-text text-muted">
                                Detta värde används som standard när nya tävlingar skapas.
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">Spara tävlingstypsinställningar</button>
                    </form>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Visning</h5>
                </div>
                <div class="card-body">
                    <form action="/settings/time-format" method="post">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="use_24h_time" name="use_24h_time" {{ if .use24hTime }}checked{{ end }}>
                            <label class="form-check-label" for="use_24h_time">
                                Använd 24-timmarsformat för tid
                            </label>
                            <div class="form-text text-muted">
                                När detta är aktiverat visas tider i 24-timmarsformat (t.ex. 14:30) istället för 12-timmarsformat (t.ex. 2:30 PM).
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">Spara tidsinställning</button>
                    </form>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-cloud-sun"></i> Väderinställningar
                    </h5>
                </div>
                <div class="card-body">
                    <form action="/settings/weather" method="post">
                        <div class="mb-3">
                            <label for="weather_api_token" class="form-label">Trafikverkets API-nyckel</label>
                            <input type="text" class="form-control" id="weather_api_token" name="weather_api_token" value="{{ .weatherApiToken }}">
                            <div class="form-text text-muted">
                                API-nyckel för Trafikverkets väder-API. Standard-nyckeln fungerar för de flesta användare.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="weather_location" class="form-label">Väderstation</label>
                            <input type="text" class="form-control" id="weather_location" name="weather_location" value="{{ .weatherLocation }}" placeholder="Linköping">
                            <div class="form-text text-muted">
                                Namn på väderstation enligt Trafikverkets API (t.ex. "Linköping", "Stockholm", "Göteborg").
                                <a href="https://www.trafikverket.se/trafikinformation/vag/?map_x=650778.00005&map_y=7200000&map_z=2&map_l=100000001000000" target="_blank">Se karta med väderstationer</a>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">Spara väderinställningar</button>
                    </form>

                    <div class="mt-3 p-3 bg-light rounded">
                        <h6><i class="bi bi-info-circle"></i> Information</h6>
                        <p class="mb-2">Väderdata hämtas från Trafikverkets öppna API och visas på huvudsidan.</p>
                        <ul class="mb-0">
                            <li>Data uppdateras automatiskt var 5:e minut</li>
                            <li>Visar vindhastighet och vindriktning</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="card mt-4" id="github-pages">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">GitHub Pages Integration</h5>
                    <a href="/about#settings" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-question-circle"></i> Hjälp
                    </a>
                </div>
                <div class="card-body">
                    <form action="/settings/github-pages" method="post">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="github_pages_enabled" name="github_pages_enabled" {{ if .githubPagesEnabled }}checked{{ end }}>
                            <label class="form-check-label" for="github_pages_enabled">
                                Aktivera GitHub Pages integration
                            </label>
                            <div class="form-text text-muted">
                                När detta är aktiverat kan du publicera tävlingsresultat till GitHub Pages.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="github_pages_repo" class="form-label">GitHub Repository URL</label>
                            <input type="text" class="form-control" id="github_pages_repo" name="github_pages_repo" value="{{ .githubPagesRepo }}" placeholder="användarnamn/repo">
                            <div class="form-text text-muted">
                                Ange GitHub repository i formatet användarnamn/repo, t.ex. "rogge66/sailapp-results"
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="github_pages_branch" class="form-label">GitHub Branch</label>
                            <input type="text" class="form-control" id="github_pages_branch" name="github_pages_branch" value="{{ .githubPagesBranch }}" placeholder="gh-pages">
                            <div class="form-text text-muted">
                                Ange vilken branch som ska användas för GitHub Pages, vanligtvis "gh-pages" eller "main"
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="github_pages_token" class="form-label">GitHub Personal Access Token</label>
                            <input type="password" class="form-control" id="github_pages_token" name="github_pages_token" value="{{ .githubPagesToken }}">
                            <div class="form-text text-muted">
                                Skapa en personal access token med "repo" behörighet på <a href="https://github.com/settings/tokens" target="_blank">GitHub</a>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="github_pages_template" class="form-label">Sidmall</label>
                            <select class="form-select" id="github_pages_template" name="github_pages_template">
                                <option value="default" {{ if eq .githubPagesTemplate "default" }}selected{{ end }}>Standard</option>
                                <option value="minimal" {{ if eq .githubPagesTemplate "minimal" }}selected{{ end }}>Minimal</option>
                            </select>
                            <div class="form-text text-muted">
                                Välj vilken mall som ska användas för publicerade resultat
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">Spara GitHub Pages inställningar</button>
                    </form>

                    {{ if and .githubPagesEnabled .githubPagesRepo }}
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6>Publicerade resultat</h6>
                        <p>Dina publicerade resultat finns på:</p>
                        <div class="input-group">
                            <input type="text" class="form-control" readonly value="https://{{ (index (split .githubPagesRepo "/") 0) }}.github.io/{{ (index (split .githubPagesRepo "/") 1) }}/" id="github-pages-url">
                            <button class="btn btn-outline-secondary" type="button" onclick="copyGitHubPagesUrl()">
                                <i class="bi bi-clipboard"></i> Kopiera
                            </button>
                            <a href="https://{{ (index (split .githubPagesRepo "/") 0) }}.github.io/{{ (index (split .githubPagesRepo "/") 1) }}/" target="_blank" class="btn btn-outline-primary">
                                <i class="bi bi-box-arrow-up-right"></i> Öppna
                            </a>
                        </div>
                        <small class="text-muted mt-2 d-block">
                            <i class="bi bi-info-circle"></i> Det kan ta några minuter innan sidan är tillgänglig efter första publiceringen.
                        </small>
                    </div>
                    {{ end }}
                </div>
            </div>

            <!-- Google Drive Integration -->
            <div class="card mt-4" id="google-drive">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Google Drive Integration</h5>
                    <a href="/about#settings" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-question-circle"></i> Hjälp
                    </a>
                </div>
                <div class="card-body">
                    <form action="/settings/google-drive" method="post">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="google_drive_enabled" name="google_drive_enabled" {{ if .googleDriveEnabled }}checked{{ end }}>
                            <label class="form-check-label" for="google_drive_enabled">
                                Aktivera Google Drive export
                            </label>
                            <div class="form-text text-muted">
                                Tillåter export av tävlingsresultat till Google Drive som Google Sheets.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="google_drive_folder_id" class="form-label">Google Drive Mapp-ID (valfritt)</label>
                            <input type="text" class="form-control" id="google_drive_folder_id" name="google_drive_folder_id" value="{{ .googleDriveFolderID }}" placeholder="Lämna tomt för rotmappen">
                            <div class="form-text text-muted">
                                ID för Google Drive-mappen där resultat ska exporteras. Lämna tomt för att använda rotmappen.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="google_drive_naming_convention" class="form-label">Namnkonvention för filer</label>
                            <input type="text" class="form-control" id="google_drive_naming_convention" name="google_drive_naming_convention" value="{{ .googleDriveNamingConvention }}" placeholder="&#123;&#123;event_name&#125;&#125;_&#123;&#123;date&#125;&#125;">
                            <div class="form-text text-muted">
                                Använd platshållare: &#123;&#123;event_name&#125;&#125;, &#123;&#123;date&#125;&#125;, &#123;&#123;competition_type&#125;&#125;, &#123;&#123;year&#125;&#125;, &#123;&#123;month&#125;&#125;, &#123;&#123;day&#125;&#125;
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">Spara Google Drive inställningar</button>
                    </form>

                    <hr class="my-4">

                    <!-- OAuth Configuration Info -->
                    <div class="mb-4">
                        <h6>OAuth Konfiguration</h6>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> <strong>Viktigt:</strong> När du skapar OAuth 2.0 Client ID i Google Console, lägg till denna callback URL:
                            <div class="mt-2">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="callback-url" value="{{ .googleDriveCallbackURL }}" readonly>
                                    <button class="btn btn-outline-secondary" type="button" onclick="copyCallbackUrl()">
                                        <i class="bi bi-clipboard"></i> Kopiera
                                    </button>
                                </div>
                            </div>
                            <small class="text-muted mt-1 d-block">
                                Gå till Google Cloud Console → APIs & Credentials → Create Credentials → OAuth 2.0 Client ID →
                                Lägg till ovanstående URL under "Authorized redirect URIs"
                            </small>
                        </div>

                        <div class="alert alert-warning mt-3">
                            <i class="bi bi-exclamation-triangle"></i> <strong>Viktiga krav för Google Console:</strong>
                            <ol class="mt-2 mb-0">
                                <li><strong>Aktivera APIs:</strong> Google Drive API och Google Sheets API måste vara aktiverade</li>
                                <li><strong>OAuth Consent Screen:</strong> Lägg till dessa scopes:
                                    <ul class="mt-1">
                                        <li><code>https://www.googleapis.com/auth/drive</code></li>
                                        <li><code>https://www.googleapis.com/auth/spreadsheets</code></li>
                                    </ul>
                                </li>
                                <li><strong>Application Type:</strong> Välj "Web application" (inte Desktop application)</li>
                            </ol>
                        </div>
                    </div>

                    <!-- Client Secrets Upload -->
                    <div class="mb-3">
                        <h6>Google API Autentisering</h6>
                        {{ if .googleDriveClientSecretsExists }}
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle"></i> Client secrets fil är uppladdad
                            </div>
                        {{ else }}
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i> Client secrets fil krävs för Google Drive integration
                            </div>
                        {{ end }}

                        <form id="client-secrets-form" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="client_secrets" class="form-label">Ladda upp client_secrets.json</label>
                                <input type="file" class="form-control" id="client_secrets" name="client_secrets" accept=".json">
                                <div class="form-text text-muted">
                                    Ladda ner client_secrets.json från Google Cloud Console för ditt projekt.
                                </div>
                            </div>
                            <button type="submit" class="btn btn-outline-primary">Ladda upp</button>
                        </form>
                    </div>

                    <!-- Authentication Status -->
                    <div class="mb-3">
                        <h6>Autentiseringsstatus</h6>
                        <button id="check-auth-btn" class="btn btn-outline-info">Kontrollera autentisering</button>
                        <div id="auth-status" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Handle client secrets upload
document.getElementById('client-secrets-form').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData();
    const fileInput = document.getElementById('client_secrets');

    if (!fileInput.files[0]) {
        alert('Välj en fil att ladda upp');
        return;
    }

    formData.append('client_secrets', fileInput.files[0]);

    fetch('/settings/upload-client-secrets', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Client secrets fil uppladdad framgångsrikt!');
            location.reload();
        } else {
            alert('Fel: ' + data.error);
        }
    })
    .catch(error => {
        alert('Fel vid uppladdning: ' + error);
    });
});

// Handle authentication check
document.getElementById('check-auth-btn').addEventListener('click', function() {
    const statusDiv = document.getElementById('auth-status');
    statusDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> Kontrollerar...';

    fetch('/google-drive/auth')
    .then(response => response.json())
    .then(data => {
        if (data.authenticated) {
            statusDiv.innerHTML = '<div class="alert alert-success"><i class="bi bi-check-circle"></i> Autentiserad med Google Drive</div>';
        } else {
            statusDiv.innerHTML = `
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> Inte autentiserad.
                    <a href="${data.auth_url}" target="_blank" class="btn btn-sm btn-primary ms-2">Autentisera</a>
                </div>
                <div class="mt-2">
                    <label for="auth-code" class="form-label">Klistra in auktoriseringskod:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="auth-code" placeholder="Auktoriseringskod">
                        <button class="btn btn-primary" onclick="submitAuthCode()">Skicka</button>
                    </div>
                </div>
            `;
        }
    })
    .catch(error => {
        statusDiv.innerHTML = '<div class="alert alert-danger">Fel: ' + error + '</div>';
    });
});

function submitAuthCode() {
    const code = document.getElementById('auth-code').value;
    if (!code) {
        alert('Ange auktoriseringskod');
        return;
    }

    const formData = new FormData();
    formData.append('code', code);

    fetch('/google-drive/auth', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Autentisering lyckades!');
            document.getElementById('check-auth-btn').click(); // Refresh status
        } else {
            alert('Fel: ' + data.error);
        }
    })
    .catch(error => {
        alert('Fel: ' + error);
    });
}

function copyGitHubPagesUrl() {
    const urlInput = document.getElementById('github-pages-url');
    urlInput.select();
    urlInput.setSelectionRange(0, 99999); // For mobile devices

    navigator.clipboard.writeText(urlInput.value).then(function() {
        // Change button text temporarily to show success
        const button = event.target.closest('button');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i> Kopierad!';
        button.classList.remove('btn-outline-secondary');
        button.classList.add('btn-success');

        setTimeout(function() {
            button.innerHTML = originalHTML;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    }).catch(function(err) {
        // Fallback for older browsers
        try {
            document.execCommand('copy');
            alert('URL kopierad till urklipp!');
        } catch (err) {
            alert('Kunde inte kopiera URL. Markera och kopiera manuellt.');
        }
    });
}

function copyCallbackUrl() {
    const urlInput = document.getElementById('callback-url');
    urlInput.select();
    urlInput.setSelectionRange(0, 99999); // For mobile devices

    navigator.clipboard.writeText(urlInput.value).then(function() {
        // Change button text temporarily to show success
        const button = event.target.closest('button');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i> Kopierad!';
        button.classList.remove('btn-outline-secondary');
        button.classList.add('btn-success');

        setTimeout(function() {
            button.innerHTML = originalHTML;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    }).catch(function(err) {
        // Fallback for older browsers
        try {
            document.execCommand('copy');
            alert('URL kopierad till urklipp!');
        } catch (err) {
            alert('Kunde inte kopiera URL. Markera och kopiera manuellt.');
        }
    });
}

function checkpointWAL() {
    const button = event.target.closest('button');
    const originalHTML = button.innerHTML;

    // Show loading state
    button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Synkroniserar...';
    button.disabled = true;

    fetch('/settings/checkpoint-wal', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success state
            button.innerHTML = '<i class="bi bi-check-circle"></i> Synkroniserad!';
            button.classList.remove('btn-outline-info');
            button.classList.add('btn-success');

            // Show success message
            alert(data.message);

            // Reset button after 3 seconds
            setTimeout(function() {
                button.innerHTML = originalHTML;
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-info');
                button.disabled = false;
            }, 3000);
        } else {
            // Show error state
            button.innerHTML = '<i class="bi bi-exclamation-triangle"></i> Fel';
            button.classList.remove('btn-outline-info');
            button.classList.add('btn-danger');

            alert('Fel: ' + data.error);

            // Reset button after 3 seconds
            setTimeout(function() {
                button.innerHTML = originalHTML;
                button.classList.remove('btn-danger');
                button.classList.add('btn-outline-info');
                button.disabled = false;
            }, 3000);
        }
    })
    .catch(error => {
        // Show error state
        button.innerHTML = '<i class="bi bi-exclamation-triangle"></i> Fel';
        button.classList.remove('btn-outline-info');
        button.classList.add('btn-danger');

        alert('Fel vid synkronisering: ' + error);

        // Reset button after 3 seconds
        setTimeout(function() {
            button.innerHTML = originalHTML;
            button.classList.remove('btn-danger');
            button.classList.add('btn-outline-info');
            button.disabled = false;
        }, 3000);
    });
}
</script>

{{ template "footer.html" . }}
