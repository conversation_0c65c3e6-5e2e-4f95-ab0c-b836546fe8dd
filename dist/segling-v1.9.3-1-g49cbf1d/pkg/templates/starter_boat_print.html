<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 9pt;
        }
        h1 {
            font-size: 14pt;
            margin-bottom: 8px;
        }
        h2 {
            font-size: 12pt;
            margin-bottom: 6px;
        }
        .event-info {
            margin-bottom: 15px;
            border: 1px solid #000;
            padding: 8px;
        }
        .event-info p {
            margin: 3px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            table-layout: fixed;
        }
        th, td {
            border: 1px solid #000;
            padding: 4px;
            text-align: left;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        th {
            background-color: #f2f2f2;
            font-size: 9pt;
            font-weight: bold;
        }
        /* Column width definitions */
        .col-start-num {
            width: 40px;
        }
        .col-start-time {
            width: 60px;
        }
        .col-seglare {
            width: 120px;
        }
        .col-klubb {
            width: 50px;
        }
        .col-battyp {
            width: 100px;
        }
        .col-segelnr {
            width: 70px;
        }
        .col-matbrev {
            width: 70px;
        }
        .col-srs {
            width: 80px;
        }
        .col-malgang {
            width: 120px;
            height: 25px;
        }
        .col-notes {
            width: 100px;
        }
        /* Entypsegling specific column widths */
        .col-entypsegling-seglare {
            width: 150px;
        }
        .col-entypsegling-segelnr {
            width: 100px;
        }
        .col-entypsegling-heat {
            width: 80px;
            height: 30px;
        }
        .page-break {
            page-break-after: always;
        }
        .footer {
            margin-top: 15px;
            font-size: 8pt;
            text-align: center;
        }
        .small {
            font-size: 8pt;
        }
        @media print {
            body {
                padding: 0;
                margin: 0;
            }
            .no-print {
                display: none;
            }
            @page {
                margin: 0.8cm;
            }
        }
    </style>
</head>
<body>
    <div class="no-print" style="margin-bottom: 20px;">
        <button onclick="window.print()">Skriv ut</button>
        <button onclick="window.history.back()">Tillbaka</button>
    </div>

    {{ if .event.Entypsegling }}
    <h1>{{ .event.Namn }} - Placeringsformulär</h1>

    <div class="event-info">
        <p><strong>Datum:</strong> {{ .event.Datum.Format "2006-01-02" }} | <strong>Båttyp:</strong> {{ .event.BoatType }}</p>
        {{ if .event.Beskrivning }}<p><strong>Beskrivning:</strong> {{ .event.Beskrivning }}</p>{{ end }}
        <p><em>Använd detta formulär för att registrera placeringar under tävlingen. Fyll i placering (1, 2, 3, DNS, DNF etc.) för varje heat.</em></p>
    </div>

    <h2>Placeringsformulär</h2>
    {{ else }}
    <h1>{{ .event.Namn }} - Startbåtsinformation</h1>

    <div class="event-info">
        <p><strong>Datum:</strong> {{ .event.Datum.Format "2006-01-02" }} | <strong>Starttid:</strong> {{ .event.Starttid }} | <strong>Vind:</strong> {{ .event.Vind }} m/s | <strong>Banlängd:</strong> {{ .event.Banlangd }} nm | <strong>Jaktstart:</strong> {{ if .isJaktstart }}{{ if .isHalfJaktstart }}Halv{{ else }}Hel{{ end }}{{ else }}Nej{{ end }}</p>
        {{ if .event.Beskrivning }}<p><strong>Beskrivning:</strong> {{ .event.Beskrivning }}</p>{{ end }}
        <p><em>Notera: S/H = short hand, * = anpassat SRS-värde</em></p>
    </div>

    <h2>Startlista</h2>
    {{ end }}
    {{ if .event.Entypsegling }}
    <!-- Entypsegling table with heat columns -->
    <table>
        <thead>
            <tr>
                <th class="col-entypsegling-seglare">Seglare</th>
                <th class="col-entypsegling-segelnr">Segelnr</th>
                {{ range .heats }}
                <th class="col-entypsegling-heat">{{ .Name }}</th>
                {{ end }}
            </tr>
        </thead>
        <tbody>
            {{ range $index, $participant := .participants }}
            <tr>
                <td class="col-entypsegling-seglare">{{ $participant.Sailor.Namn }}</td>
                <td class="col-entypsegling-segelnr">{{ $participant.EventParticipant.PersonalNumber }}</td>
                {{ range $.heats }}
                <td class="col-entypsegling-heat"></td>
                {{ end }}
            </tr>
            {{ end }}
        </tbody>
    </table>
    {{ else }}
    <!-- Normal sailing event table -->
    <table>
        <thead>
            <tr>
                <th class="col-start-num">Start #</th>
                <th class="col-start-time">Starttid</th>
                <th class="col-seglare">Seglare</th>
                <th class="col-klubb">Klubb</th>
                <th class="col-battyp">Båttyp</th>
                <th class="col-segelnr">Segelnr</th>
                <th class="col-matbrev">Mätbrev</th>
                <th class="col-srs">SRS</th>
                <th class="col-malgang">Målgång</th>
                <th class="col-notes">Anteckningar</th>
            </tr>
        </thead>
        <tbody>
            {{ range $index, $participant := .participants }}
            <tr>
                <td class="col-start-num">{{ add $index 1 }}</td>
                <td class="col-start-time">{{ if $.isJaktstart }}{{ $participant.AbsoluteStartTime }}{{ else }}{{ $.event.Starttid }}{{ end }}</td>
                <td class="col-seglare">{{ $participant.Sailor.Namn }}</td>
                <td class="col-klubb">{{ $participant.Sailor.Klubb }}</td>
                <td class="col-battyp">{{ if gt (len $participant.Boat.Battyp) 14 }}{{ slice $participant.Boat.Battyp 0 14 }}...{{ else }}{{ $participant.Boat.Battyp }}{{ end }}</td>
                <td class="col-segelnr">{{ if and $participant.Boat.Nationality $participant.Boat.Segelnummer }}{{ $participant.Boat.Nationality }}-{{ $participant.Boat.Segelnummer }}{{ end }}</td>
                <td class="col-matbrev">{{ if $participant.Boat.MatbrevsNummer }}{{ $participant.Boat.MatbrevsNummer }}{{ end }}</td>
                <td class="col-srs">
                    {{ $participant.EventParticipant.SelectedSRSValue }}
                    {{ if $participant.EventParticipant.UseCustomSRSValue }}
                        (*)
                    {{ else if eq $participant.EventParticipant.SRSType "srs" }}
                        (spinn)
                    {{ else if eq $participant.EventParticipant.SRSType "srs_shorthanded" }}
                        (S/H spinn)
                    {{ end }}
                </td>
                <td class="col-malgang"></td>
                <td class="col-notes"></td>
            </tr>
            {{ end }}
        </tbody>
    </table>
    {{ end }}

    <div class="footer">
        <p>Utskrivet: {{ now.Format "2006-01-02 15:04:05" }}</p>
    </div>
</body>
</html>
