{{ range .participants }}
<tr id="participant-{{ .ID }}">
    <td>{{ .Sailor.Namn }}</td>
    {{ if $.event.Entypsegling }}
    <td>
        <strong>{{ .PersonalNumber }}</strong>
    </td>
    {{ else }}
    <td>
        {{ .Boat.Namn }} ({{ .Boat.Battyp }})
        {{ if or .Boat.Segelnummer .Boat.Nationality }}
            <br>
            <small class="text-muted">
                {{ if and .Boat.Nationality .Boat.Segelnummer }}
                    {{ .Boat.Nationality }}-{{ .Boat.Segelnummer }}
                {{ else if .Boat.Segelnummer }}
                    {{ .Boat.Segelnummer }}
                {{ else if .Boat.Nationality }}
                    {{ .Boat.Nationality }}
                {{ end }}
            </small>
        {{ end }}
    </td>
    <td>
        {{ if .UseCustomSRSValue }}
            {{ .CustomSRSValue }} (anpassad)
        {{ else }}
            {{ if eq .SRSType "srs" }}
                {{ .Boat.SRS }} (SRS)
            {{ else if eq .SRSType "srs_utan_undanvindsegel" }}
                {{ .Boat.SRSUtanUndanvindsegel }} (SRS utan undanvindsegel)
            {{ else if eq .SRSType "srs_shorthanded" }}
                {{ .Boat.SRSShorthanded }} (SRS S/H)
            {{ else if eq .SRSType "srs_shorthanded_utan_undanvindsegel" }}
                {{ .Boat.SRSShorthandedUtanUndanvindsegel }} (SRS S/H utan undanvindsegel)
            {{ end }}
        {{ end }}
    </td>
    {{ end }}
    <td>
        {{ if $.event.Entypsegling }}
            {{ .CrewCount }}
        {{ else }}
            {{ if .CrewCount }}
                {{ add .CrewCount 1 }}
            {{ else }}
                1
            {{ end }}
        {{ end }}
    </td>
    <td>
        <a href="/participants/{{ .ID }}" class="btn btn-sm btn-outline-primary">Redigera</a>
        <button class="btn btn-sm btn-outline-danger delete-participant-btn"
            data-participant-id="{{ .ID }}">
            Ta bort
        </button>
    </td>
</tr>
{{ end }}
