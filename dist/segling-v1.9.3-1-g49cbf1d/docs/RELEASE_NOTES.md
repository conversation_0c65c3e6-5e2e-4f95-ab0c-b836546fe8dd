# Release Notes

## Version 1.9.2 (2025-01-15)

### Weather Integration
- **Weather Widget**: Added real-time weather information display on the main page
  - Shows current wind speed in m/s from Trafikverket weather stations
  - Displays wind direction with both degrees and Swedish compass directions (e.g., "Väst-sydväst")
  - Features a wind direction arrow that correctly points in the direction the wind is blowing
  - Automatically updates every 5 minutes without page refresh
  - Shows last updated timestamp and weather station location
- **Weather Settings**: Added configurable weather integration settings
  - Trafikverket API token configuration (with working default)
  - Weather station selection (default: Linköping)
  - Link to Trafikverket weather stations map for easy station discovery
- **Weather API Integration**: Integrated with Trafikverket's open weather API
  - Real-time data from Swedish road weather stations
  - Reliable and accurate weather information for sailing activities
  - Automatic fallback to default settings if configuration is missing

### User Interface Improvements
- **Wind Direction Display**: Fixed wind direction arrow to show the direction wind is blowing towards (not from)
- **Weather Widget Design**: Clean, responsive design that adapts to light and dark themes
- **Error Handling**: Graceful error handling when weather data is unavailable

### Documentation Updates
- **Help Page**: Added comprehensive weather section explaining the weather widget functionality
- **About Page Reorganization**: Streamlined About page to focus only on version and release information
  - Moved all detailed documentation to the Help page
  - Added direct link from About page to Help page for user guidance
- **Settings Documentation**: Updated settings section with detailed weather configuration instructions
- **Navigation**: Added weather section to help page navigation menu

## Version 1.9.1 (2025-06-07)

### Documentation and Navigation Improvements
- **Entypsegling Documentation**: Added comprehensive documentation about the Entypsegling (one-design sailing) feature
  - Added complete entypsegling section to both Help and About pages
  - Updated main navigation to include "Hjälp" (Help) link for better accessibility
  - Updated overview sections to mention entypsegling capabilities
  - Enhanced events, participants, finish times, and results sections with entypsegling information
  - Added detailed instructions for creating, managing, and recording results for one-design competitions
  - Documented tied finishes, export capabilities, and incompatibility with jaktstart

## Version 1.7.9 (2025-05-24)

### Google Drive Integration
- **Complete Google Drive Export**: Added full Google Drive integration for exporting competition results as Google Sheets
  - OAuth2 authentication with Google Cloud Platform integration
  - One-click export from any competition results page
  - Professional formatting with competition details, participant information, and complete results tables
  - Configurable folder organization in Google Drive
  - Customizable filename templates with placeholders for event name and date
  - Automatic opening of exported documents in new browser tabs
- **Google Drive Service**: Implemented comprehensive Google Drive service layer
  - Secure credential management with client_secrets.json upload
  - Folder management and file creation capabilities
  - Error handling and user feedback for authentication and export processes
- **Setup Documentation**: Added detailed Google Drive setup guides
  - Step-by-step Google Cloud Platform configuration instructions
  - Visual examples for finding Google Drive folder IDs
  - Comprehensive troubleshooting and configuration help

### Documentation and Navigation Improvements
- **Help Route Fix**: Fixed /help route redirect loop by adding proper GetHelp handler
- **Bidirectional Navigation**: Added navigation links between About and Settings pages for easier configuration access
- **Documentation Cleanup**: Streamlined documentation by removing redundant content for cleaner user experience
- **User Interface**: Enhanced About and Settings templates with improved navigation and dark mode support

## Version 1.7.8 (2025-05-22)

### User Interface Improvements
- **Results Tables**: Adjusted column widths in results tables for better readability
  - Increased SRS column width from 60px to 65px for better display of SRS values and labels
  - Decreased time columns width from 60px to 55px for more compact display
  - Decreased elapsed time, corrected time, and time difference columns from 70px to 65px
  - Improved overall table layout balance in printed and published results

### Results Display Improvements
- **DNS/DNF Handling**: Improved display of DNS (Did Not Start) and DNF (Did Not Finish) entries in results
  - Don't show 'efter vinnare' (time after winner) for DNF and DNS entries
  - Don't display start time for DNS entries
  - Properly format DNS/DNF status in both printed and published results

### Bug Fixes
- **Main Page**: Fixed boat count in main page to correctly show the number of participants (boat/sailor combinations)
  - Modified CountEventBoats function to count the number of participants instead of unique boat IDs
  - Updated UI text to indicate that the count represents the number of participants
  - Ensures that the boat count in the main page matches the number of rows in the results table

## Version 1.7.7 (2025-05-21)

### User Interface Improvements
- **About Page**: Made release notes section collapsible to improve page readability
- **User Experience**: Added toggle button to show/hide release notes with "Visa"/"Dölj" text

## Version 1.7.6 (2025-05-21)

### Bug Fixes
- **About Page**: Fixed release notes loading in About page to handle both root and docs directory locations
- **Build Process**: Updated build script and GitHub Actions workflow to maintain backward compatibility
- **Documentation**: Ensured release notes are copied to both root and docs directories in distribution packages

## Version 1.7.5 (2025-05-21)

### Build and Deployment Improvements
- **GitHub Actions Workflow**: Fixed GitHub Actions workflow to properly include all necessary files in releases
- **Release Package Structure**: Improved directory structure in release packages to avoid conflicts
- **Documentation**: Ensured release notes are included in all distribution packages

## Version 1.7.4 (2025-05-21)

### Performance and Deployment Improvements
- **GitHub Release Packages**: Enhanced GitHub Actions workflow to include complete application packages in releases
  - Added platform-specific zip archives with all necessary files (templates, static files, scripts, etc.)
  - Improved release structure to match the local build output from the build script
  - Included start scripts and documentation for each platform
- **SQLite Optimization**: Significantly improved SRS data update performance on Windows with SATA disks
  - Added WAL journal mode and optimized SQLite configuration
  - Implemented batch processing for SRS data updates
  - Reduced disk I/O operations with prepared statements and in-memory processing
  - Added detailed logging for better troubleshooting

## Version 1.7.3 (2025-05-20)

### Performance, Documentation, and Deployment Improvements
- **SQLite Optimization**: Significantly improved SRS data update performance on Windows with SATA disks
  - Added WAL journal mode and optimized SQLite configuration
  - Implemented batch processing for SRS data updates
  - Reduced disk I/O operations with prepared statements and in-memory processing
  - Added detailed logging for better troubleshooting
- **GitHub Release Packages**: Enhanced GitHub Actions workflow to include complete application packages in releases
  - Added platform-specific zip archives with all necessary files (templates, static files, scripts, etc.)
  - Improved release structure to match the local build output from the build script
  - Included start scripts and documentation for each platform
- **OpenAPI Specification**: Updated OpenAPI specification for SRS data sync endpoint with loading indicator details
- **API Documentation**: Improved documentation for the SRS data update endpoint with accurate response codes and examples

## Version 1.7.2 (2025-05-20)

### User Interface Improvements
- **Custom Sailboat Icon**: Added a custom SVG sailboat icon for boat count in the main page
- **Display Order**: Swapped positions of boat count and participant count badges in main page for better logical flow
- **Summary Information**: Added summary row at the bottom of competition list showing total boats and participants
- **Loading Indicator**: Added loading indicator for SRS data update to prevent double clicks and show progress

## Version 1.7.1 (2025-05-19)

### Code Quality Improvements
- **Dependency Updates**: Replaced deprecated `io/ioutil` package with `os` package functions
- **Version Handling**: Fixed version handling to use git tag instead of hardcoded value
- **Build Process**: Improved build process to correctly inject version from git tag

## Version 1.7.0 (2025-05-18)

### Security Enhancements
- **Credential Management**: Moved GitHub Pages token from database to separate file for improved security
- **Mutual TLS**: Added support for client certificate authentication (mutual TLS) to restrict server access to known clients
- **HTTPS Improvements**:
  - Enhanced certificate generation and browser import process
  - Added support for importing self-signed certificates to browsers
  - Improved documentation for HTTPS setup and configuration

### User Interface Improvements
- **Favicon Support**: Added favicon support to the application
- **Browser Compatibility**: Fixed favicon.ico 404 error by automatically copying favicon.png to favicon.ico

## Version 1.6.1 (2025-05-18)

### Bug Fixes
- **GitHub Pages**: Fixed issue with deleting GitHub Pages files by using full path with year and competition type directories
- **Code Quality**: Fixed linting issues for better code quality and maintainability
- **Cache Busting**: Added cache-busting parameters to GitHub API requests to prevent stale data
- **Error Handling**: Improved error handling and logging for GitHub Pages operations

## Version 1.6.0 (2025-05-18)

### Code Quality and Stability Improvements
- **Error Handling**: Improved error handling throughout the application with proper checking of all error returns
- **Code Cleanup**: Removed unused functions and improved code organization
- **Linting**: Added comprehensive linting configuration with golangci-lint
- **GitHub Pages Workflow**: Enhanced GitHub Pages workflow to handle cases when Pages is not enabled
- **Migration Scripts**: Reorganized database migration scripts for better maintainability
- **Build Process**: Fixed build process to handle migration scripts properly

## Version 1.5.7 (2025-05-18)

### CI/CD Improvements
- **GitHub Actions**: Added GitHub Actions workflows for continuous integration and deployment
- **Automated Testing**: Set up automated testing on multiple platforms (Linux, macOS, Windows)
- **Code Quality**: Added linting workflow to ensure code quality and style consistency
- **Automated Releases**: Configured automatic release creation when tags are pushed
- **Documentation Site**: Added workflow to automatically deploy documentation to GitHub Pages
- **Build Badges**: Added build status badges to the README.md file

## Version 1.5.6 (2025-05-18)

### Documentation Improvements
- **Enhanced About Page**: Updated the settings section in the about page with detailed information about all available settings
- **Competition Type Documentation**: Added comprehensive documentation about the competition type feature and how it's used in the application
- **GitHub Pages Structure**: Added detailed explanation of the new hierarchical directory structure for GitHub Pages with visual representation
- **Event Information**: Updated event documentation to include information about auto-generated event names
- **Repository Link**: Added repository URL to the version section in the about page for easy access to the source code

## Version 1.5.5 (2025-05-18)

### Event Creation Improvements
- **Auto-generated Event Names**: When creating a new event, the name field is automatically populated with "<tävlingstyp> <date>" based on the selected competition type and date
- **User Experience**: The auto-generated name is only used when the user hasn't manually entered a name, preserving user input when desired

## Version 1.5.4 (2025-05-18)

### Competition Type Feature
- **Competition Type Setting**: Added new setting for tävlingstyp (competition type) with Kvällssegling and Regatta as defaults
- **Event Creation**: Added competition type field to event creation and editing forms
- **GitHub Pages Structure**: Enhanced GitHub Pages directory structure to organize results by year and competition type
- **Navigation Improvements**: Updated navigation in GitHub Pages to support the new hierarchical structure (year → competition type → results)
- **Settings Management**: Added UI for managing competition types in the settings page

## Version 1.5.3 (2025-05-18)

### GitHub Pages Improvements
- **Year-based Directory Structure**: Implemented a hierarchical organization for published results, grouping them by year for better navigation
- **Main Index Page Enhancement**: Updated the main index.html to display links to year directories
- **Year-specific Index Pages**: Added year-specific index.html files that list all events for that year
- **Navigation Improvements**: Added "Back to all years" links for easier navigation between year directories

## Version 1.5.2 (2025-05-18)

### User Experience Improvements
- **Finish Time Entry Enhancement**: Allow users to enter finish times without seconds, automatically defaulting to ":00"

## Version 1.5.1 (2025-05-18)

### Documentation
- **Comprehensive GitHub Pages Documentation**: Added detailed documentation about the GitHub Pages functionality to the help page
- **Event Locking Information**: Updated help page and README with information about event locking functionality

## Version 1.5.0 (2025-05-17/18)

This major release introduces GitHub Pages integration for publishing race results to the web.

### GitHub Pages Integration
- **Publishing Functionality**: Added ability to publish event results to GitHub Pages
- **Results Management**: Implemented a dedicated GitHub Pages management page
- **Template Improvements**:
  - Updated templates to match printable version
  - Fixed field names in GitHub Pages templates
  - Improved design with modern Bootstrap styling
  - Removed unnecessary links from GitHub Pages index
  - Added timestamp parameters for cache invalidation

### Performance & Reliability
- **Cache Control**: Added cache busting with timestamp parameters
- **CORS Handling**: Fixed CORS issues with server-side proxy for availability checks
- **Polling Improvements**: Increased polling time and improved waiting UI
- **Verification**: Added checksum verification for published pages

### User Experience
- **UI Behavior**: Improved UI behavior for publish and delete buttons
- **Delete Functionality**: Added ability to delete published GitHub Pages
- **Dark Mode**: Fixed dark mode for year filter and published results sections

### Technical Improvements
- **API Integration**: Replaced mock implementations with real GitHub API calls
- **Debugging**: Added extensive debugging to GitHub Pages functionality
- **Code Organization**: Moved GitHub Pages templates to separate files
- **Template Functions**: Fixed template function issues by registering split function globally

### Testing
- **Endpoint Testing**: Enhanced test script with comprehensive endpoint testing
- **Database Parameter**: Updated main.go to accept database parameter for testing
- **Test Script**: Added test script for automated testing

### Documentation
- **PRD Updates**: Updated PRD with GitHub Pages integration requirements
- **Testing Requirements**: Added testing requirements to PRD

### Event Locking
- **UI Indicators**: Show that event is locked in different views

## Version 1.4.2 (2025-05-14)

### User Interface
- **Scrollable Competition List**: Made the "tävlingar" (competitions) section on the main page scrollable for better navigation with many events
- **Documentation**: Added Product Requirements Document (PRD)

## Version 1.4.1 (2025-05-13)

### Data Management
- **CSV Import/Export**: Refactored CSV import/export functionality for improved reliability
- **UI Cleanup**: Removed unnecessary alert information for cleaner user experience

## Version 1.4.0 (2025-05-13)

### User Interface
- **Version Display**: Added version display in UI using git tags for versioning
- **Print Layout**:
  - Adjusted dark stripes in printed results page for better readability
  - Improved layout of printed pages for better presentation

### Documentation
- **Help Page**: Updated help page with new features and instructions
- **README**: Enhanced README with additional information

### Data Management
- **Custom SRS Handling**: Improved handling of custom SRS values when results are locked
- **Test Data**: Added real data for testing purposes

## Version 1.3.0 (2025-05-13)

### Event Locking
- **Lock Functionality**: Implemented event locking to preserve historical results
- **Data Protection**: Fixed update and lock of boat values to maintain data integrity
- **Boat Updates**: Improved boat data update process

## Version 1.2.0 (2025-05-12/13)

### User Interface
- **Starter Boat Info**: Swapped mätbrev and SRS value in starter boat information for better clarity
- **Search Functionality**: Fixed sailors and boats search for improved user experience
- **Favicon**: Added favicon for better browser identification

### Data Management
- **Mätbrev Handling**:
  - Fixed mätbrev handling in boat editing
  - Added mätbrevsnummer to results and starter boat information

### System Configuration
- **Root Parameter**: Added parameter for root directory with default to current directory

## Version 1.1.0 (2025-05-12)

### Data Management
- **SRS Data**:
  - Added SRS export/import functionality
  - Added CSV export/import of SRS tables
  - Added export of results to CSV format

### User Interface
- **Navigation Improvements**:
  - Moved buttons in results page to align with other pages
  - Removed redundant "visa resultat" button in finish times page
  - Removed unnecessary "hantera deltagare" button in edit competition page
  - Fixed "ta bort" (delete) functionality in participants section
  - Improved navigation flow when saving an event (redirects to edit event instead of events list)

### User Experience
- **Time Input**: Enhanced Safari time input handler for better compatibility
