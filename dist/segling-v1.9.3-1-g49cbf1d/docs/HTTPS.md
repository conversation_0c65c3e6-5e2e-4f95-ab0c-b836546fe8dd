# Enabling HTTPS for Segling

This document explains how to enable HTTPS for the Segling application to secure the connection between clients and the server.

## Why Use HTTPS?

HTTPS provides several important benefits:

1. **Encryption**: All traffic between clients and the server is encrypted, protecting sensitive data.
2. **Authentication**: Ensures clients are connecting to the legitimate server, not an impostor.
3. **Data Integrity**: Prevents data from being modified in transit.

## Quick Start

### For Development (Self-Signed Certificates)

1. Generate self-signed certificates using the provided script:

   ```bash
   ./scripts/generate_certs.sh
   ```

2. Start the server with TLS enabled:

   ```bash
   ./segling -tls -cert certs/cert.pem -key certs/key.pem
   ```

3. Access the application at `https://localhost:8080` (or your configured port).

   **Note**: Browsers will show a warning about the self-signed certificate. This is normal for development environments.

### Importing Self-Signed Certificates to Browsers

To avoid browser warnings during development, you can import the self-signed certificate into your browser's trust store:

1. Generate certificates with enhanced settings (if you haven't already):

   ```bash
   ./scripts/generate_certs.sh
   ```

2. Export the certificate in browser-friendly formats:

   ```bash
   ./scripts/export_cert_for_browser.sh
   ```

3. Follow the browser-specific instructions provided by the script:

   #### For Chrome/Edge (Method 1 - Recommended):
   1. Open Chrome/Edge and navigate to `chrome://settings/certificates` (Edge: `edge://settings/certificates`)
   2. Go to the 'Authorities' tab
   3. Click 'Import' and select `certs/export/cert.crt`
   4. Check 'Trust this certificate for identifying websites' and click OK
   5. Restart the browser

   #### For Chrome/Edge (Method 2 - Alternative):
   1. Double-click the `certs/export/cert.p12` file
   2. Select 'Current User' for the store location
   3. Click Next, enter a blank password (just press Enter)
   4. Select 'Place all certificates in the following store' and choose 'Trusted Root Certification Authorities'
   5. Complete the wizard and restart the browser

   #### For Firefox:
   1. Open Firefox and navigate to `about:preferences#privacy`
   2. Scroll down to 'Certificates' and click 'View Certificates'
   3. Go to the 'Authorities' tab
   4. Click 'Import' and select `certs/export/cert.crt`
   5. Check 'Trust this CA to identify websites' and click OK
   6. Restart Firefox

   #### For Safari:
   1. Double-click the `certs/export/cert.crt` file to open it in Keychain Access
   2. It will be added to the 'login' keychain by default
   3. Find the certificate in the 'Certificates' category
   4. Double-click it, expand the 'Trust' section
   5. Change 'When using this certificate' to 'Always Trust'
   6. Close the window and enter your password when prompted
   7. Restart Safari

   #### Troubleshooting Certificate Warnings:
   1. Make sure you're accessing the site using exactly 'localhost' (not 127.0.0.1)
   2. Try clearing your browser cache and restarting the browser
   3. On Windows, you may need to import the certificate to the system store using the MMC console
   4. On macOS, make sure the certificate is in the System keychain, not just login
   5. Try using a different browser
   6. Verify the certificate has the correct Subject Alternative Names (SANs) by running:
      ```bash
      openssl x509 -in certs/cert.pem -noout -text | grep -A1 "Subject Alternative Name"
      ```

   **Warning**: Adding self-signed certificates to your browser's trust store reduces security. Only do this for development purposes and on trusted development machines.

### For Production (Trusted Certificates)

For production environments, you should obtain certificates from a trusted Certificate Authority (CA) like Let's Encrypt, DigiCert, or similar.

1. Obtain a certificate and private key from a trusted CA.

2. Start the server with TLS enabled, pointing to your trusted certificates:

   ```bash
   ./segling -tls -cert /path/to/cert.pem -key /path/to/key.pem
   ```

## Using Let's Encrypt (Recommended for Production)

[Let's Encrypt](https://letsencrypt.org/) provides free, automated, and trusted certificates.

1. Install [certbot](https://certbot.eff.org/):

   ```bash
   # On Ubuntu/Debian
   sudo apt-get update
   sudo apt-get install certbot

   # On macOS with Homebrew
   brew install certbot
   ```

2. Obtain a certificate:

   ```bash
   sudo certbot certonly --standalone -d your-domain.com
   ```

3. Start the server with the Let's Encrypt certificates:

   ```bash
   ./segling -tls -cert /etc/letsencrypt/live/your-domain.com/fullchain.pem -key /etc/letsencrypt/live/your-domain.com/privkey.pem
   ```

4. Set up automatic renewal:

   ```bash
   sudo certbot renew --dry-run  # Test renewal
   ```

   Add a cron job to renew certificates automatically:

   ```bash
   echo "0 0,12 * * * root python -c 'import random; import time; time.sleep(random.random() * 3600)' && certbot renew -q" | sudo tee -a /etc/crontab > /dev/null
   ```

## Advanced Configuration

### Redirect HTTP to HTTPS

For production environments, it's recommended to redirect all HTTP traffic to HTTPS. This can be achieved using a reverse proxy like Nginx or Apache.

### Using a Reverse Proxy

For production deployments, it's often better to use a reverse proxy (like Nginx, Apache, or Caddy) to handle TLS termination:

1. Configure your reverse proxy to handle HTTPS.
2. Run Segling without TLS and have the reverse proxy forward requests to it.

This approach provides additional benefits like load balancing, caching, and easier certificate management.

## Troubleshooting

### Certificate Issues

- **Browser Warning**: Self-signed certificates will always trigger browser warnings. This is expected in development.
- **Certificate Expired**: Certificates have an expiration date. Renew them before they expire.
- **Certificate Name Mismatch**: Ensure the certificate's Common Name (CN) or Subject Alternative Name (SAN) matches your domain.

### Connection Issues

- **Connection Refused**: Ensure the server is running and listening on the correct port.
- **Handshake Failed**: This could indicate a problem with the certificate or TLS configuration.

## Security Best Practices

1. **Keep Certificates Private**: Never share your private key.
2. **Use Strong Ciphers**: The default configuration uses strong ciphers.
3. **Regular Updates**: Keep your server and dependencies updated.
4. **Certificate Renewal**: Set up automatic certificate renewal to avoid expiration.
