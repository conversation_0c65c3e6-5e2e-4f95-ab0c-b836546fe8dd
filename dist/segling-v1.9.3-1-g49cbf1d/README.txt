Segling Application vv1.9.3-1-g49cbf1d
==============================

This is a sailing competition management application.

Quick Start:
1. Ensure you have the necessary permissions to run the executable
   chmod +x segling
2. Run the application
   ./segling
3. Open your browser and navigate to:
   http://localhost:8080

Enabling HTTPS (Secure Connection):
1. Generate self-signed certificates (for development)
   ./scripts/generate_certs.sh
2. Run the application with TLS enabled
   ./segling -tls -cert certs/cert.pem -key certs/key.pem
3. Open your browser and navigate to:
   https://localhost:8080
   (Note: Browsers will show a warning about self-signed certificates)

Importing Certificates to <PERSON><PERSON><PERSON> (to avoid warnings):
1. Export the certificate in browser-friendly formats
   ./scripts/export_cert_for_browser.sh
2. Follow the instructions provided by the script to import
   the certificate into your specific browser

Enabling Mutual TLS (Client Certificate Authentication):
1. Generate client certificates
   ./scripts/generate_client_certs.sh
2. Run the application with client certificate verification
   ./segling -tls -cert certs/cert.pem -key certs/key.pem -client-ca certs/client/ca.crt -require-client-cert
3. Import the client certificate to your browser
   (Password for the client certificate: segling)

Directory Structure:
- segling: The main executable
- pkg/templates/: HTML templates
- static/: CSS, JavaScript, and other static assets
- backups/: Directory for database backups
- certs/: Directory for TLS certificates
  - certs/export/: Exported certificates for browser import
  - certs/client/: Client certificates for mutual TLS
- credentials/: Directory for sensitive credentials (not included in backups)
- scripts/: Utility scripts
- docs/: Documentation
- segling.db: SQLite database file

Notes:
- The application will create and initialize the database on first run
- Backups will be stored in the backups/ directory
- The application runs on port 8080 by default
- Default club for new sailors can be configured in Settings
- Dark/light mode toggle is available in the top navigation bar

For more information, visit: https://github.com/rogge66/sailapp
