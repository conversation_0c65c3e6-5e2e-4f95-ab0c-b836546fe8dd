# Segling - Sailing Competition Management System

Segling is a web application for managing sailing competitions, sailors, and boats. It provides a user-friendly interface for organizing events, tracking participants, and managing SRS (Swedish Rating System) data.

![Segling Logo](https://img.shields.io/badge/Segling-Sailing%20Management-blue)
[![Go Version](https://img.shields.io/badge/Go-1.21-blue.svg)](https://golang.org/doc/go1.21)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Version](https://img.shields.io/badge/Version-1.9.1-orange.svg)](https://github.com/rogge66/sailapp/releases)
[![Build and Test](https://github.com/rogge66/sailapp/actions/workflows/build.yml/badge.svg)](https://github.com/rogge66/sailapp/actions/workflows/build.yml)
[![Lint](https://github.com/rogge66/sailapp/actions/workflows/lint.yml/badge.svg)](https://github.com/rogge66/sailapp/actions/workflows/lint.yml)

## Features

- **Sailor Management**: Add, edit, and delete sailor information with live search functionality
- **Boat Management**: Manage boats with SRS data, including automatic data retrieval from measurement certificates (mätbrev)
- **Event Management**: Create and manage sailing competitions with comprehensive features
  - Filter events by year
  - Copy participants between events
  - Manage event details including start time, wind speed, and course length
- **Competition Features**:
  - **Standard Racing**: Time-based results with SRS handicap calculations
  - **Pursuit Start (Jaktstart)**: Calculate and display start times for boats based on SRS values
  - **Entypsegling (One-Design)**: Placement-based results for one-design fleet racing without handicaps
  - **Finish Times**: Record finish times or placements for participants
  - **Results**: View results sorted by corrected sailing times or placements
  - **Printer-friendly Views**: Print pursuit start times and results
  - **GitHub Pages Integration**: Publish results to GitHub Pages for online sharing
  - **Google Drive Export**: Export results directly to Google Sheets for easy sharing and collaboration
  - **CSV Export**: Export results to CSV format for use in other applications
- **SRS Data Management**: Fetch and store SRS data from the Swedish Sailing Federation
  - Offline access to SRS tables and measurement certificates (mätbrev)
  - Live filtering and searching of SRS data with instant results
  - Support for all SRS types (standard, without spinnaker, shorthanded, shorthanded without spinnaker)
  - Custom SRS value overrides for competitions with clear UI separation between boat SRS values and custom values
  - Automatic validation of mätbrevsnummer format (B#### or E####)
- **Database Management**:
  - Automatic database backups when creating new events
  - Configurable backup settings
  - Backups stored in a dedicated directory with timestamps
- **Application Settings**:
  - Configurable default club for new sailors
  - Dark/light mode toggle
- **Responsive UI**: Built with Bootstrap and HTMX for a modern, responsive interface with consistent icons
- **User Experience**: Improved form handling with autocomplete prevention and intelligent data updates

## Technology Stack

- **Backend**: Go (Golang) with Gin web framework
- **Database**: SQLite
- **Frontend**: HTML, Bootstrap, HTMX
- **Templates**: Go's html/template package
- **External Data**: Integration with Swedish Sailing Federation's SRS database

## Getting Started

### Prerequisites

- Go 1.21 or higher
- Git

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/rogge66/sailapp.git
   cd sailapp
   ```

2. Build and run the application:
   ```bash
   go build -o segling ./cmd/server
   ./segling
   ```

   Alternatively, you can run it directly:
   ```bash
   go run cmd/server/main.go
   ```

3. Open your browser and navigate to:
   ```
   http://localhost:8080
   ```

### Building a Distribution Package

The project includes build scripts for creating distribution packages for both Unix-like systems (macOS, Linux) and Windows.

#### For Unix-like systems (macOS, Linux):

```bash
./build.sh
```

This will:
- Compile the application
- Create a directory structure with all necessary files
- Package everything into a zip file at `dist/segling-1.0.0.zip`

#### For Windows:

```
build.bat
```

This will:
- Compile the application
- Create a directory structure with all necessary files
- Place the package in the `dist\segling-1.0.0` directory

#### Package Contents

The distribution package includes:
- The compiled executable (`segling` or `segling.exe`)
- All HTML templates
- Static assets (CSS, JavaScript)
- An empty database file (will be initialized on first run)
- A README file with instructions
- A start script for easy launching

#### Distributing the Application

To distribute the application, simply share the zip file created by the build script. Users can extract it and run the application using the included start script without needing to install Go or any other dependencies.

## Project Structure

```
segling/
├── cmd/
│   ├── dbdump/         # Database dump utility
│   └── server/         # Main application entry point
├── pkg/
│   ├── database/       # Database operations
│   ├── handlers/       # HTTP request handlers
│   ├── models/         # Data models
│   ├── templates/      # HTML templates
│   └── utils/          # Utility functions
├── static/             # Static assets (CSS, JS)
├── tests/              # Test documentation
├── backups/            # Database backup storage
├── build.sh            # Unix build script
├── build.bat           # Windows build script
├── go.mod              # Go module definition
├── go.sum              # Go module checksums
└── README.md           # This file
```

## Usage

### Managing Sailors

Navigate to the "Sailors" section to add, edit, or delete sailor information. Each sailor has a name and phone number. Use the live search functionality to quickly find sailors.

### Managing Boats

Navigate to the "Boats" section to manage your fleet. You can:
- Add new boats manually
- Search for boats by type in the SRS database with live filtering
- Search for boats by "mätbrevsnummer" (measurement certificate number) with live filtering
- Automatically populate boat data from measurement certificates
- Automatic validation of mätbrevsnummer format (B#### or E####)
- Automatic update of boat name when fetching data from mätbrev
- View and edit SRS data including standard and shorthanded ratings
- View S/H values in the boat list

### Managing Competitions

Navigate to the "Tävlingar" (Competitions) section to manage sailing competitions:

#### Creating and Editing Competitions
- Create new competitions with name, date, and description
- Set start time, wind speed, and course length
- Enable or disable pursuit start (jaktstart)
- Enable entypsegling (one-design) format for placement-based results
- Set boat type for entypsegling competitions
- Filter competitions by year
- Copy participants from one competition to another
- Lock competitions to preserve results even if boat data changes
- Unlock competitions if changes are needed (with appropriate warnings)

#### Managing Participants
- Add sailors and boats to competitions (standard racing)
- Add sailors with personal sail numbers for entypsegling competitions
- Select which SRS value to use for each participant (standard, without spinnaker, shorthanded, etc.)
- Override SRS values with custom values if needed
  - Clear UI separation between boat's SRS value (read-only) and custom SRS value (editable when enabled)
  - Enable/disable custom SRS values with a simple checkbox
  - Custom values are preserved when editing participants
- Edit participants after they've been added

#### Pursuit Start (Jaktstart)
- View calculated start times for each boat based on SRS values
- Print individual start cards for participants

#### Finish Times
- Record finish times for participants in HH:MM:SS format (standard racing)
- Record placements for entypsegling competitions (1st, 2nd, 3rd, etc.)
- Support for tied finishes in entypsegling
- Mark DNS (Did Not Start) or DNF (Did Not Finish) status
- View start times for pursuit start competitions

#### Results
- View results sorted by corrected sailing time (standard racing) or placement (entypsegling)
- See time differences between boats (standard racing) or points (entypsegling)
- Support for tied finishes with average points calculation in entypsegling
- Print results in a printer-friendly format
- Publish results to GitHub Pages for online sharing
  - Share results with participants and spectators via a public URL
  - Automatically formatted for both desktop and mobile viewing
  - Includes all relevant race information and complete results table
  - Supports both standard racing and entypsegling formats
- Export results to Google Drive as Google Sheets
  - Direct integration with Google Drive for easy sharing and collaboration
  - Configurable folder organization and file naming conventions
  - Automatic formatting with event details and complete results
  - Supports both standard racing and entypsegling formats
- Export results to CSV format for use in other applications

### SRS Data Management

Navigate to the "SRS Data" section to:
- Fetch and store the latest SRS data from the Swedish Sailing Federation
- Access SRS data offline for instant searching
- View and search SRS tables with live filtering
- View and search measurement certificates with live filtering
- Automatically populate boat type and name from measurement certificate data
- Strict validation of mätbrevsnummer format to prevent unnecessary API calls
- User-friendly error messages for invalid or not found mätbrevsnummer

### Settings

Navigate to the "Inställningar" (Settings) section to:
- Configure automatic database backups when creating new events
- Set the default club for new sailors
- Configure Google Drive integration for result exports
- Set up GitHub Pages integration for public result sharing
- View information about where backups are stored
- Manage application preferences

The automatic backup feature creates a timestamped copy of the database file each time a new event is created, providing an easy way to recover data if needed.

The default club setting allows you to specify which sailing club should be pre-filled when creating new sailors, making it faster to add multiple sailors from the same club.

## Testing

The project includes a comprehensive test suite covering database operations, HTTP handlers, and utility functions.

Run all tests using the provided script:

```bash
./run_tests.sh
```

Or run specific test packages:

```bash
go test -v ./pkg/database
go test -v ./pkg/handlers
go test -v ./pkg/utils
```

See the [tests/README.md](tests/README.md) file for more information about the test suite.

## Continuous Integration

This project uses GitHub Actions for continuous integration and deployment:

### Build and Test Workflow

The [Build and Test](.github/workflows/build.yml) workflow runs on every push to the main branch and on pull requests:

- Builds the application on multiple platforms (Linux, macOS, Windows)
- Runs all tests to ensure code quality
- Creates releases automatically when tags are pushed
- Builds binaries for multiple platforms and attaches them to the release

### Lint Workflow

The [Lint](.github/workflows/lint.yml) workflow runs on every push to the main branch and on pull requests:

- Uses golangci-lint to check code quality
- Ensures code follows best practices and style guidelines

### Documentation Workflow

The [Documentation](.github/workflows/pages.yml) workflow runs on every push to the main branch and when tags are pushed:

- Generates documentation from the project's markdown files
- Deploys the documentation to GitHub Pages
- Makes the documentation available at https://rogge66.github.io/sailapp/

## Database Utilities

The project includes a database dump utility for debugging and inspecting the database:

```bash
./dbdump.sh
```

See the [cmd/dbdump/README.md](cmd/dbdump/README.md) file for more information about the utility.

### Database Backups

The application can automatically create backups of the database when new events are created. These backups are stored in the `backups` directory in the same location as the database file.

Each backup is named with a timestamp, for example: `segling_20230515_123045.db`.

To restore from a backup:
1. Stop the application
2. Copy the backup file to replace the current database file
3. Restart the application

You can enable or disable automatic backups in the Settings page.

#### Backups in Distribution Packages

When using the distribution packages created by the build scripts, the `backups` directory is already included in the package structure. The application will automatically use this directory for storing backups.

If you're distributing the application to users, make sure to inform them about the backup feature and where to find the backup files in case they need to restore from a backup.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Swedish Sailing Federation](https://www.svensksegling.se/) for providing the SRS data
- [Gin Web Framework](https://github.com/gin-gonic/gin)
- [Bootstrap](https://getbootstrap.com/)
- [HTMX](https://htmx.org/)
