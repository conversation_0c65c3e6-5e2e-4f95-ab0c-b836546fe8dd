#!/bin/bash
# Start script for Segling application with HTTPS enabled

# Check if certificates exist
if [ ! -f "certs/cert.pem" ] || [ ! -f "certs/key.pem" ]; then
    echo "TLS certificates not found. Generating self-signed certificates..."
    ./scripts/generate_certs.sh
fi

# Make sure the executable has the right permissions
chmod +x segling

# Run the application with TLS enabled
./segling -tls -cert certs/cert.pem -key certs/key.pem

# If the application exits, wait for user input before closing the terminal
echo "Application has stopped. Press Enter to exit."
read
