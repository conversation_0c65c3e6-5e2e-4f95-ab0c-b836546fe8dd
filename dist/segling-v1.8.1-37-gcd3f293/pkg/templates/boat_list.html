{{ if .boats }}
<div class="table-responsive">
    <table class="table table-striped table-sm">
        <thead>
            <tr>
                <th>Namn</th>
                <th><PERSON><PERSON><PERSON>p</th>
                <th>Mätbrevs nr</th>
                <th>Segelnr</th>
                <th>Nat</th>
                <th class="text-center">SRS</th>
                <th class="text-center">SRS utan<br>undanvindsegel</th>
                <th class="text-center">SRS S/H</th>
                <th class="text-center">SRS S/H utan<br>undanvindsegel</th>
                <th>Åtgärder</th>
            </tr>
        </thead>
        <tbody>
            {{ range .boats }}
            <tr>
                <td>{{ .Namn }}</td>
                <td>{{ .Battyp }}</td>
                <td>{{ .MatbrevsNummer }}</td>
                <td>{{ .Segelnummer }}</td>
                <td>{{ .Nationality }}</td>
                <td class="text-center">{{ .SRS }}</td>
                <td class="text-center">{{ .SRSUtanUndanvindsegel }}</td>
                <td class="text-center">{{ .SRSShorthanded }}</td>
                <td class="text-center">{{ .SRSShorthandedUtanUndanvindsegel }}</td>
                <td>
                    <div class="btn-group">
                        <a href="/boats/{{ .ID }}" class="btn btn-sm btn-outline-primary">Redigera</a>
                        <button class="btn btn-sm btn-outline-danger"
                            hx-delete="/boats/{{ .ID }}"
                            hx-confirm="Är du säker på att du vill ta bort denna båt?"
                            hx-target="closest tr"
                            hx-swap="delete swap:1s">
                            Ta bort
                        </button>
                    </div>
                </td>
            </tr>
            {{ end }}
        </tbody>
    </table>
</div>
{{ else }}
<p>Inga båtar hittades.</p>
{{ end }}
