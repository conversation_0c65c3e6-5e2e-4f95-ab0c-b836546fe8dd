{{ template "header.html" . }}

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3>Tävlingar</h3>
                        <div class="filters d-flex gap-2">
                            <form action="/" method="get" id="filter-form" class="d-flex align-items-center bg-light p-2 rounded">
                                <div class="d-flex align-items-center me-3">
                                    <label for="year" class="me-2 fw-bold">År:</label>
                                    <select name="year" id="year" class="form-select form-select-sm" onchange="document.getElementById('filter-form').submit()">
                                        <option value="0" {{ if eq .selectedYear 0 }}selected{{ end }}>Alla år</option>
                                        {{ range .years }}
                                        <option value="{{ . }}" {{ if eq . $.selectedYear }}selected{{ end }}>{{ . }}</option>
                                        {{ end }}
                                    </select>
                                </div>
                                <div class="d-flex align-items-center">
                                    <label for="type" class="me-2 fw-bold">Typ:</label>
                                    <select name="type" id="type" class="form-select form-select-sm" onchange="document.getElementById('filter-form').submit()">
                                        <option value="" {{ if eq .selectedType "" }}selected{{ end }}>Alla typer</option>
                                        {{ range .competitionTypesList }}
                                        <option value="{{ . }}" {{ if eq . $.selectedType }}selected{{ end }}>{{ . }}</option>
                                        {{ end }}
                                    </select>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="card-body">
                        {{ if .events }}
                        <div class="events-list-container" style="max-height: 650px; overflow-y: auto; margin-bottom: 15px;">
                            <ul class="list-group">
                                {{ range .events }}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center" style="flex: 1;">
                                        <div class="me-3" style="min-width: 100px;">
                                            <span class="text-muted">{{ .Datum.Format "2006-01-02" }}</span>
                                        </div>
                                        <div class="me-3" style="flex: 2;">
                                            <a href="/events/{{ .ID }}" class="fw-bold">{{ .Namn }}</a>
                                        </div>
                                        {{ if .Tavlingstyp }}
                                        <div class="me-3" style="min-width: 120px;">
                                            <span class="badge bg-secondary">{{ .Tavlingstyp }}</span>
                                        </div>
                                        {{ end }}
                                        <div class="me-3">
                                            <span class="badge bg-secondary" title="Antal deltagare">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                    <path d="M0 13.5C0 12.1 1.1 11 2.5 11h11c1.4 0 2.5 1.1 2.5 2.5S14.9 16 13.5 16h-11C1.1 16 0 14.9 0 13.5z"/>
                                                    <path d="M2 9l1-1h10l1 1"/>
                                                    <path d="M13 7l-1-5H8L4 7"/>
                                                    <path d="M8 2V1"/>
                                                </svg>
                                                {{ .BoatCount }}
                                            </span>
                                        </div>
                                        <div class="me-3">
                                            <span class="badge bg-info" title="Antal deltagare">
                                                <i class="bi bi-people-fill"></i> {{ .ParticipantCount }}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <!-- TEMPORARILY DISABLED: Lock status icons -->
                                        <!-- {{ if .Locked }}
                                        <span class="badge bg-danger" title="Låst tävling">
                                            <i class="bi bi-lock-fill"></i>
                                        </span>
                                        {{ else }}
                                        <span class="badge bg-success" title="Öppen tävling">
                                            <i class="bi bi-unlock-fill"></i>
                                        </span>
                                        {{ end }} -->
                                    </div>
                                </li>
                                {{ end }}
                                <!-- Summary row at the bottom -->
                                <li class="list-group-item d-flex justify-content-between align-items-center bg-light">
                                    <div class="d-flex align-items-center" style="flex: 1;">
                                        <div class="me-3" style="min-width: 100px;">
                                            <span class="fw-bold">Totalt</span>
                                        </div>
                                        <div class="me-3" style="flex: 2;">
                                            <span class="fw-bold">{{ len .events }} tävlingar</span>
                                        </div>
                                        <div class="me-3" style="min-width: 120px;">
                                            <!-- Empty space to align with competition type -->
                                        </div>
                                        <div class="me-3">
                                            <span class="badge bg-dark" title="Totalt antal deltagare">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                    <path d="M0 13.5C0 12.1 1.1 11 2.5 11h11c1.4 0 2.5 1.1 2.5 2.5S14.9 16 13.5 16h-11C1.1 16 0 14.9 0 13.5z"/>
                                                    <path d="M2 9l1-1h10l1 1"/>
                                                    <path d="M13 7l-1-5H8L4 7"/>
                                                    <path d="M8 2V1"/>
                                                </svg>
                                                {{ .totalBoats }}
                                            </span>
                                        </div>
                                        <div class="me-3">
                                            <span class="badge bg-dark" title="Totalt antal deltagare">
                                                <i class="bi bi-people-fill"></i> {{ .totalParticipants }}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <!-- Empty space to align with lock icon -->
                                    </div>
                                </li>
                            </ul>
                        </div>
                        {{ else }}
                        <p>Inga tävlingar hittades.</p>
                        {{ end }}
                        <div class="mt-3">
                            <a href="/events/new" class="btn btn-primary">Skapa ny tävling</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

{{ template "footer.html" . }}
