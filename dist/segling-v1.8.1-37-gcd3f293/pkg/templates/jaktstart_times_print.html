<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>J<PERSON><PERSON><PERSON>ttider - {{ .event.Namn }} - Utskrift</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @media print {
            body {
                font-size: 12pt;
            }
            .no-print {
                display: none;
            }
            .container {
                width: 100%;
                max-width: 100%;
            }
            @page {
                size: A4;
                margin: 0.5cm;
            }
        }

        /* Card styles for individual participant notes */
        .participant-card {
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 15px;
            page-break-inside: avoid;
            background-color: #f8f9fa;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .participant-card h4 {
            margin-top: 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            font-size: 16px;
        }

        .participant-card .start-time {
            font-size: 24px;
            font-weight: bold;
            color: #dc3545;
            text-align: center;
            margin: 10px 0;
        }

        .participant-card .details {
            font-size: 14px;
            margin-bottom: 5px;
        }

        .participant-card .event-info {
            font-size: 12px;
            color: #6c757d;
            border-top: 1px solid #ddd;
            padding-top: 5px;
            margin-top: 5px;
        }

        /* Grid layout for cards */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        @media (min-width: 768px) {
            .card-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media print {
            .card-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Scissors line for cutting */
        .cut-line {
            border: 1px dashed #999;
            margin: 0;
            position: relative;
        }

        .cut-line::after {
            content: "✂";
            position: absolute;
            top: -10px;
            left: -15px;
            font-size: 16px;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row mb-4 no-print">
            <div class="col-12">
                <h1>Jaktstarttider - {{ .event.Namn }}</h1>
                <p>
                    <strong>Datum:</strong> {{ .event.Datum.Format "2006-01-02" }} |
                    <strong>Starttid:</strong> {{ .event.Starttid }} |
                    <strong>Vind:</strong> {{ .event.Vind }} m/s |
                    <strong>Banlängd:</strong> {{ .event.Banlangd }} nm
                </p>
                <div class="mb-3">
                    <button class="btn btn-primary" onclick="window.print()">Skriv ut</button>
                    <a href="/events/{{ .event.ID }}/jaktstart" class="btn btn-secondary">Tillbaka</a>
                </div>
                <div class="alert alert-info">
                    <p><strong>Instruktioner:</strong> Skriv ut denna sida och klipp längs de streckade linjerna för att skapa individuella startkort till varje deltagare.</p>
                </div>
            </div>
        </div>

        {{ if .participants }}
        <div class="card-grid">
            {{ range .participants }}
            <div class="participant-card">
                <h4>{{ .Sailor.Namn }}</h4>
                <div class="details">
                    <strong>Båt:</strong> {{ .Boat.Namn }} ({{ .Boat.Battyp }})
                    {{ if or .Boat.Segelnummer .Boat.Nationality }}
                        <br>
                        <small>
                            {{ if and .Boat.Nationality .Boat.Segelnummer }}
                                {{ .Boat.Nationality }}-{{ .Boat.Segelnummer }}
                            {{ else if .Boat.Segelnummer }}
                                {{ .Boat.Segelnummer }}
                            {{ else if .Boat.Nationality }}
                                {{ .Boat.Nationality }}
                            {{ end }}
                        </small>
                    {{ end }}
                </div>
                <div class="details">
                    <strong>SRS:</strong>
                    {{ if .UseCustomSRSValue }}
                        {{ .CustomSRSValue }} (anpassad)
                    {{ else }}
                        {{ .SelectedSRSValue }}
                        {{ if eq .SRSType "srs" }}
                            (SRS)
                        {{ else if eq .SRSType "srs_utan_undanvindsegel" }}
                            (SRS utan undanvindsegel)
                        {{ else if eq .SRSType "srs_shorthanded" }}
                            (SRS S/H)
                        {{ else if eq .SRSType "srs_shorthanded_utan_undanvindsegel" }}
                            (SRS S/H utan undanvindsegel)
                        {{ end }}
                    {{ end }}
                </div>
                <div class="start-time">{{ .AbsoluteStartTime }}</div>
                <div class="details"><strong>Offset:</strong> {{ .StartTimeOffset }}</div>
                <div class="event-info">
                    <div><strong>{{ $.event.Namn }}</strong> - {{ $.event.Datum.Format "2006-01-02" }}</div>
                    <div>Vind: {{ $.event.Vind }} m/s | Banlängd: {{ $.event.Banlangd }} nm</div>
                </div>
            </div>
            {{ end }}
        </div>
        {{ else }}
        <div class="alert alert-warning">
            <p>Inga deltagare hittades.</p>
        </div>
        {{ end }}
    </div>

    <script>
        // Auto-print when the page loads
        window.onload = function() {
            // Wait a moment for the page to fully render
            setTimeout(function() {
                // Uncomment the line below to automatically print when the page loads
                // window.print();
            }, 500);
        };
    </script>
</body>
</html>
