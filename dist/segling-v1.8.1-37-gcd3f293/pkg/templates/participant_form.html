<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#0d6efd">
    <title>{{ .title }} - Segling</title>
    <!-- Prevent flash of unstyled content in dark mode -->
    <script>
        // Apply theme immediately before page renders
        (function() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                document.documentElement.setAttribute('data-bs-theme', savedTheme);
            } else {
                const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
                const initialTheme = prefersDarkMode ? 'dark' : 'light';
                document.documentElement.setAttribute('data-bs-theme', initialTheme);
                localStorage.setItem('theme', initialTheme);
            }

            // Add a class to body to prevent transition effects on initial load
            document.addEventListener('DOMContentLoaded', function() {
                document.body.classList.add('theme-transition-ready');
            });
        })();
    </script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <script src="https://unpkg.com/htmx.org@1.9.2"></script>
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="icon" href="/static/favicon.png" type="image/png">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">Segling</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Hem</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/sailors">Seglare</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/boats">Båtar</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/events">Tävlingar</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/srs">SRS Data</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/settings">Inställningar</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/help">Hjälp</a>
                    </li>
                </ul>
                <div class="theme-toggle nav-link text-white" id="theme-toggle" aria-label="Toggle dark/light mode">
                    <i class="bi bi-sun-fill" id="theme-icon"></i>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h2>{{ .title }}</h2>
                    </div>
                    <div class="card-body">
                        <form id="participant-form" action="/participants/update" method="post">
                            <input type="hidden" name="id" value="{{ .ID }}">
                            <input type="hidden" name="event_id" value="{{ .EventID }}">

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Seglare</label>
                                    <p class="form-control-static">{{ .Sailor.Namn }}</p>
                                </div>
                                <div class="col-md-6">
                                    {{ if .Event.Entypsegling }}
                                    <label class="form-label">Segelnummer</label>
                                    <input type="text" class="form-control" name="personal_number" value="{{ .Participant.PersonalNumber }}" placeholder="t.ex. SWE 123">
                                    <small class="form-text text-muted">Segelnummer för tävlingen ({{ .Event.BoatType }})</small>
                                    {{ else }}
                                    <label class="form-label">Båt</label>
                                    <p class="form-control-static">
                                        {{ .Boat.Namn }} ({{ .Boat.Battyp }})
                                        {{ if or .Boat.Segelnummer .Boat.Nationality }}
                                            <br>
                                            <small class="text-muted">
                                                {{ if and .Boat.Nationality .Boat.Segelnummer }}
                                                    {{ .Boat.Nationality }}-{{ .Boat.Segelnummer }}
                                                {{ else if .Boat.Segelnummer }}
                                                    {{ .Boat.Segelnummer }}
                                                {{ else if .Boat.Nationality }}
                                                    {{ .Boat.Nationality }}
                                                {{ end }}
                                            </small>
                                        {{ end }}
                                    </p>
                                    {{ end }}
                                </div>
                            </div>

                            {{ if not .Event.Entypsegling }}
                            <!-- SRS fields for regular events -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="srs_type" class="form-label">SRS-typ</label>
                                    <select class="form-select" id="srs_type" name="srs_type">
                                        <option value="srs" {{ if or (eq .SRSType "srs") (eq .SRSType "") }}selected{{ end }}>SRS ({{ .Boat.SRS }})</option>
                                        <option value="srs_utan_undanvindsegel" {{ if eq .SRSType "srs_utan_undanvindsegel" }}selected{{ end }}>SRS utan undanvindsegel ({{ .Boat.SRSUtanUndanvindsegel }})</option>
                                        <option value="srs_shorthanded" {{ if eq .SRSType "srs_shorthanded" }}selected{{ end }}>SRS S/H ({{ .Boat.SRSShorthanded }})</option>
                                        <option value="srs_shorthanded_utan_undanvindsegel" {{ if eq .SRSType "srs_shorthanded_utan_undanvindsegel" }}selected{{ end }}>SRS S/H utan undanvindsegel ({{ .Boat.SRSShorthandedUtanUndanvindsegel }})</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="boat_srs_value" class="form-label">SRS-värde från båt</label>
                                    <input type="number" class="form-control" id="boat_srs_value" step="0.001" readonly>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="custom_srs_value" class="form-label">Anpassat SRS-värde</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="custom_srs_value" name="custom_srs_value" step="0.001"
                                               value="{{ if .UseCustomSRSValue }}{{ .CustomSRSValue }}{{ end }}"
                                               {{ if not .UseCustomSRSValue }}readonly{{ end }}>
                                        <div class="input-group-text">
                                            <input class="form-check-input mt-0" type="checkbox" id="use_custom_srs_value" name="use_custom_srs_value"
                                                   {{ if .UseCustomSRSValue }}checked{{ end }} onchange="toggleCustomSRSValue(this)">
                                            <label class="form-check-label ms-2" for="use_custom_srs_value">Använd</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="crew_count" class="form-label">Antal gastar</label>
                                    <div class="input-group">
                                        <input type="number" min="0" class="form-control" id="crew_count" name="crew_count" value="{{ .CrewCount }}">
                                        <div class="input-group-text">
                                            <small>Exklusive skeppare</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{ else }}
                            <!-- Crew count field for entypsegling events -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="crew_count" class="form-label">Antal gastar</label>
                                    <div class="input-group">
                                        <input type="number" min="0" class="form-control" id="crew_count" name="crew_count" value="{{ .CrewCount }}">
                                        <div class="input-group-text">
                                            <small>Exklusive skeppare</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{ end }}

                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">Spara</button>
                                    <a href="/events/{{ .EventID }}/participants" class="btn btn-secondary">Avbryt</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="mt-5 py-3 bg-light text-center">
        <div class="container">
            <p class="mb-0">© 2025 Segling</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/scripts.js"></script>
    <script>
        // Handle errors in HTMX requests
        document.addEventListener('htmx:responseError', function(event) {
            // Show an error message
            alert('Ett fel uppstod: ' + (event.detail.xhr.responseText || 'Okänt fel'));
        });

        // Initialize theme toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            initTheme();
        });

        // Get boat SRS values from Go templates
        const boatSRS = "{{ .Boat.SRS }}";
        const boatSRSUtanUndanvindsegel = "{{ .Boat.SRSUtanUndanvindsegel }}";
        const boatSRSShorthanded = "{{ .Boat.SRSShorthanded }}";
        const boatSRSShorthandedUtanUndanvindsegel = "{{ .Boat.SRSShorthandedUtanUndanvindsegel }}";

        // Simple function to get boat SRS value based on SRS type
        function getBoatSRSValue(srsType) {
            let srsValue = 0;
            switch (srsType) {
                case 'srs':
                    srsValue = boatSRS;
                    break;
                case 'srs_utan_undanvindsegel':
                    srsValue = boatSRSUtanUndanvindsegel;
                    break;
                case 'srs_shorthanded':
                    srsValue = boatSRSShorthanded;
                    break;
                case 'srs_shorthanded_utan_undanvindsegel':
                    srsValue = boatSRSShorthandedUtanUndanvindsegel;
                    break;
            }
            return srsValue;
        }

        // Update the boat SRS value field based on the selected SRS type
        function updateBoatSRSValue() {
            const srsType = document.getElementById('srs_type').value;
            const boatSRSValueInput = document.getElementById('boat_srs_value');

            if (!boatSRSValueInput) return;

            // Get the SRS value for the selected type
            const srsValue = getBoatSRSValue(srsType);

            // Update the boat SRS value field
            boatSRSValueInput.value = srsValue;
        }

        // Handle checkbox state changes
        function handleCheckboxChange(checkbox) {
            const customSRSValueInput = document.getElementById('custom_srs_value');
            const boatSRSValueInput = document.getElementById('boat_srs_value');

            if (checkbox.checked) {
                // If checked, enable the field
                customSRSValueInput.readOnly = false;

                // If the field is empty, initialize with boat SRS value
                if (!customSRSValueInput.value && boatSRSValueInput) {
                    customSRSValueInput.value = boatSRSValueInput.value;
                }
            } else {
                // If unchecked, disable the field and clear it
                customSRSValueInput.readOnly = true;
                customSRSValueInput.value = '';
            }
        }

        // Initialize the form on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Update the boat SRS value field
            updateBoatSRSValue();

            // Directly set the custom SRS value and readonly state on page load
            const customSRSValueInput = document.getElementById('custom_srs_value');
            const useCustomSRSValue = {{ .UseCustomSRSValue }};
            const customSRSValue = "{{ .CustomSRSValue }}";

            if (useCustomSRSValue) {
                // If custom value should be used, make sure it's set and editable
                customSRSValueInput.readOnly = false;
                customSRSValueInput.value = customSRSValue;
            } else {
                // If custom value should not be used, make it readonly and empty
                customSRSValueInput.readOnly = true;
                customSRSValueInput.value = '';
            }

            // Set up the checkbox change handler for future changes
            const checkbox = document.getElementById('use_custom_srs_value');
            checkbox.addEventListener('change', function() {
                handleCheckboxChange(this);
            });

            // Update SRS value when type changes
            document.getElementById('srs_type').addEventListener('change', function() {
                updateBoatSRSValue();

                // If custom is enabled and empty, update it too
                const customSRSValueInput = document.getElementById('custom_srs_value');
                const useCustomSRSValue = document.getElementById('use_custom_srs_value').checked;
                const boatSRSValueInput = document.getElementById('boat_srs_value');

                if (useCustomSRSValue && !customSRSValueInput.value && boatSRSValueInput) {
                    customSRSValueInput.value = boatSRSValueInput.value;
                }
            });
        });
    </script>
</body>
</html>
