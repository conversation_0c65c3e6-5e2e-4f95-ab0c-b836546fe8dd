<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resultat - {{ .event.Namn }}{{ if .isMultiHeat }}{{ if .showOverall }} - Totala resultat{{ else if .selectedHeat.Name }} - {{ .selectedHeat.Name }}{{ end }}{{ end }} - Segling</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @media print {
            body {
                font-size: 9pt;
            }
            .no-print {
                display: none;
            }
            .container {
                width: 100%;
                max-width: 100%;
            }
            .table {
                width: 100%;
                max-width: 100%;
                border-collapse: collapse;
                table-layout: fixed;
            }
            .table th, .table td {
                padding: 0.3rem;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
            .table th {
                font-size: 9pt;
                font-weight: bold;
            }
            .table-striped > tbody > tr:nth-of-type(odd) {
                background-color: rgba(0, 0, 0, 0.18) !important;
            }
            .small {
                font-size: 8pt;
            }
            @page {
                size: landscape;
                margin: 0.8cm;
            }
        }

        /* Also apply smaller font for screen view */
        .table {
            font-size: 9pt;
            table-layout: fixed;
        }
        .table th {
            font-size: 9pt;
            font-weight: bold;
        }
        .table td {
            padding: 0.3rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .small {
            font-size: 8pt;
        }

        /* Striped table styling for screen view */
        .table-striped > tbody > tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.18) !important;
        }

        /* Column width definitions */
        .col-plac {
            width: 20px;
        }
        .col-seglare {
            width: 100px;
        }
        .col-klubb {
            width: 40px;
        }
        .col-battyp {
            width: 70px;
        }
        .col-segelnr {
            width: 50px;
        }
        .col-matbrev {
            width: 50px;
        }
        .col-srs {
            width: 65px;
        }
        .col-besattn {
            width: 40px;
        }
        .col-time {
            width: 40px;
        }
        .col-elapsed {
            width: 60px;
        }
        .col-corrected {
            width: 60px;
        }
        .col-diff {
            width: 60px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-12">
                <h1>Resultat - {{ .event.Namn }}{{ if .isMultiHeat }}{{ if .showOverall }} - Totala resultat{{ else if .selectedHeat.Name }} - {{ .selectedHeat.Name }}{{ end }}{{ end }}</h1>
                {{ $totalCrew := 0 }}
                {{ if .usingSavedResults }}
                    {{ range .savedResults }}
                        {{ $totalCrew = add $totalCrew (add .CrewCount 1) }}
                    {{ end }}
                {{ else if .totalResults }}
                    {{ range .totalResults }}
                        {{ $totalCrew = add $totalCrew (add .EventParticipant.CrewCount 1) }}
                    {{ end }}
                {{ else if .heatResults }}
                    {{ range .heatResults }}
                        {{ $totalCrew = add $totalCrew .TotalPersons }}
                    {{ end }}
                {{ end }}
                <p style="line-height: 1.2;">
                    <strong>Datum:</strong> {{ .event.Datum.Format "2006-01-02" }} | <strong>Starttid:</strong> {{ .event.Starttid }} | <strong>Vind:</strong> {{ .event.Vind }} m/s | <strong>Banlängd:</strong> {{ .event.Banlangd }} nm | <strong>Deltagare:</strong> {{ $totalCrew }}{{ if not .event.Entypsegling }} | <em>* = anpassat SRS-värde</em>{{ end }}
                </p>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                {{ if or .savedResults .totalResults .heatResults }}

                <div class="table-responsive">
                    <table class="table table-striped" style="--bs-table-striped-bg: rgba(0, 0, 0, 0.18);">
                        <thead>
                            <tr>
                                {{ if .event.Entypsegling }}
                                    {{ if .totalResults }}
                                    <!-- Entypsegling total results: Placering, Seglare, Segelnummer, Besättning, Totala poäng, Heat columns -->
                                    <th class="col-plac">Placering</th>
                                    <th class="col-seglare">Seglare</th>
                                    <th class="col-segelnr">Segelnummer</th>
                                    <th class="col-besattn">Besättning</th>
                                    <th class="col-diff">Totala poäng</th>
                                    {{ range .heats }}
                                    <th class="col-diff">{{ .Name }}</th>
                                    {{ end }}
                                    {{ else }}
                                    <!-- Entypsegling individual heat: Plats, Seglare, Klubb, Segelnummer, Besättning, Poäng -->
                                    <th class="col-plac">Plats</th>
                                    <th class="col-seglare">Seglare</th>
                                    <th class="col-klubb">Klubb</th>
                                    <th class="col-segelnr">Segelnummer</th>
                                    <th class="col-besattn">Besättning</th>
                                    <th class="col-diff">Poäng</th>
                                    {{ end }}
                                {{ else }}
                                    <!-- Regular sailing events: show boat info, SRS, etc. -->
                                    <th class="col-plac">Plac.</th>
                                    <th class="col-seglare">Seglare</th>
                                    <th class="col-klubb">Klubb</th>
                                    <th class="col-battyp">Båttyp</th>
                                    <th class="col-segelnr">Segelnr</th>
                                    <th class="col-matbrev">Mätbrev</th>
                                    <th class="col-srs">SRS</th>
                                    <th class="col-besattn">Besättn.</th>
                                    {{ if .totalResults }}
                                    <th class="col-diff">Totala poäng</th>
                                    {{ range .heats }}
                                    <th class="col-diff">{{ .Name }}</th>
                                    {{ end }}
                                    {{ else }}
                                    {{ if .event.Jaktstart }}
                                    <th class="col-time">Starttid</th>
                                    {{ end }}
                                    <th class="col-time">Måltid</th>
                                    <th class="col-elapsed">Seglad tid</th>
                                    <th class="col-corrected">Korr. tid</th>
                                    <th class="col-diff">Efter föregående</th>
                                    <th class="col-diff">Efter vinnare</th>
                                    {{ if and .heatResults .isMultiHeat }}
                                    <th class="col-diff">Poäng</th>
                                    {{ end }}
                                    {{ end }}
                                {{ end }}
                            </tr>
                        </thead>
                        <tbody>
                            {{ if .usingSavedResults }}
                                {{ range $result := .savedResults }}
                                <tr>
                                    <td class="col-plac">{{ $result.Position }}</td>
                                    <td class="col-seglare">{{ $result.SailorName }}</td>
                                    <td class="col-klubb">{{ $result.SailorClub }}</td>
                                    <td class="col-battyp">{{ if gt (len $result.BoatType) 14 }}{{ slice $result.BoatType 0 14 }}...{{ else }}{{ $result.BoatType }}{{ end }}</td>
                                    <td class="col-segelnr">
                                        {{ if and $result.Nationality $result.Segelnummer }}
                                            {{ $result.Nationality }}-{{ $result.Segelnummer }}
                                        {{ else if $result.Segelnummer }}
                                            {{ $result.Segelnummer }}
                                        {{ else if $result.Nationality }}
                                            {{ $result.Nationality }}
                                        {{ end }}
                                    </td>
                                    <td class="col-matbrev">{{ $result.MatbrevsNummer }}</td>
                                    <td class="col-srs">
                                        {{ $result.SRSValue }}
                                        {{ if $result.UseCustomSRSValue }}
                                            (*)
                                        {{ else if eq $result.SRSType "srs" }}
                                            (spinn)
                                        {{ else if eq $result.SRSType "srs_shorthanded" }}
                                            (S/H spinn)
                                        {{ end }}
                                    </td>
                                    <td class="col-besattn">{{ add $result.CrewCount 1 }}</td>
                                    {{ if $.event.Jaktstart }}
                                    <td class="col-time">{{ if $result.DNS }}-{{ else }}{{ $result.StartTime }}{{ end }}</td>
                                    {{ end }}
                                    <td class="col-time">{{ if $result.DNS }}<span class="badge bg-warning">DNS</span>{{ else if $result.DNF }}<span class="badge bg-danger">DNF</span>{{ else }}{{ $result.FinishTime }}{{ end }}</td>
                                    <td class="col-elapsed">{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.ElapsedTime }}{{ end }}</td>
                                    <td class="col-corrected">{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.CorrectedTime }}{{ end }}</td>
                                    <td class="col-diff">{{ if or $result.DNS $result.DNF }}-{{ else if $result.TimeToPrevious }}{{ $result.TimeToPrevious }}{{ else }}-{{ end }}</td>
                                    <td class="col-diff">{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.TimeToWinner }}{{ end }}</td>
                                </tr>
                                {{ end }}
                            {{ else if .totalResults }}
                                {{ range $result := .totalResults }}
                                <tr>
                                    {{ if $.event.Entypsegling }}
                                        <!-- Entypsegling total results: Placering, Seglare, Segelnummer, Besättning, Totala poäng, Heat columns -->
                                        <td class="col-plac">{{ $result.TotalPosition }}</td>
                                        <td class="col-seglare">{{ $result.Sailor.Namn }}</td>
                                        <td class="col-segelnr">{{ $result.EventParticipant.PersonalNumber }}</td>
                                        <td class="col-besattn">{{ add $result.EventParticipant.CrewCount 1 }}</td>
                                        <td class="col-diff"><strong>{{ printf "%.1f" $result.TotalPoints }}</strong></td>
                                        {{ $currentResult := $result }}
                                        {{ range $heat := $.heats }}
                                            {{ $found := false }}
                                            {{ range $currentResult.HeatResults }}
                                                {{ if eq .Heat.ID $heat.ID }}
                                                    {{ $found = true }}
                                                    <td class="col-diff">
                                                        {{ if .DNS }}
                                                            DNS ({{ printf "%.1f" .Points }})
                                                        {{ else if .DNF }}
                                                            DNF ({{ printf "%.1f" .Points }})
                                                        {{ else }}
                                                            {{ .Position }} ({{ printf "%.1f" .Points }}p)
                                                        {{ end }}
                                                    </td>
                                                {{ end }}
                                            {{ end }}
                                            {{ if not $found }}
                                                <td class="col-diff">-</td>
                                            {{ end }}
                                        {{ end }}
                                    {{ else }}
                                        <!-- Regular sailing total results: show boat info, SRS, etc. -->
                                        <td class="col-plac">{{ $result.TotalPosition }}</td>
                                        <td class="col-seglare">{{ $result.Sailor.Namn }}</td>
                                        <td class="col-klubb">{{ $result.Sailor.Klubb }}</td>
                                        <td class="col-battyp">{{ if gt (len $result.Boat.Battyp) 14 }}{{ slice $result.Boat.Battyp 0 14 }}...{{ else }}{{ $result.Boat.Battyp }}{{ end }}</td>
                                        <td class="col-segelnr">
                                            {{ if and $result.Boat.Nationality $result.Boat.Segelnummer }}
                                                {{ $result.Boat.Nationality }}-{{ $result.Boat.Segelnummer }}
                                            {{ else if $result.Boat.Segelnummer }}
                                                {{ $result.Boat.Segelnummer }}
                                            {{ else if $result.Boat.Nationality }}
                                                {{ $result.Boat.Nationality }}
                                            {{ end }}
                                        </td>
                                        <td class="col-matbrev">{{ $result.Boat.MatbrevsNummer }}</td>
                                        <td class="col-srs">
                                            {{ if $result.EventParticipant.UseCustomSRSValue }}
                                                {{ $result.EventParticipant.CustomSRSValue }} (*)
                                            {{ else }}
                                                {{ printf "%.3f" $result.EventParticipant.SelectedSRSValue }}
                                                {{ if eq $result.EventParticipant.SRSType "srs" }}
                                                    (spinn)
                                                {{ else if eq $result.EventParticipant.SRSType "srs_shorthanded" }}
                                                    (S/H spinn)
                                                {{ end }}
                                            {{ end }}
                                        </td>
                                        <td class="col-besattn">{{ add $result.EventParticipant.CrewCount 1 }}</td>
                                        <td class="col-diff"><strong>{{ $result.TotalPoints }}</strong></td>
                                        {{ $currentResult := $result }}
                                        {{ range $heat := $.heats }}
                                            {{ $found := false }}
                                            {{ range $currentResult.HeatResults }}
                                                {{ if eq .Heat.ID $heat.ID }}
                                                    {{ $found = true }}
                                                    <td class="col-diff">
                                                        {{ if .DNS }}
                                                            DNS ({{ printf "%.1f" .Points }})
                                                        {{ else if .DNF }}
                                                            DNF ({{ printf "%.1f" .Points }})
                                                        {{ else }}
                                                            {{ .Position }} ({{ printf "%.1f" .Points }}p)
                                                        {{ end }}
                                                    </td>
                                                {{ end }}
                                            {{ end }}
                                            {{ if not $found }}
                                                <td class="col-diff">-</td>
                                            {{ end }}
                                        {{ end }}
                                    {{ end }}
                                </tr>
                                {{ end }}
                            {{ else if .heatResults }}
                                {{ range $result := .heatResults }}
                                <tr>
                                    {{ if $.event.Entypsegling }}
                                        <!-- Entypsegling individual heat: Plats, Seglare, Klubb, Segelnummer, Besättning, Poäng -->
                                        <td class="col-plac">
                                            {{ if .DNS }}
                                                <span class="badge bg-warning">DNS</span>
                                            {{ else if .DNF }}
                                                <span class="badge bg-danger">DNF</span>
                                            {{ else }}
                                                {{ .Position }}
                                            {{ end }}
                                        </td>
                                        <td class="col-seglare">{{ $result.Sailor.Namn }}</td>
                                        <td class="col-klubb">{{ $result.Sailor.Klubb }}</td>
                                        <td class="col-segelnr">{{ $result.EventParticipant.PersonalNumber }}</td>
                                        <td class="col-besattn">{{ $result.TotalPersons }}</td>
                                        <td class="col-diff">{{ printf "%.1f" $result.Points }}</td>
                                    {{ else }}
                                        <!-- Regular sailing individual heat: show boat info, SRS, times, etc. -->
                                        <td class="col-plac">
                                            {{ if .DNS }}
                                                <span class="badge bg-warning">DNS</span>
                                            {{ else if .DNF }}
                                                <span class="badge bg-danger">DNF</span>
                                            {{ else }}
                                                {{ .Position }}
                                            {{ end }}
                                        </td>
                                        <td class="col-seglare">{{ $result.Sailor.Namn }}</td>
                                        <td class="col-klubb">{{ $result.Sailor.Klubb }}</td>
                                        <td class="col-battyp">{{ if gt (len $result.Boat.Battyp) 14 }}{{ slice $result.Boat.Battyp 0 14 }}...{{ else }}{{ $result.Boat.Battyp }}{{ end }}</td>
                                        <td class="col-segelnr">
                                            {{ if and $result.Boat.Nationality $result.Boat.Segelnummer }}
                                                {{ $result.Boat.Nationality }}-{{ $result.Boat.Segelnummer }}
                                            {{ else if $result.Boat.Segelnummer }}
                                                {{ $result.Boat.Segelnummer }}
                                            {{ else if $result.Boat.Nationality }}
                                                {{ $result.Boat.Nationality }}
                                            {{ end }}
                                        </td>
                                        <td class="col-matbrev">{{ $result.Boat.MatbrevsNummer }}</td>
                                        <td class="col-srs">
                                            {{ if $result.EventParticipant.UseCustomSRSValue }}
                                                {{ $result.EventParticipant.CustomSRSValue }} (*)
                                            {{ else }}
                                                {{ printf "%.2f" $result.EventParticipant.SelectedSRSValue }}
                                                {{ if eq $result.EventParticipant.SRSType "srs" }}
                                                    (spinn)
                                                {{ else if eq $result.EventParticipant.SRSType "srs_shorthanded" }}
                                                    (S/H spinn)
                                                {{ end }}
                                            {{ end }}
                                        </td>
                                        <td class="col-besattn">{{ $result.TotalPersons }}</td>
                                        {{ if $.event.Jaktstart }}
                                        <td class="col-time">{{ if $result.DNS }}-{{ else }}{{ $result.StartTime }}{{ end }}</td>
                                        {{ end }}
                                        <td class="col-time">{{ if $result.DNS }}<span class="badge bg-warning">DNS</span>{{ else if $result.DNF }}<span class="badge bg-danger">DNF</span>{{ else }}{{ $result.FinishTime }}{{ end }}</td>
                                        <td class="col-elapsed">{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.ElapsedTime }}{{ end }}</td>
                                        <td class="col-corrected">{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.CorrectedTime }}{{ end }}</td>
                                        <td class="col-diff">{{ if or $result.DNS $result.DNF }}-{{ else if $result.TimeToPrevious }}{{ $result.TimeToPrevious }}{{ else }}-{{ end }}</td>
                                        <td class="col-diff">{{ if or $result.DNS $result.DNF }}-{{ else }}{{ $result.TimeToWinner }}{{ end }}</td>
                                        {{ if $.isMultiHeat }}
                                        <td class="col-diff">{{ $result.Points }}</td>
                                        {{ end }}
                                    {{ end }}
                                </tr>
                                {{ end }}
                            {{ end }}
                        </tbody>
                    </table>
                </div>

                <!-- Heat selection for multi-heat events -->
                {{ if .isMultiHeat }}
                <div class="mt-3 no-print">
                    <h5>Välj vad som ska skrivas ut:</h5>
                    <div class="btn-group" role="group">
                        <a href="/events/{{ .event.ID }}/results/print?heat=overall" class="btn {{ if .showOverall }}btn-primary{{ else }}btn-outline-primary{{ end }}">
                            Totala resultat
                        </a>
                        {{ range .heats }}
                        <a href="/events/{{ $.event.ID }}/results/print?heat={{ .ID }}" class="btn {{ if eq $.selectedHeat.ID .ID }}btn-primary{{ else }}btn-outline-primary{{ end }}">
                            {{ .Name }}
                        </a>
                        {{ end }}
                    </div>
                </div>
                {{ end }}

                <div class="mt-3 no-print">
                    <button class="btn btn-primary" onclick="window.print()">Skriv ut</button>
                    <a href="/events/{{ .event.ID }}/results" class="btn btn-secondary">Tillbaka till resultat</a>
                </div>
                {{ else }}
                <p>Inga resultat hittades. Kontrollera att måltider har registrerats.</p>
                <div class="mt-3 no-print">
                    <a href="/events/{{ .event.ID }}/finish-times" class="btn btn-primary">Registrera måltider</a>
                </div>
                {{ end }}
            </div>
        </div>
    </div>

    <script>
        // Auto-print when the page loads
        window.onload = function() {
            // Apply darker background to odd rows
            const oddRows = document.querySelectorAll('.table-striped > tbody > tr:nth-of-type(odd)');
            oddRows.forEach(row => {
                row.style.backgroundColor = 'rgba(0, 0, 0, 0.18)';
            });

            // Wait a moment for the page to fully render
            setTimeout(function() {
                // Uncomment the line below to automatically print when the page loads
                // window.print();
            }, 500);
        };


    </script>
</body>
</html>
