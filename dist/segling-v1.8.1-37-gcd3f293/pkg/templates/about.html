{{ template "header.html" . }}

<div class="container mt-4">
    <h1>Om Segling</h1>

    <div class="row">
        <div class="col-md-3">
            <div class="list-group mb-4 sticky-top" style="top: 20px;">
                <a href="#overview" class="list-group-item list-group-item-action">Översikt</a>
                <a href="#version" class="list-group-item list-group-item-action">Version</a>
                <a href="#release-notes" class="list-group-item list-group-item-action">Release Notes</a>
                <a href="#sailors" class="list-group-item list-group-item-action">Seglare</a>
                <a href="#boats" class="list-group-item list-group-item-action">Båtar</a>
                <a href="#events" class="list-group-item list-group-item-action">Tävlingar</a>
                <a href="#participants" class="list-group-item list-group-item-action"><PERSON><PERSON><PERSON></a>
                <a href="#jaktstart" class="list-group-item list-group-item-action">Jaktstart</a>
                <a href="#finish-times" class="list-group-item list-group-item-action">Måltider</a>
                <a href="#results" class="list-group-item list-group-item-action">Resultat</a>
                <a href="#github-pages" class="list-group-item list-group-item-action">GitHub Pages</a>
                <a href="#google-drive" class="list-group-item list-group-item-action">Google Drive</a>
                <a href="#srs-data" class="list-group-item list-group-item-action">SRS Data</a>
                <a href="#settings" class="list-group-item list-group-item-action">Inställningar</a>
            </div>
        </div>

        <div class="col-md-9">
            <div class="card mb-4" id="overview">
                <div class="card-header">
                    <h2>Översikt</h2>
                </div>
                <div class="card-body">
                    <p>Segling är en applikation för att hantera seglingstävlingar. Med denna applikation kan du:</p>
                    <ul>
                        <li>Hantera seglare och båtar</li>
                        <li>Skapa och hantera tävlingar</li>
                        <li>Lägga till deltagare i tävlingar</li>
                        <li>Beräkna jaktstartstider</li>
                        <li>Registrera måltider</li>
                        <li>Visa resultat</li>
                        <li>Publicera resultat till GitHub Pages för att dela dem online</li>
                        <li>Exportera resultat till Google Drive som Google Sheets</li>
                        <li>Exportera resultat till CSV-format</li>
                        <li>Hantera SRS-data</li>
                        <li>Konfigurera applikationsinställningar</li>
                        <li>Skapa automatiska säkerhetskopior av databasen</li>
                    </ul>
                </div>
            </div>

            <div class="card mb-4" id="version">
                <div class="card-header">
                    <h2>Version</h2>
                </div>
                <div class="card-body">
                    <p>Aktuell version: <strong>{{ .version }}</strong></p>
                    <p>Byggdatum: <strong>{{ .buildDate }}</strong></p>
                    <p>Commit: <strong>{{ .commitHash }}</strong></p>
                    <p>Repository: <a href="https://github.com/rogge66/sailapp" target="_blank">https://github.com/rogge66/sailapp</a></p>
                </div>
            </div>

            <div class="card mb-4" id="release-notes">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2>Release Notes</h2>
                    <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#releaseNotesContent" aria-expanded="false" aria-controls="releaseNotesContent">
                        <span class="collapse-toggle-text">Visa</span>
                    </button>
                </div>
                <div class="collapse" id="releaseNotesContent">
                    <div class="card-body">
                        <div class="accordion" id="releaseNotesAccordion">
                            {{ range .releaseNotes }}
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading-{{ .Version }}">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-{{ .Version }}" aria-expanded="false" aria-controls="collapse-{{ .Version }}">
                                        {{ .Version }} ({{ .Date }})
                                    </button>
                                </h2>
                                <div id="collapse-{{ .Version }}" class="accordion-collapse collapse" aria-labelledby="heading-{{ .Version }}" data-bs-parent="#releaseNotesAccordion">
                                    <div class="accordion-body">
                                        <div class="release-notes-content">
                                            {{ .Content }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{ end }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4" id="sailors">
                <div class="card-header">
                    <h2>Seglare</h2>
                </div>
                <div class="card-body">
                    <p>Under menyn "Seglare" kan du hantera alla seglare i systemet.</p>
                    <h4>Funktioner:</h4>
                    <ul>
                        <li><strong>Visa alla seglare:</strong> Se en lista över alla seglare i systemet</li>
                        <li><strong>Sök seglare:</strong> Använd sökfunktionen för att hitta specifika seglare</li>
                        <li><strong>Lägg till seglare:</strong> Registrera nya seglare i systemet</li>
                        <li><strong>Redigera seglare:</strong> Uppdatera information om befintliga seglare</li>
                        <li><strong>Ta bort seglare:</strong> Radera seglare från systemet</li>
                    </ul>
                </div>
            </div>

            <div class="card mb-4" id="boats">
                <div class="card-header">
                    <h2>Båtar</h2>
                </div>
                <div class="card-body">
                    <p>Under menyn "Båtar" kan du hantera alla båtar i systemet.</p>
                    <h4>Funktioner:</h4>
                    <ul>
                        <li><strong>Visa alla båtar:</strong> Se en lista över alla båtar i systemet</li>
                        <li><strong>Sök båtar:</strong> Använd sökfunktionen med live-filtrering för att hitta specifika båtar</li>
                        <li><strong>Lägg till båt:</strong> Registrera nya båtar i systemet</li>
                        <li><strong>Redigera båt:</strong> Uppdatera information om befintliga båtar</li>
                        <li><strong>Ta bort båt:</strong> Radera båtar från systemet</li>
                        <li><strong>Hämta data:</strong> Hämta SRS-data för en båt baserat på mätbrevsnummer</li>
                    </ul>
                    <h4>Mätbrevsnummer:</h4>
                    <p>I mätbrevsnummerfältet kan du söka på mätbrevsnummer, båttyp, båtnamn, ägare eller segelnummer:</p>
                    <ul>
                        <li>Börja skriva för att se förslag från den lokala databasen</li>
                        <li>Välj ett mätbrev från förslagen för att automatiskt fylla i båttyp, båtnamn och SRS-värden</li>
                        <li>Om du anger ett komplett mätbrevsnummer i formatet B#### eller E#### hämtas data automatiskt</li>
                    </ul>
                    <h4>SRS-värden:</h4>
                    <p>Varje båt har fyra olika SRS-värden som visas i båtlistan:</p>
                    <ul>
                        <li><strong>SRS:</strong> Standardvärde för båten</li>
                        <li><strong>SRS utan undanvindsegel:</strong> Värde när båten seglar utan spinnaker/gennaker</li>
                        <li><strong>SRS S/H:</strong> Värde när båten seglas med reducerad besättning (Shorthanded)</li>
                        <li><strong>SRS S/H utan undanvindsegel:</strong> Värde när båten seglas med reducerad besättning och utan spinnaker/gennaker</li>
                    </ul>
                </div>
            </div>

            <div class="card mb-4" id="events">
                <div class="card-header">
                    <h2>Tävlingar</h2>
                </div>
                <div class="card-body">
                    <p>Under menyn "Tävlingar" kan du hantera alla seglingstävlingar i systemet.</p>
                    <h4>Funktioner:</h4>
                    <ul>
                        <li><strong>Visa alla tävlingar:</strong> Se en lista över alla tävlingar i systemet</li>
                        <li><strong>Filtrera tävlingar per år:</strong> Visa tävlingar för ett specifikt år</li>
                        <li><strong>Skapa ny tävling:</strong> Registrera en ny tävling i systemet</li>
                        <li><strong>Redigera tävling:</strong> Uppdatera information om en befintlig tävling</li>
                        <li><strong>Ta bort tävling:</strong> Radera en tävling från systemet</li>
                    </ul>
                    <h4>Tävlingsinformation:</h4>
                    <p>För varje tävling kan du ange följande information:</p>
                    <ul>
                        <li><strong>Namn:</strong> Tävlingens namn (genereras automatiskt baserat på tävlingstyp och datum när en ny tävling skapas)</li>
                        <li><strong>Tävlingstyp:</strong> Typ av tävling (t.ex. Kvällssegling, Regatta)</li>
                        <li><strong>Datum:</strong> Datum för tävlingen</li>
                        <li><strong>Starttid:</strong> Starttid för tävlingen (används för jaktstart)</li>
                        <li><strong>Vind:</strong> Vindstyrka i m/s (används för beräkning av jaktstart)</li>
                        <li><strong>Banlängd:</strong> Banlängd i nautiska mil (används för beräkning av jaktstart)</li>
                        <li><strong>Jaktstart:</strong> Markera om tävlingen använder jaktstart</li>
                        <li><strong>Beskrivning:</strong> Valfri beskrivning av tävlingen</li>
                    </ul>

                    <h4>Låsning av tävlingar:</h4>
                    <p>När en tävling är avslutad och resultaten är klara kan du låsa tävlingen för att bevara resultaten:</p>
                    <ul>
                        <li><strong>Låsa tävling:</strong> Klicka på "Lås tävling" på resultatsidan för att låsa tävlingen och bevara resultaten</li>
                        <li><strong>Låsta tävlingar:</strong> Låsta tävlingar markeras med ett hänglåsikon i tävlingslistan</li>
                        <li><strong>Låsta resultat:</strong> Resultaten för låsta tävlingar bevaras även om båtdata ändras</li>
                        <li><strong>Låsa upp tävling:</strong> Om du behöver göra ändringar i en låst tävling kan du låsa upp den genom att klicka på "Lås upp tävling" på resultatsidan</li>
                    </ul>
                    <p>Observera att när du låser upp en tävling kommer de sparade resultaten att raderas och nya resultat kommer att beräknas baserat på aktuella båtdata och måltider.</p>
                </div>
            </div>

            <div class="card mb-4" id="participants">
                <div class="card-header">
                    <h2>Deltagare</h2>
                </div>
                <div class="card-body">
                    <p>När du redigerar en tävling kan du hantera deltagare för tävlingen.</p>
                    <h4>Funktioner:</h4>
                    <ul>
                        <li><strong>Lägg till deltagare:</strong> Registrera en ny deltagare i tävlingen</li>
                        <li><strong>Redigera deltagare:</strong> Uppdatera information om en befintlig deltagare</li>
                        <li><strong>Ta bort deltagare:</strong> Radera en deltagare från tävlingen</li>
                        <li><strong>Kopiera deltagare:</strong> Kopiera deltagare från en annan tävling</li>
                    </ul>
                    <h4>SRS-typ:</h4>
                    <p>För varje deltagare kan du välja vilken SRS-typ som ska användas:</p>
                    <ul>
                        <li><strong>SRS med undanvind:</strong> Standardvärde för båten</li>
                        <li><strong>SRS utan undanvindsegel:</strong> Värde när båten seglar utan spinnaker/gennaker</li>
                        <li><strong>S/H med undanvind:</strong> Värde när båten seglas med reducerad besättning (Shorthanded)</li>
                        <li><strong>S/H utan undanvindsegel:</strong> Värde när båten seglas med reducerad besättning och utan spinnaker/gennaker</li>
                    </ul>

                    <h4>SRS-värden:</h4>
                    <p>När du lägger till eller redigerar en deltagare visas två SRS-värden:</p>
                    <ul>
                        <li><strong>Båtens SRS-värde:</strong> Det valda SRS-värdet för båten baserat på vald SRS-typ (skrivskyddat)</li>
                        <li><strong>Anpassat SRS-värde:</strong> Ett manuellt angivet SRS-värde som kan användas istället för båtens SRS-värde</li>
                    </ul>
                    <p>För att använda ett anpassat SRS-värde:</p>
                    <ol>
                        <li>Markera kryssrutan "Använd" bredvid det anpassade SRS-värdet</li>
                        <li>Ange det önskade SRS-värdet i fältet</li>
                    </ol>
                    <p>Om kryssrutan "Använd" inte är markerad kommer båtens ordinarie SRS-värde att användas för beräkningar.</p>
                </div>
            </div>

            <div class="card mb-4" id="jaktstart">
                <div class="card-header">
                    <h2>Jaktstart</h2>
                </div>
                <div class="card-body">
                    <p>Om en tävling använder jaktstart kan du visa jaktstartstider för alla deltagare.</p>
                    <h4>Funktioner:</h4>
                    <ul>
                        <li><strong>Visa jaktstartstider:</strong> Se en lista över alla deltagares starttider</li>
                        <li><strong>Skriv ut jaktstartstider:</strong> Skriv ut en utskriftsvänlig version av jaktstartstiderna</li>
                    </ul>
                    <h4>Beräkning av jaktstartstider:</h4>
                    <p>Jaktstartstider beräknas baserat på båtarnas SRS-värden, den planerade banlängden och den förväntade vindhastigheten. Båten med lägst SRS-värde startar först (referensbåten), och övriga båtar startar senare baserat på deras SRS-värde och den teoretiska tidsskillnaden över den planerade banan.</p>
                    <p>Observera att jaktstartstiderna är teoretiska och baseras på ideala förhållanden. Om de faktiska förhållandena (vind, banlängd) avviker från de planerade, kommer detta att påverka resultatet. Därför beräknas alltid de slutliga resultaten baserat på faktiska måltider och SRS-värden.</p>
                </div>
            </div>

            <div class="card mb-4" id="finish-times">
                <div class="card-header">
                    <h2>Måltider</h2>
                </div>
                <div class="card-body">
                    <p>Under "Måltider" kan du registrera måltider för alla deltagare i en tävling.</p>
                    <h4>Funktioner:</h4>
                    <ul>
                        <li><strong>Registrera måltid:</strong> Ange måltid för en deltagare i formatet HH:MM:SS</li>
                        <li><strong>Rensa måltid:</strong> Ta bort en registrerad måltid</li>
                    </ul>
                </div>
            </div>

            <div class="card mb-4" id="results">
                <div class="card-header">
                    <h2>Resultat</h2>
                </div>
                <div class="card-body">
                    <p>Under "Resultat" kan du se resultatlistan för en tävling.</p>
                    <h4>Funktioner:</h4>
                    <ul>
                        <li><strong>Visa resultat:</strong> Se en lista över alla deltagare sorterade efter korrigerad tid</li>
                        <li><strong>Skriv ut resultat:</strong> Skriv ut en utskriftsvänlig version av resultatlistan</li>
                        <li><strong>Publicera resultat:</strong> Publicera resultaten till GitHub Pages för att dela dem online</li>
                        <li><strong>Exportera till Google Drive:</strong> Exportera resultaten till Google Drive som Google Sheets för enkel delning och samarbete</li>
                        <li><strong>Exportera resultat:</strong> Exportera resultaten till CSV-format för användning i andra program</li>
                    </ul>

                    <h4>Beräkning av resultat:</h4>
                    <p>Resultaten beräknas baserat på deltagarnas måltider och SRS-värden.</p>
                    <h4>Jaktstart och resultat:</h4>
                    <p>För jaktstart är målgångsordningen <strong>inte nödvändigtvis</strong> samma som den slutliga resultatlistan. Jaktstartstiderna beräknas baserat på planerad vind och banlängd, men om de faktiska förhållandena avviker från de planerade (t.ex. om vinden ändras under tävlingen eller om banan blir kortare/längre än planerat) påverkas resultatet. Systemet beräknar alltid korrigerade tider baserat på faktiska måltider och SRS-värden för att säkerställa rättvisa resultat.</p>
                </div>
            </div>

            <div class="card mb-4" id="github-pages">
                <div class="card-header">
                    <h2>GitHub Pages</h2>
                </div>
                <div class="card-body">
                    <p>Segling har stöd för att publicera tävlingsresultat till GitHub Pages, vilket gör det möjligt att dela resultaten online med deltagare, åskådare och andra intresserade.</p>

                    <h4>Vad är GitHub Pages?</h4>
                    <p>GitHub Pages är en gratis webbtjänst från GitHub som låter dig publicera webbsidor direkt från ett GitHub-repository. I Segling används denna tjänst för att publicera tävlingsresultat i ett format som är tillgängligt via en webbläsare.</p>

                    <h4>Förutsättningar för publicering:</h4>
                    <ul>
                        <li><strong>Låst tävling:</strong> Tävlingen måste vara låst innan den kan publiceras</li>
                        <li><strong>GitHub-konfiguration:</strong> GitHub-inställningarna måste vara korrekt konfigurerade i applikationens inställningar</li>
                    </ul>

                    <h4>Publicera resultat till GitHub Pages:</h4>
                    <ol>
                        <li>Gå till resultatsidan för en låst tävling</li>
                        <li>Klicka på knappen "Publicera till GitHub Pages"</li>
                        <li>Vänta medan systemet:
                            <ul>
                                <li>Genererar HTML-sidor för tävlingsresultaten</li>
                                <li>Laddar upp filerna till GitHub</li>
                                <li>Väntar på att GitHub Pages ska göra sidorna tillgängliga</li>
                            </ul>
                        </li>
                        <li>När publiceringen är klar aktiveras knappen "Visa publicerad sida"</li>
                        <li>Klicka på "Visa publicerad sida" för att öppna den publicerade sidan i en ny flik</li>
                    </ol>

                    <h4>Publicerade sidor:</h4>
                    <p>De publicerade sidorna innehåller:</p>
                    <ul>
                        <li><strong>Huvudindexsida:</strong> En lista över alla tillgängliga år med publicerade tävlingar</li>
                        <li><strong>Årsindexsidor:</strong> För varje år, en lista över tillgängliga tävlingstyper</li>
                        <li><strong>Tävlingstypindexsidor:</strong> För varje tävlingstyp, en lista över alla publicerade tävlingar av den typen, sorterade efter datum</li>
                        <li><strong>Resultatsidor:</strong> Detaljerade resultatsidor för varje tävling med:
                            <ul>
                                <li>Tävlingsnamn och datum</li>
                                <li>Vind, banlängd och starttid</li>
                                <li>Komplett resultattabell med alla deltagare</li>
                                <li>Samma kolumner som i den utskriftsvänliga versionen</li>
                            </ul>
                        </li>
                    </ul>

                    <h4>Katalogstruktur:</h4>
                    <p>Resultaten organiseras i en hierarkisk struktur på GitHub Pages:</p>
                    <pre>
/                           # Huvudindexsida med länkar till alla år
├── 2025/                  # År
│   ├── index.html         # Årsindexsida med länkar till tävlingstyper
│   ├── Kvällssegling/     # Tävlingstyp
│   │   ├── index.html     # Tävlingstypindexsida med länkar till tävlingar
│   │   ├── 2025-05-15-kvallssegling.html  # Tävlingsresultat
│   │   └── ...
│   ├── Regatta/           # Annan tävlingstyp
│   │   ├── index.html
│   │   ├── 2025-05-20-regatta.html
│   │   └── ...
│   └── ...
├── 2024/                  # Annat år
│   └── ...
└── ...
</pre>

                    <h4>Dela publicerade resultat:</h4>
                    <p>När resultaten är publicerade kan du dela webbadressen med andra. Adressen visas efter publiceringen och har formatet:</p>
                    <pre>https://[användarnamn].github.io/sailapp/</pre>
                    <p>Denna adress leder till indexsidan med alla publicerade tävlingar. För att dela en specifik tävling, navigera till tävlingen och kopiera adressen från webbläsarens adressfält.</p>

                    <p><a href="/settings#github-pages" class="btn btn-info btn-sm">
                        <i class="fas fa-cog"></i> Konfigurera GitHub Pages
                    </a></p>
                </div>
            </div>

            <div class="card mb-4" id="google-drive">
                <div class="card-header">
                    <h2>Google Drive Export</h2>
                </div>
                <div class="card-body">
                    <p>Segling har stöd för att exportera tävlingsresultat direkt till Google Drive som Google Sheets, vilket gör det enkelt att dela och samarbeta kring resultaten.</p>

                    <h4>Vad är Google Drive Export?</h4>
                    <p>Google Drive Export låter dig skapa Google Sheets-dokument med tävlingsresultat direkt från Segling-applikationen. Dokumenten sparas i din Google Drive och kan enkelt delas med andra.</p>

                    <h4>Förutsättningar:</h4>
                    <ul>
                        <li><strong>Google Cloud-projekt:</strong> Du behöver ett Google Cloud-projekt med aktiverade APIs</li>
                        <li><strong>Autentisering:</strong> Du måste autentisera applikationen med ditt Google-konto</li>
                        <li><strong>Aktiverad integration:</strong> Google Drive-integration måste vara aktiverad i inställningarna</li>
                    </ul>

                    <h4>Snabbstart:</h4>
                    <ol>
                        <li><strong>Skapa Google Cloud-projekt</strong> på <a href="https://console.cloud.google.com/" target="_blank">console.cloud.google.com</a></li>
                        <li><strong>Aktivera APIs:</strong> Google Drive API och Google Sheets API</li>
                        <li><strong>Skapa OAuth-autentiseringsuppgifter</strong> (Desktop application)</li>
                        <li><strong>Ladda upp client_secrets.json</strong> i Segling-inställningarna</li>
                        <li><strong>Autentisera</strong> med ditt Google-konto</li>
                        <li><strong>Exportera resultat</strong> från valfri resultatsida</li>
                    </ol>

                    <h4>Funktioner:</h4>
                    <ul>
                        <li><strong>Ett-klicks export:</strong> Exportera resultat direkt från resultatsidan</li>
                        <li><strong>Professionell formatering:</strong> Automatisk formatering av tävlingsdata och resultat</li>
                        <li><strong>Mapporganisation:</strong> Organisera exporterade filer i specifika Google Drive-mappar</li>
                        <li><strong>Anpassade filnamn:</strong> Konfigurera filnamn med platshållare som &#123;&#123;event_name&#125;&#125; och &#123;&#123;date&#125;&#125;</li>
                        <li><strong>Automatisk öppning:</strong> Exporterade dokument öppnas automatiskt i en ny flik</li>
                    </ul>

                    <h4>Exporterade dokument innehåller:</h4>
                    <ul>
                        <li><strong>Tävlingsinformation:</strong> Namn, datum, starttid, vind och banlängd</li>
                        <li><strong>Komplett resultattabell:</strong> Alla deltagare med placering, seglare, klubb, båtinformation, tider och tidsskillnader</li>
                        <li><strong>Professionell layout:</strong> Tydliga rubriker och formatering för enkel läsning</li>
                    </ul>

                    <p><a href="/settings#google-drive" class="btn btn-success btn-sm">
                        <i class="fas fa-cog"></i> Konfigurera Google Drive Export
                    </a></p>

                </div>
            </div>

            <div class="card mb-4" id="srs-data">
                <div class="card-header">
                    <h2>SRS Data</h2>
                </div>
                <div class="card-body">
                    <p>Under menyn "SRS Data" kan du hantera SRS-data från Svensk Segling.</p>
                    <h4>Funktioner:</h4>
                    <ul>
                        <li><strong>Uppdatera SRS-data:</strong> Hämta senaste SRS-data från https://matbrev.svensksegling.se/</li>
                        <li><strong>Visa SRS Tabell:</strong> Se en lista över alla båttyper i SRS-tabellen</li>
                        <li><strong>Visa Mätbrev:</strong> Se en lista över alla båtar med mätbrev</li>
                        <li><strong>Sök SRS-data:</strong> Använd sökfunktionen med live-filtrering för att hitta specifika båttyper eller mätbrev</li>
                        <li><strong>Validering av mätbrevsnummer:</strong> Automatisk validering av format (B#### eller E####)</li>
                        <li><strong>Offline-användning:</strong> All SRS-data lagras lokalt för snabb åtkomst utan internetanslutning</li>
                    </ul>
                    <h4>Offline-åtkomst:</h4>
                    <p>När du har uppdaterat SRS-data lagras den i den lokala databasen, vilket ger följande fördelar:</p>
                    <ul>
                        <li><strong>Snabb sökning:</strong> Omedelbar åtkomst till SRS-data utan att behöva vänta på nätverksanrop</li>
                        <li><strong>Offline-användning:</strong> Tillgång till SRS-data även utan internetanslutning</li>
                        <li><strong>Automatisk ifyllning:</strong> När du lägger till eller redigerar en båt med mätbrevsnummer fylls båttyp och båtnamn i automatiskt</li>
                    </ul>
                    <h4>Mätbrev vs SRS Tabell:</h4>
                    <ul>
                        <li><strong>Mätbrev:</strong> Innehåller data för specifika båtar med mätbrevsnummer, inklusive båtnamn och ägare</li>
                        <li><strong>SRS Tabell:</strong> Innehåller standardvärden för olika båttyper utan koppling till specifika båtar</li>
                    </ul>
                </div>
            </div>

            <div class="card mb-4" id="settings">
                <div class="card-header">
                    <h2>Inställningar</h2>
                </div>
                <div class="card-body">
                    <p>Under menyn "Inställningar" kan du konfigurera applikationens beteende och anpassa den efter dina behov.</p>

                    <h4>Databas:</h4>
                    <ul>
                        <li><strong>Automatisk säkerhetskopiering:</strong> När denna inställning är aktiverad skapas automatiskt en säkerhetskopia av databasen varje gång en ny tävling skapas.</li>
                    </ul>

                    <h4>Säkerhetskopior:</h4>
                    <p>Säkerhetskopior sparas i mappen "backups" i samma katalog som databasen. Varje säkerhetskopia namnges med datum och tid för när den skapades, t.ex. "segling_20230515_123045.db".</p>
                    <p>Säkerhetskopior är användbara om du behöver återställa databasen till ett tidigare tillstånd, t.ex. om data har gått förlorad eller om du har gjort ändringar som du vill ångra.</p>

                    <h4>Seglare:</h4>
                    <ul>
                        <li><strong>Standard klubb:</strong> Ange standardvärdet för klubb som används när nya seglare skapas. Standardvärdet är "LSS".</li>
                    </ul>

                    <h4>Visning:</h4>
                    <ul>
                        <li><strong>Tidsformat:</strong> Välj mellan 24-timmarsformat (t.ex. 14:30) och 12-timmarsformat (t.ex. 2:30 PM) för visning av tider i applikationen.</li>
                    </ul>

                    <h4>Tävlingstyper:</h4>
                    <ul>
                        <li><strong>Tillgängliga tävlingstyper:</strong> Hantera listan över tillgängliga tävlingstyper som kan väljas när en ny tävling skapas. Standardvärdena är "Kvällssegling" och "Regatta".</li>
                        <li><strong>Standard tävlingstyp:</strong> Välj vilken tävlingstyp som ska vara förvald när en ny tävling skapas.</li>
                    </ul>
                    <p>Tävlingstyper används för att kategorisera tävlingar och organisera publicerade resultat på GitHub Pages. När en ny tävling skapas används den valda tävlingstypen tillsammans med datumet för att automatiskt generera ett förslag på tävlingsnamn.</p>

                    <h4>GitHub Pages-konfiguration:</h4>
                    <p>För att kunna publicera tävlingsresultat till GitHub Pages behöver du konfigurera följande inställningar:</p>
                    <ul>
                        <li><strong>GitHub-användarnamn:</strong> Ditt användarnamn på GitHub</li>
                        <li><strong>GitHub-token:</strong> En personlig åtkomsttoken från GitHub med behörighet att skapa och uppdatera repositories</li>
                        <li><strong>Repository-namn:</strong> Namnet på det repository där resultaten ska publiceras (vanligtvis "sailapp")</li>
                        <li><strong>Branch:</strong> Grenen i repositoryt där sidorna ska publiceras (vanligtvis "gh-pages")</li>
                    </ul>
                    <p>När dessa inställningar är konfigurerade kan du publicera tävlingsresultat direkt från applikationen till GitHub Pages, vilket gör resultaten tillgängliga online för alla deltagare och intresserade.</p>

                    <p><a href="/settings#github-pages" class="btn btn-primary btn-sm">
                        <i class="fas fa-cog"></i> Konfigurera GitHub Pages-inställningar
                    </a></p>

                    <h4>Google Drive-konfiguration:</h4>
                    <p>För att kunna exportera tävlingsresultat till Google Drive behöver du konfigurera följande inställningar:</p>
                    <ul>
                        <li><strong>Client Secrets-fil:</strong> Ladda upp client_secrets.json-filen från ditt Google Cloud-projekt</li>
                        <li><strong>Aktivera Google Drive-integration:</strong> Markera kryssrutan för att aktivera funktionen</li>
                        <li><strong>Google Drive Mapp-ID:</strong> (Valfritt) Ange ID för en specifik mapp där exporterade filer ska sparas</li>
                        <li><strong>Filnamnsmall:</strong> (Valfritt) Anpassa hur exporterade filer ska namnges</li>
                    </ul>

                    <h5>Så här hittar du Google Drive Mapp-ID:</h5>
                    <ol>
                        <li><strong>Öppna Google Drive</strong> i din webbläsare (drive.google.com)</li>
                        <li><strong>Skapa en ny mapp</strong> eller öppna en befintlig mapp där du vill spara resultaten</li>
                        <li><strong>Dubbelklicka på mappen</strong> för att öppna den</li>
                        <li><strong>Kopiera URL:en</strong> från webbläsarens adressfält</li>
                        <li><strong>Hitta mapp-ID:t</strong> - det är den långa textsträngen efter "/folders/" i URL:en</li>
                    </ol>

                    <div class="bg-light dark:bg-dark border rounded p-3 my-3">
                        <h6>Exempel:</h6>
                        <p><strong>URL:</strong><br>
                        https://drive.google.com/drive/folders/<span class="bg-warning text-dark px-1 fw-bold">1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74mMngDnt</span></p>
                        <p><strong>Mapp-ID att kopiera:</strong><br>
                        <code class="bg-secondary bg-opacity-25 px-2 py-1 rounded">1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74mMngDnt</code></p>
                    </div>

                    <h6>Tips för mapporganisation:</h6>
                    <ul>
                        <li><strong>Skapa en dedikerad mapp:</strong> Skapa en mapp som heter "Seglingsresultat" eller "Tävlingsresultat"</li>
                        <li><strong>Dela mappen:</strong> Du kan dela mappen med andra för enkel åtkomst till alla exporterade resultat</li>
                        <li><strong>Lämna tomt:</strong> Om du lämnar mapp-ID tomt sparas filerna i din huvudmapp (My Drive)</li>
                        <li><strong>Undermappar:</strong> Du kan skapa undermappar för olika år eller tävlingstyper</li>
                    </ul>

                    <p>Efter konfiguration måste du autentisera applikationen med ditt Google-konto genom att klicka på "Kontrollera autentisering" och följa OAuth-flödet. När autentiseringen är klar kan du exportera tävlingsresultat direkt till Google Drive som Google Sheets-dokument.</p>

                    <p><a href="/settings#google-drive" class="btn btn-primary btn-sm">
                        <i class="fas fa-cog"></i> Konfigurera Google Drive-inställningar
                    </a></p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Toggle button text between "Visa" and "Dölj" when the collapse state changes
    document.addEventListener('DOMContentLoaded', function() {
        const releaseNotesContent = document.getElementById('releaseNotesContent');
        const toggleButton = document.querySelector('[data-bs-target="#releaseNotesContent"]');
        const toggleText = toggleButton.querySelector('.collapse-toggle-text');

        releaseNotesContent.addEventListener('hidden.bs.collapse', function () {
            toggleText.textContent = 'Visa';
        });

        releaseNotesContent.addEventListener('shown.bs.collapse', function () {
            toggleText.textContent = 'Dölj';
        });
    });
</script>

{{ template "footer.html" . }}
