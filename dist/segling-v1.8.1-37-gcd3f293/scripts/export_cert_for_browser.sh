#!/bin/bash
# Script to export the certificate in a browser-friendly format

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print header
echo -e "${GREEN}=== Exporting Certificate for Browser Import ===${NC}"

# Check if OpenSSL is installed
if ! command -v openssl &> /dev/null; then
    echo -e "${RED}Error: OpenSSL is not installed. Please install OpenSSL to export certificates.${NC}"
    exit 1
fi

# Define variables
CERT_DIR="certs"
CERT_FILE="${CERT_DIR}/cert.pem"
EXPORT_DIR="${CERT_DIR}/export"
DER_FILE="${EXPORT_DIR}/cert.der"
CRT_FILE="${EXPORT_DIR}/cert.crt"
P12_FILE="${EXPORT_DIR}/cert.p12"

# Check if certificate exists
if [ ! -f "${CERT_FILE}" ]; then
    echo -e "${RED}Error: Certificate file ${CERT_FILE} not found.${NC}"
    echo -e "${YELLOW}Please run ./scripts/generate_certs.sh first to generate certificates.${NC}"
    exit 1
fi

# Create export directory if it doesn't exist
echo -e "${YELLOW}Creating export directory...${NC}"
mkdir -p "${EXPORT_DIR}"

# Export certificate in DER format (for Chrome/Edge)
echo -e "${YELLOW}Exporting certificate in DER format (for Chrome/Edge)...${NC}"
openssl x509 -outform der -in "${CERT_FILE}" -out "${DER_FILE}"

# Copy certificate as CRT format (for Firefox)
echo -e "${YELLOW}Copying certificate as CRT format (for Firefox)...${NC}"
cp "${CERT_FILE}" "${CRT_FILE}"

# Create PKCS#12 format (for all browsers, especially Chrome)
echo -e "${YELLOW}Creating PKCS#12 format (for all browsers)...${NC}"
# Use a blank password for easier import
openssl pkcs12 -export -out "${P12_FILE}" -inkey "${CERT_DIR}/key.pem" -in "${CERT_FILE}" -passout pass:

# Check if export was successful
if [ -f "${DER_FILE}" ] && [ -f "${CRT_FILE}" ] && [ -f "${P12_FILE}" ]; then
    echo -e "${GREEN}Certificate export successful!${NC}"
    echo -e "${GREEN}DER format (Chrome/Edge): ${DER_FILE}${NC}"
    echo -e "${GREEN}CRT format (Firefox): ${CRT_FILE}${NC}"
    echo -e "${GREEN}PKCS#12 format (All browsers): ${P12_FILE}${NC}"

    echo -e "${YELLOW}=== Instructions for importing the certificate ===${NC}"

    echo -e "${YELLOW}For Chrome/Edge (Method 1 - Recommended):${NC}"
    echo -e "1. Open Chrome/Edge and navigate to chrome://settings/certificates (Edge: edge://settings/certificates)"
    echo -e "2. Go to the 'Authorities' tab"
    echo -e "3. Click 'Import' and select ${CRT_FILE}"
    echo -e "4. Check 'Trust this certificate for identifying websites' and click OK"
    echo -e "5. Restart the browser"

    echo -e "${YELLOW}For Chrome/Edge (Method 2 - Alternative):${NC}"
    echo -e "1. Double-click the ${P12_FILE} file"
    echo -e "2. Select 'Current User' for the store location"
    echo -e "3. Click Next, enter a blank password (just press Enter)"
    echo -e "4. Select 'Place all certificates in the following store' and choose 'Trusted Root Certification Authorities'"
    echo -e "5. Complete the wizard and restart the browser"

    echo -e "${YELLOW}For Firefox:${NC}"
    echo -e "1. Open Firefox and navigate to about:preferences#privacy"
    echo -e "2. Scroll down to 'Certificates' and click 'View Certificates'"
    echo -e "3. Go to the 'Authorities' tab"
    echo -e "4. Click 'Import' and select ${CRT_FILE}"
    echo -e "5. Check 'Trust this CA to identify websites' and click OK"
    echo -e "6. Restart Firefox"

    echo -e "${YELLOW}For Safari:${NC}"
    echo -e "1. Double-click the ${CRT_FILE} file to open it in Keychain Access"
    echo -e "2. It will be added to the 'login' keychain by default"
    echo -e "3. Find the certificate in the 'Certificates' category"
    echo -e "4. Double-click it, expand the 'Trust' section"
    echo -e "5. Change 'When using this certificate' to 'Always Trust'"
    echo -e "6. Close the window and enter your password when prompted"
    echo -e "7. Restart Safari"

    echo -e "${YELLOW}Troubleshooting:${NC}"
    echo -e "1. If you still see warnings, try using a different browser"
    echo -e "2. Make sure you're accessing the site using exactly 'localhost' (not 127.0.0.1)"
    echo -e "3. Try clearing your browser cache and restarting the browser"
    echo -e "4. On Windows, you may need to import the certificate to the system store using the MMC console"
    echo -e "5. On macOS, make sure the certificate is in the System keychain, not just login"

    echo -e "${RED}NOTE: Adding self-signed certificates to your browser's trust store reduces security.${NC}"
    echo -e "${RED}      Only do this for development purposes and on trusted development machines.${NC}"
else
    echo -e "${RED}Certificate export failed.${NC}"
    exit 1
fi
