# Mutual TLS Authentication for Segling

This document explains how to set up and use mutual TLS authentication (mTLS) for the Segling application. Mutual TLS ensures that both the server and client authenticate each other using certificates, providing an additional layer of security.

## What is Mutual TLS?

In standard TLS, only the server presents a certificate to prove its identity to the client. In mutual TLS, both the server and client present certificates to authenticate each other. This ensures that:

1. The server is who it claims to be (server authentication)
2. The client is authorized to connect (client authentication)

This is particularly useful for restricting access to your Segling application to only known clients.

## Quick Start

### Step 1: Generate Server Certificates

If you haven't already, generate server certificates:

```bash
./scripts/generate_certs.sh
```

### Step 2: Generate Client Certificates

Generate client certificates and a client Certificate Authority (CA):

```bash
./scripts/generate_client_certs.sh
```

This will create:
- A client CA certificate (`certs/client/ca.crt`)
- A client certificate (`certs/client/client.crt`)
- A client key (`certs/client/client.key`)
- A PKCS#12 file for browser import (`certs/client/client.p12`)

### Step 3: Start the Server with Client Certificate Verification

Start the server with client certificate verification:

```bash
# To require client certificates (strict mode):
./segling -tls -cert certs/cert.pem -key certs/key.pem -client-ca certs/client/ca.crt -require-client-cert

# To accept but not require client certificates (optional mode):
./segling -tls -cert certs/cert.pem -key certs/key.pem -client-ca certs/client/ca.crt
```

### Step 4: Import Client Certificate to Browser

1. Double-click the `certs/client/client.p12` file
2. Enter the password: `segling`
3. Follow your browser's instructions to complete the import

## Detailed Setup Instructions

### Server Configuration

The server can be configured in two modes:

1. **Require Client Certificates**: The server will reject any connection that doesn't present a valid client certificate.
   ```
   -client-ca certs/client/ca.crt -require-client-cert
   ```

2. **Optional Client Certificates**: The server will accept connections without client certificates, but will verify them if presented.
   ```
   -client-ca certs/client/ca.crt
   ```

### Browser Configuration

Different browsers handle client certificates differently:

#### Chrome/Edge
1. Double-click the `certs/client/client.p12` file
2. Select "Current User" for the store location
3. Enter the password: `segling`
4. Select "Automatically select the certificate store"
5. Complete the wizard and restart the browser

#### Firefox
1. Go to Settings > Privacy & Security
2. Scroll down to "Certificates" and click "View Certificates"
3. Go to the "Your Certificates" tab
4. Click "Import" and select `certs/client/client.p12`
5. Enter the password: `segling`
6. Click OK and restart Firefox

#### Safari
1. Double-click the `certs/client/client.p12` file to open it in Keychain Access
2. Enter the password: `segling`
3. The certificate will be added to your login keychain
4. Restart Safari

### Command-Line Client Configuration

For command-line clients like curl, you can use:

```bash
curl --cert certs/client/client.crt --key certs/client/client.key https://localhost:8080
```

## Troubleshooting

### Certificate Not Recognized

If the server doesn't recognize your client certificate:

1. Ensure the client certificate was signed by the CA you provided to the server
2. Check that the CA certificate is correctly formatted (PEM format)
3. Verify that the client certificate hasn't expired

### Browser Doesn't Prompt for Certificate

If your browser doesn't prompt you to select a certificate:

1. Ensure the certificate is correctly imported
2. Try clearing your browser cache and cookies
3. Restart your browser
4. Try a different browser

### Connection Refused

If the connection is refused:

1. Ensure the server is running with the correct flags
2. Check the server logs for any certificate validation errors
3. Verify that the client certificate has the correct extensions for client authentication

## Security Considerations

1. **Protect Private Keys**: Keep all private keys secure. Anyone with access to a private key can impersonate that entity.
2. **Certificate Revocation**: If a client certificate is compromised, you'll need to generate a new CA and client certificates.
3. **Certificate Expiration**: Certificates have expiration dates. Monitor these and renew before they expire.
4. **Development vs. Production**: For production use, consider using a proper PKI (Public Key Infrastructure) rather than self-signed certificates.
