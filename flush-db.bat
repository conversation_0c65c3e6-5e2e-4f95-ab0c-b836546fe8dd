@echo off
setlocal enabledelayedexpansion

REM SQLite WAL Checkpoint Script for Windows
REM This script flushes the WAL (Write-Ahead Log) to the main database file

REM Default values
set "DB_PATH=segling.db"
set "VERBOSE=false"
set "SCRIPT_DIR=%~dp0"
set "CHECKPOINT_BINARY=%SCRIPT_DIR%checkpoint.exe"

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="-db" (
    set "DB_PATH=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--database" (
    set "DB_PATH=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="-v" (
    set "VERBOSE=true"
    shift
    goto :parse_args
)
if "%~1"=="--verbose" (
    set "VERBOSE=true"
    shift
    goto :parse_args
)
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help
echo Unknown option: %~1
echo Use -h or --help for usage information
exit /b 1

:show_help
echo SQLite WAL Checkpoint Script
echo.
echo Usage: %~nx0 [options]
echo.
echo Options:
echo   -db, --database PATH    Database file path (default: segling.db)
echo   -v, --verbose          Verbose output
echo   -h, --help             Show this help
echo.
echo Examples:
echo   %~nx0                     # Checkpoint segling.db
echo   %~nx0 -db mydb.db         # Checkpoint specific database
echo   %~nx0 -v                  # Verbose output
echo.
echo This script flushes all changes from the SQLite WAL file
echo to the main database file, making changes visible to Git.
exit /b 0

:args_done

REM Check if checkpoint binary exists, if not try to build it
if not exist "%CHECKPOINT_BINARY%" (
    echo Checkpoint utility not found. Building...
    
    REM Check if Go is installed
    where go >nul 2>nul
    if errorlevel 1 (
        echo Error: Go is not installed. Please install Go or use the manual method:
        echo   sqlite3 %DB_PATH% "PRAGMA wal_checkpoint(FULL);"
        exit /b 1
    )
    
    REM Build the checkpoint utility
    echo Building checkpoint utility...
    cd /d "%SCRIPT_DIR%"
    go build -o checkpoint.exe ./cmd/checkpoint
    
    if not exist "%CHECKPOINT_BINARY%" (
        echo Error: Failed to build checkpoint utility
        exit /b 1
    )
    
    echo ✓ Checkpoint utility built successfully
)

REM Prepare arguments for the checkpoint utility
set "ARGS=-db %DB_PATH%"
if "%VERBOSE%"=="true" (
    set "ARGS=%ARGS% -v"
)

REM Run the checkpoint utility
echo Flushing database: %DB_PATH%
"%CHECKPOINT_BINARY%" %ARGS%

echo.
echo Database flush completed!
echo You can now run 'git status' to see database changes.

endlocal
