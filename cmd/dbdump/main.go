package main

import (
	"database/sql"
	"flag"
	"fmt"
	"io"
	"log"
	"os"
	"strings"
	"text/tabwriter"
	"time"

	_ "modernc.org/sqlite"
)

// TableInfo represents information about a table
type TableInfo struct {
	Name    string
	Columns []string
}

func main() {
	// Parse command-line flags
	dbPath := flag.String("db", "segling.db", "Path to the SQLite database file")
	tableName := flag.String("table", "", "Name of the table to dump (empty for all tables)")
	outputFile := flag.String("output", "", "Path to output file (empty for stdout)")
	flag.Parse()

	// Open the database
	db, err := sql.Open("sqlite", *dbPath)
	if err != nil {
		log.Fatalf("Error opening database: %v", err)
	}
	defer db.Close()

	// Get tables
	tables, err := getTables(db)
	if err != nil {
		log.Fatalf("Error getting tables: %v", err)
	}

	// Filter tables if a specific table was requested
	if *tableName != "" {
		var filteredTables []TableInfo
		for _, table := range tables {
			if table.Name == *tableName {
				filteredTables = append(filteredTables, table)
				break
			}
		}
		if len(filteredTables) == 0 {
			log.Fatalf("Table '%s' not found", *tableName)
		}
		tables = filteredTables
	}

	// Set up output writer
	var out io.Writer = os.Stdout
	if *outputFile != "" {
		file, err := os.Create(*outputFile)
		if err != nil {
			log.Fatalf("Error creating output file: %v", err)
		}
		defer file.Close()
		out = file
	}

	// For each table, print its contents
	for _, table := range tables {
		fmt.Fprintf(out, "\n=== Table: %s ===\n\n", table.Name)

		// Create a tabwriter for aligned output
		w := tabwriter.NewWriter(out, 0, 0, 2, ' ', 0)

		// Print column headers
		fmt.Fprintln(w, strings.Join(table.Columns, "\t"))
		fmt.Fprintln(w, strings.Repeat("-\t", len(table.Columns)))

		// Get and print rows
		rows, err := db.Query(fmt.Sprintf("SELECT * FROM %s", table.Name))
		if err != nil {
			log.Printf("Error querying table %s: %v", table.Name, err)
			continue
		}

		for rows.Next() {
			// Create a slice of interface{} to hold the values
			values := make([]interface{}, len(table.Columns))
			valuePtrs := make([]interface{}, len(table.Columns))

			// Create pointers to each element in values
			for i := range values {
				valuePtrs[i] = &values[i]
			}

			// Scan the row into the slice of pointers
			if err := rows.Scan(valuePtrs...); err != nil {
				log.Printf("Error scanning row: %v", err)
				continue
			}

			// Convert values to strings for display
			valueStrings := make([]string, len(table.Columns))
			for i, v := range values {
				// Handle different types
				switch val := v.(type) {
				case nil:
					valueStrings[i] = "NULL"
				case []byte:
					valueStrings[i] = string(val)
				case time.Time:
					valueStrings[i] = val.Format("2006-01-02 15:04:05")
				default:
					valueStrings[i] = fmt.Sprintf("%v", val)
				}
			}

			// Print the row
			fmt.Fprintln(w, strings.Join(valueStrings, "\t"))
		}
		rows.Close()

		// Flush the tabwriter
		w.Flush()
		fmt.Fprintln(out)
	}
}

// getTables returns information about all tables in the database
func getTables(db *sql.DB) ([]TableInfo, error) {
	// Query for table names
	rows, err := db.Query("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tables []TableInfo

	// For each table, get its columns
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			return nil, err
		}

		// Get column information
		colRows, err := db.Query(fmt.Sprintf("PRAGMA table_info(%s)", tableName))
		if err != nil {
			return nil, err
		}

		var columns []string
		for colRows.Next() {
			var cid, notnull, pk int
			var name, dataType string
			var dfltValue interface{}

			if err := colRows.Scan(&cid, &name, &dataType, &notnull, &dfltValue, &pk); err != nil {
				colRows.Close()
				return nil, err
			}

			columns = append(columns, name)
		}
		colRows.Close()

		tables = append(tables, TableInfo{
			Name:    tableName,
			Columns: columns,
		})
	}

	return tables, nil
}
