# Database Dump Utility

This utility dumps the contents of the SQLite database in a formatted text format. It's useful for debugging and inspecting the database.

## Usage

You can run the utility directly:

```bash
go run cmd/dbdump/main.go [options]
```

Or use the provided shell script:

```bash
./dbdump.sh [options]
```

### Options

- `-db=PATH`: Path to the SQLite database file (default: "segling.db")
- `-table=NAME`: Name of the table to dump (empty for all tables)
- `-output=FILE`: Path to output file (empty for stdout)

When using the shell script, the options are:

- `-t, --table TABLE`: Dump only the specified table
- `-o, --output FILE`: Write output to FILE instead of stdout
- `-h, --help`: Display help message

## Examples

Dump all tables to the console:

```bash
./dbdump.sh
```

Dump only the boats table:

```bash
./dbdump.sh -t boats
```

Save the output to a file:

```bash
./dbdump.sh -o database_dump.txt
```

Dump a specific table to a file:

```bash
./dbdump.sh -t boats -o boats_dump.txt
```

## Output Format

The output is formatted as a table with aligned columns. Each table is preceded by its name, and column headers are included.

Example:

```
=== Table: boats ===

id  namn       battyp       matbrevs_nummer  srs    srs_utan_undanvindsegel  srs_shorthanded  srs_shorthanded_utan_undanvindsegel  created_at           updated_at
-   -          -            -                -      -                        -                -                                    -                    -  
3   bellatrix  X-332        e4256            1.004  0.977                    0.983            0.955                                2025-05-10 10:55:41  2025-05-10 11:25:49
4   aa         Arcona 340                    1.053  1                        0                0                                    2025-05-10 11:05:48  2025-05-10 11:25:16
```
