package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "modernc.org/sqlite"
)

func main() {
	// Open the database
	db, err := sql.Open("sqlite", "segling.db")
	if err != nil {
		log.Fatalf("Failed to open database: %v", err)
	}
	defer db.Close()

	// Check if the elapsed_seconds column already exists
	var count int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('saved_results')
		WHERE name = 'elapsed_seconds'
	`).Scan(&count)
	if err != nil {
		log.Fatalf("Failed to check if elapsed_seconds column exists: %v", err)
	}

	if count > 0 {
		fmt.Println("elapsed_seconds column already exists in saved_results table")
		return
	}

	// Add the elapsed_seconds column to the saved_results table
	_, err = db.Exec(`
		ALTER TABLE saved_results
		ADD COLUMN elapsed_seconds INTEGER DEFAULT 0
	`)
	if err != nil {
		log.Fatalf("Failed to add elapsed_seconds column: %v", err)
	}

	fmt.Println("Added elapsed_seconds column to saved_results table")
	fmt.Println("Migration completed successfully.")
}
