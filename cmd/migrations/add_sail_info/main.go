package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "modernc.org/sqlite"
)

func main() {
	// Open the database
	db, err := sql.Open("sqlite", "segling.db")
	if err != nil {
		log.Fatalf("Failed to open database: %v", err)
	}
	defer db.Close()

	// Check if the segelnummer column already exists in srs_matbrev
	var count int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('srs_matbrev')
		WHERE name = 'segelnummer'
	`).Scan(&count)
	if err != nil {
		log.Fatalf("Failed to check if segelnummer column exists in srs_matbrev: %v", err)
	}

	if count == 0 {
		// Add the segelnummer column to the srs_matbrev table
		_, err = db.Exec(`
			ALTER TABLE srs_matbrev
			ADD COLUMN segelnummer TEXT
		`)
		if err != nil {
			log.Fatalf("Failed to add segelnummer column to srs_matbrev: %v", err)
		}
		fmt.Println("Added segelnummer column to srs_matbrev table")
	} else {
		fmt.Println("segelnummer column already exists in srs_matbrev table")
	}

	// Check if the nationality column already exists in srs_matbrev
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('srs_matbrev')
		WHERE name = 'nationality'
	`).Scan(&count)
	if err != nil {
		log.Fatalf("Failed to check if nationality column exists in srs_matbrev: %v", err)
	}

	if count == 0 {
		// Add the nationality column to the srs_matbrev table
		_, err = db.Exec(`
			ALTER TABLE srs_matbrev
			ADD COLUMN nationality TEXT DEFAULT 'SWE'
		`)
		if err != nil {
			log.Fatalf("Failed to add nationality column to srs_matbrev: %v", err)
		}
		fmt.Println("Added nationality column to srs_matbrev table")
	} else {
		fmt.Println("nationality column already exists in srs_matbrev table")
	}

	// Check if the segelnummer column already exists in boats
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('boats')
		WHERE name = 'segelnummer'
	`).Scan(&count)
	if err != nil {
		log.Fatalf("Failed to check if segelnummer column exists in boats: %v", err)
	}

	if count == 0 {
		// Add the segelnummer column to the boats table
		_, err = db.Exec(`
			ALTER TABLE boats
			ADD COLUMN segelnummer TEXT
		`)
		if err != nil {
			log.Fatalf("Failed to add segelnummer column to boats: %v", err)
		}
		fmt.Println("Added segelnummer column to boats table")
	} else {
		fmt.Println("segelnummer column already exists in boats table")
	}

	// Check if the nationality column already exists in boats
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('boats')
		WHERE name = 'nationality'
	`).Scan(&count)
	if err != nil {
		log.Fatalf("Failed to check if nationality column exists in boats: %v", err)
	}

	if count == 0 {
		// Add the nationality column to the boats table
		_, err = db.Exec(`
			ALTER TABLE boats
			ADD COLUMN nationality TEXT DEFAULT 'SWE'
		`)
		if err != nil {
			log.Fatalf("Failed to add nationality column to boats: %v", err)
		}
		fmt.Println("Added nationality column to boats table")
	} else {
		fmt.Println("nationality column already exists in boats table")
	}

	fmt.Println("Migration completed successfully. Please run the SRS sync from the web interface to populate the sail numbers and nationalities.")
}
