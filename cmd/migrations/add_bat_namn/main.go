package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "modernc.org/sqlite"
)

func main() {
	// Open the database
	db, err := sql.Open("sqlite", "segling.db")
	if err != nil {
		log.Fatalf("Failed to open database: %v", err)
	}
	defer db.Close()

	// Check if the bat_namn column already exists
	var count int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('srs_matbrev')
		WHERE name = 'bat_namn'
	`).Scan(&count)
	if err != nil {
		log.Fatalf("Failed to check if bat_namn column exists: %v", err)
	}

	if count > 0 {
		fmt.Println("bat_namn column already exists in srs_matbrev table")
		return
	}

	// Add the bat_namn column to the srs_matbrev table
	_, err = db.Exec(`
		ALTER TABLE srs_matbrev
		ADD COLUMN bat_namn TEXT
	`)
	if err != nil {
		log.Fatalf("Failed to add bat_namn column: %v", err)
	}
	fmt.Println("Added bat_namn column to srs_matbrev table")

	fmt.Println("Migration completed successfully. Please run the SRS sync from the web interface to populate the boat names.")
}
