package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// Open the database
	db, err := sql.Open("sqlite3", "../../../segling.db")
	if err != nil {
		log.Fatalf("Failed to open database: %v", err)
	}
	defer db.Close()

	// Check if the dns column already exists in event_participants
	var count int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'dns'
	`).Scan(&count)
	if err != nil {
		log.Fatalf("Failed to check if dns column exists: %v", err)
	}

	if count == 0 {
		// Add the dns column to the event_participants table
		_, err = db.Exec(`
			ALTER TABLE event_participants
			ADD COLUMN dns BOOLEAN DEFAULT 0
		`)
		if err != nil {
			log.Fatalf("Failed to add dns column to event_participants: %v", err)
		}
		fmt.Println("Added dns column to event_participants table")
	} else {
		fmt.Println("dns column already exists in event_participants table")
	}

	// Check if the dnf column already exists in event_participants
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'dnf'
	`).Scan(&count)
	if err != nil {
		log.Fatalf("Failed to check if dnf column exists: %v", err)
	}

	if count == 0 {
		// Add the dnf column to the event_participants table
		_, err = db.Exec(`
			ALTER TABLE event_participants
			ADD COLUMN dnf BOOLEAN DEFAULT 0
		`)
		if err != nil {
			log.Fatalf("Failed to add dnf column to event_participants: %v", err)
		}
		fmt.Println("Added dnf column to event_participants table")
	} else {
		fmt.Println("dnf column already exists in event_participants table")
	}

	// Check if the saved_results table exists
	err = db.QueryRow(`
		SELECT COUNT(*) FROM sqlite_master
		WHERE type='table' AND name='saved_results'
	`).Scan(&count)
	if err != nil {
		log.Fatalf("Failed to check if saved_results table exists: %v", err)
	}

	if count > 0 {
		// Check if the dns column already exists in saved_results
		err = db.QueryRow(`
			SELECT COUNT(*) FROM pragma_table_info('saved_results')
			WHERE name = 'dns'
		`).Scan(&count)
		if err != nil {
			log.Fatalf("Failed to check if dns column exists in saved_results: %v", err)
		}

		if count == 0 {
			// Add the dns column to the saved_results table
			_, err = db.Exec(`
				ALTER TABLE saved_results
				ADD COLUMN dns BOOLEAN DEFAULT 0
			`)
			if err != nil {
				log.Fatalf("Failed to add dns column to saved_results: %v", err)
			}
			fmt.Println("Added dns column to saved_results table")
		} else {
			fmt.Println("dns column already exists in saved_results table")
		}

		// Check if the dnf column already exists in saved_results
		err = db.QueryRow(`
			SELECT COUNT(*) FROM pragma_table_info('saved_results')
			WHERE name = 'dnf'
		`).Scan(&count)
		if err != nil {
			log.Fatalf("Failed to check if dnf column exists in saved_results: %v", err)
		}

		if count == 0 {
			// Add the dnf column to the saved_results table
			_, err = db.Exec(`
				ALTER TABLE saved_results
				ADD COLUMN dnf BOOLEAN DEFAULT 0
			`)
			if err != nil {
				log.Fatalf("Failed to add dnf column to saved_results: %v", err)
			}
			fmt.Println("Added dnf column to saved_results table")
		} else {
			fmt.Println("dnf column already exists in saved_results table")
		}
	}

	fmt.Println("Migration completed successfully.")
}
