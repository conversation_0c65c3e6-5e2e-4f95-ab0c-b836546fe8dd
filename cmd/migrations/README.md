# Database Migrations

This directory contains migration scripts for updating the database schema. Each migration is a separate Go program that can be run independently.

## Available Migrations

- **add_bat_namn**: Adds the `bat_namn` column to the `srs_matbrev` table
- **add_klubb**: Adds the `klubb` column to the `sailors` table with a default value of "LSS"
- **add_sail_info**: Adds `segelnummer` and `nationality` columns to both the `srs_matbrev` and `boats` tables
- **add_elapsed_seconds**: Adds the `elapsed_seconds` column to the `saved_results` table for tiebreaker sorting

## Running Migrations

To run a migration, navigate to the specific migration directory and run the Go program:

```bash
# Example: Run the add_bat_namn migration
cd cmd/migrations/add_bat_namn
go run main.go
```

Each migration will:
1. Check if the columns already exist
2. Add the columns if they don't exist
3. Print a message indicating the result

## Notes

- Migrations are idempotent - running them multiple times will not cause issues
- Some migrations may require additional steps after running, such as syncing SRS data
- The database file (`segling.db`) should be in the current working directory when running migrations
