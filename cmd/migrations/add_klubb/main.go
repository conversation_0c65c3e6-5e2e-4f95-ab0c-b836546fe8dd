package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "modernc.org/sqlite"
)

func main() {
	// Open the database
	db, err := sql.Open("sqlite", "segling.db")
	if err != nil {
		log.Fatalf("Failed to open database: %v", err)
	}
	defer db.Close()

	// Check if the klubb column already exists in sailors
	var count int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('sailors')
		WHERE name = 'klubb'
	`).Scan(&count)
	if err != nil {
		log.Fatalf("Failed to check if klubb column exists in sailors: %v", err)
	}

	if count == 0 {
		// Add the klubb column to the sailors table with default value "LSS"
		_, err = db.Exec(`
			ALTER TABLE sailors
			ADD COLUMN klubb TEXT DEFAULT 'LSS'
		`)
		if err != nil {
			log.Fatalf("Failed to add klubb column to sailors: %v", err)
		}
		fmt.Println("Added klubb column to sailors table with default value 'LSS'")
	} else {
		fmt.Println("klubb column already exists in sailors table")
	}

	fmt.Println("Migration completed successfully.")
}
