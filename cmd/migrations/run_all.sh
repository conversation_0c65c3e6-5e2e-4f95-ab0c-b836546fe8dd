#!/bin/bash

# Run all migrations in sequence

echo "Running all database migrations..."

# Store the current directory
CURRENT_DIR=$(pwd)

# Navigate to each migration directory and run the migration
cd "$(dirname "$0")/add_bat_namn" || exit
echo "Running add_bat_namn migration..."
go run main.go

cd "$CURRENT_DIR/$(dirname "$0")/add_klubb" || exit
echo "Running add_klubb migration..."
go run main.go

cd "$CURRENT_DIR/$(dirname "$0")/add_sail_info" || exit
echo "Running add_sail_info migration..."
go run main.go

cd "$CURRENT_DIR/$(dirname "$0")/add_elapsed_seconds" || exit
echo "Running add_elapsed_seconds migration..."
go run main.go

cd "$CURRENT_DIR" || exit
echo "All migrations completed."
