@echo off
REM Run all migrations in sequence

echo Running all database migrations...

REM Store the current directory
set CURRENT_DIR=%CD%

REM Navigate to each migration directory and run the migration
cd "%~dp0\add_bat_namn"
echo Running add_bat_namn migration...
go run main.go

cd "%CURRENT_DIR%\%~dp0\add_klubb"
echo Running add_klubb migration...
go run main.go

cd "%CURRENT_DIR%\%~dp0\add_sail_info"
echo Running add_sail_info migration...
go run main.go

cd "%CURRENT_DIR%\%~dp0\add_elapsed_seconds"
echo Running add_elapsed_seconds migration...
go run main.go

cd "%CURRENT_DIR%"
echo All migrations completed.
