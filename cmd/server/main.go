package main

import (
	"crypto/tls"
	"crypto/x509"
	"flag"
	"html/template"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rbjoregren/segling/pkg/database"
	"github.com/rbjoregren/segling/pkg/handlers"
)

// Version information - will be set during build via -ldflags
var Version = "dev"

func main() {
	// Define database flag
	var dbPath string
	flag.StringVar(&dbPath, "db", "segling.db", "Database file path")

	// Define project root flag
	var projectRoot string
	flag.StringVar(&projectRoot, "root", ".", "Project root directory")

	// Define port flag
	var port string
	flag.StringVar(&port, "port", "", "Port to listen on")

	// Define TLS flags
	var enableTLS bool
	flag.BoolVar(&enableTLS, "tls", false, "Enable TLS/HTTPS")

	var certFile string
	flag.StringVar(&certFile, "cert", "cert.pem", "TLS certificate file path")

	var keyFile string
	flag.StringVar(&keyFile, "key", "key.pem", "TLS key file path")

	// Define client certificate authentication flags
	var clientCAFile string
	flag.StringVar(&clientCAFile, "client-ca", "", "Client CA certificate file path for mutual TLS (optional)")

	var requireClientCert bool
	flag.BoolVar(&requireClientCert, "require-client-cert", false, "Require client certificate authentication")

	flag.Parse()

	// Set up the database
	db, err := database.New(dbPath)
	if err != nil {
		log.Fatalf("Error connecting to database: %v", err)
	}
	defer db.Close()

	// Initialize the database
	if err := db.Initialize(); err != nil {
		log.Fatalf("Error initializing database: %v", err)
	}

	// Create the handler
	handler := handlers.NewHandler(db)

	// Set the application version
	handler.SetVersion(Version)

	// Set up the router
	router := gin.Default()

	// Register routes
	handler.RegisterRoutes(router)

	// Add custom template functions
	router.SetFuncMap(template.FuncMap{
		"add": func(a, b int) int {
			return a + b
		},
		"now": func() time.Time {
			return time.Now()
		},
		"split": strings.Split,
		"isDiscardEnabled": func(event interface{}) bool {
			if e, ok := event.(map[string]interface{}); ok {
				if discardAfterHeats, exists := e["DiscardAfterHeats"]; exists {
					if val, ok := discardAfterHeats.(int); ok {
						return val > 0
					}
				}
			}
			return false
		},
		"getDiscardCount": func(event interface{}, totalHeats int) int {
			if e, ok := event.(map[string]interface{}); ok {
				if discardAfterHeats, exists := e["DiscardAfterHeats"]; exists {
					if val, ok := discardAfterHeats.(int); ok && val > 0 {
						if totalHeats >= val {
							return totalHeats / val
						}
					}
				}
			}
			return 0
		},
	})

	// If projectRoot is empty, use the current working directory
	if projectRoot == "." {
		cwd, err := os.Getwd()
		if err != nil {
			log.Fatalf("Error getting current working directory: %v", err)
		}
		projectRoot = cwd
	}

	// Load templates
	templatesPath := filepath.Join(projectRoot, "pkg", "templates", "*")
	log.Printf("Loading templates from: %s", templatesPath)
	router.LoadHTMLGlob(templatesPath)

	// Serve static files
	staticDir := filepath.Join(projectRoot, "static")
	log.Printf("Serving static files from: %s", staticDir)
	router.Static("/static", staticDir)

	// Serve favicon.ico directly from static directory
	router.GET("/favicon.ico", func(c *gin.Context) {
		c.File(filepath.Join(staticDir, "favicon.png"))
	})

	// Create static directory if it doesn't exist
	if _, err := os.Stat(staticDir); os.IsNotExist(err) {
		if err := os.MkdirAll(staticDir, 0755); err != nil {
			log.Fatalf("Error creating static directory: %v", err)
		}

		// Create CSS directory
		if err := os.MkdirAll(filepath.Join(staticDir, "css"), 0755); err != nil {
			log.Fatalf("Error creating CSS directory: %v", err)
		}

		// Create JS directory
		if err := os.MkdirAll(filepath.Join(staticDir, "js"), 0755); err != nil {
			log.Fatalf("Error creating JS directory: %v", err)
		}
	}

	// Check if favicon.png exists and copy it to favicon.ico if needed
	faviconPng := filepath.Join(staticDir, "favicon.png")
	faviconIco := filepath.Join(staticDir, "favicon.ico")
	if _, err := os.Stat(faviconPng); err == nil {
		// favicon.png exists, check if favicon.ico exists
		if _, err := os.Stat(faviconIco); os.IsNotExist(err) {
			// favicon.ico doesn't exist, copy favicon.png to favicon.ico
			log.Printf("Copying %s to %s", faviconPng, faviconIco)
			source, err := os.Open(faviconPng)
			if err != nil {
				log.Printf("Warning: Could not open favicon.png: %v", err)
			} else {
				defer source.Close()

				destination, err := os.Create(faviconIco)
				if err != nil {
					log.Printf("Warning: Could not create favicon.ico: %v", err)
				} else {
					defer destination.Close()

					_, err = io.Copy(destination, source)
					if err != nil {
						log.Printf("Warning: Could not copy favicon.png to favicon.ico: %v", err)
					} else {
						log.Printf("Successfully copied favicon.png to favicon.ico")
					}
				}
			}
		}
	}

	// Get port from environment variable if not specified via flag
	if port == "" {
		port = os.Getenv("PORT")
		if port == "" {
			port = "8080"
		}
	}

	// Set server configuration in handler for dynamic callback URL generation
	handler.SetServerConfig("localhost", port, enableTLS)

	// Start the server
	if enableTLS {
		// Configure TLS
		tlsConfig := &tls.Config{
			MinVersion: tls.VersionTLS12,
			CipherSuites: []uint16{
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			},
		}

		// Configure client certificate authentication if requested
		if clientCAFile != "" || requireClientCert {
			// Load client CA certificate
			if clientCAFile == "" {
				log.Fatalf("Error: -client-ca flag is required when -require-client-cert is set")
			}

			clientCAPool := x509.NewCertPool()
			clientCAPEM, err := os.ReadFile(clientCAFile)
			if err != nil {
				log.Fatalf("Error reading client CA certificate: %v", err)
			}

			if !clientCAPool.AppendCertsFromPEM(clientCAPEM) {
				log.Fatalf("Error parsing client CA certificate")
			}

			// Set client certificate verification options
			tlsConfig.ClientCAs = clientCAPool

			if requireClientCert {
				tlsConfig.ClientAuth = tls.RequireAndVerifyClientCert
				log.Printf("Client certificate authentication is required")
			} else {
				tlsConfig.ClientAuth = tls.VerifyClientCertIfGiven
				log.Printf("Client certificate authentication is optional")
			}

			log.Printf("Using client CA certificate: %s", clientCAFile)
		}

		server := &http.Server{
			Addr:      ":" + port,
			Handler:   router,
			TLSConfig: tlsConfig,
		}

		log.Printf("Starting secure server on https://localhost:%s", port)
		log.Printf("Using TLS certificate: %s", certFile)
		log.Printf("Using TLS key: %s", keyFile)

		if err := server.ListenAndServeTLS(certFile, keyFile); err != nil {
			log.Fatalf("Error starting secure server: %v", err)
		}
	} else {
		log.Printf("Starting server on http://localhost:%s", port)
		log.Printf("WARNING: Running in insecure mode. Use --tls flag to enable HTTPS.")
		if err := router.Run(":" + port); err != nil {
			log.Fatalf("Error starting server: %v", err)
		}
	}
}
