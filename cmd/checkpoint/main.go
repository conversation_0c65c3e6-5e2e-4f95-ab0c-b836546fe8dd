package main

import (
	"database/sql"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// Define flags
	var dbPath string
	var verbose bool
	var help bool

	flag.StringVar(&dbPath, "db", "segling.db", "Path to the SQLite database file")
	flag.BoolVar(&verbose, "v", false, "Verbose output")
	flag.BoolVar(&help, "h", false, "Show help")
	flag.Parse()

	if help {
		fmt.Println("SQLite WAL Checkpoint Utility")
		fmt.Println()
		fmt.Println("Usage:")
		fmt.Printf("  %s [options]\n", os.Args[0])
		fmt.Println()
		fmt.Println("Options:")
		flag.PrintDefaults()
		fmt.Println()
		fmt.Println("Examples:")
		fmt.Printf("  %s                    # Checkpoint segling.db in current directory\n", os.Args[0])
		fmt.Printf("  %s -db mydb.db        # Checkpoint specific database file\n", os.Args[0])
		fmt.Printf("  %s -v                 # Verbose output\n", os.Args[0])
		fmt.Println()
		fmt.Println("This utility performs a WAL checkpoint to flush all changes from")
		fmt.Println("the Write-Ahead Log (WAL) file to the main database file.")
		return
	}

	// Check if database file exists
	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		log.Fatalf("Database file does not exist: %s", dbPath)
	}

	// Get absolute path for better error messages
	absPath, err := filepath.Abs(dbPath)
	if err != nil {
		log.Fatalf("Error getting absolute path: %v", err)
	}

	if verbose {
		fmt.Printf("Database file: %s\n", absPath)
	}

	// Check for WAL files before checkpoint
	walFile := dbPath + "-wal"
	shmFile := dbPath + "-shm"
	
	walExists := false
	shmExists := false
	
	if _, err := os.Stat(walFile); err == nil {
		walExists = true
		if verbose {
			if stat, err := os.Stat(walFile); err == nil {
				fmt.Printf("WAL file exists: %s (size: %d bytes)\n", walFile, stat.Size())
			}
		}
	}
	
	if _, err := os.Stat(shmFile); err == nil {
		shmExists = true
		if verbose {
			if stat, err := os.Stat(shmFile); err == nil {
				fmt.Printf("SHM file exists: %s (size: %d bytes)\n", shmFile, stat.Size())
			}
		}
	}

	if !walExists && !shmExists {
		if verbose {
			fmt.Println("No WAL or SHM files found - database is already synchronized")
		} else {
			fmt.Println("Database already synchronized (no WAL files)")
		}
		return
	}

	// Open database connection
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		log.Fatalf("Error opening database: %v", err)
	}
	defer db.Close()

	// Test connection
	if err := db.Ping(); err != nil {
		log.Fatalf("Error connecting to database: %v", err)
	}

	if verbose {
		fmt.Println("Connected to database successfully")
		fmt.Println("Performing WAL checkpoint...")
	}

	// Perform WAL checkpoint
	var result1, result2, result3 int
	err = db.QueryRow("PRAGMA wal_checkpoint(FULL)").Scan(&result1, &result2, &result3)
	if err != nil {
		log.Fatalf("Error performing WAL checkpoint: %v", err)
	}

	// Interpret results
	// result1: 0 = success, 1 = error
	// result2: number of pages in WAL file before checkpoint
	// result3: number of pages in WAL file after checkpoint
	
	if result1 != 0 {
		log.Fatalf("WAL checkpoint failed with code: %d", result1)
	}

	if verbose {
		fmt.Printf("WAL checkpoint completed successfully\n")
		fmt.Printf("Pages checkpointed: %d\n", result2)
		fmt.Printf("Pages remaining in WAL: %d\n", result3)
	} else {
		if result2 > 0 {
			fmt.Printf("✓ Checkpoint completed: %d pages synchronized to main database\n", result2)
		} else {
			fmt.Println("✓ Checkpoint completed: no changes to synchronize")
		}
	}

	// Check WAL files after checkpoint
	if verbose {
		fmt.Println("\nChecking files after checkpoint:")
		
		if stat, err := os.Stat(dbPath); err == nil {
			fmt.Printf("Main DB: %s (size: %d bytes)\n", dbPath, stat.Size())
		}
		
		if stat, err := os.Stat(walFile); err == nil {
			fmt.Printf("WAL file: %s (size: %d bytes)\n", walFile, stat.Size())
		} else {
			fmt.Printf("WAL file: %s (removed)\n", walFile)
		}
		
		if stat, err := os.Stat(shmFile); err == nil {
			fmt.Printf("SHM file: %s (size: %d bytes)\n", shmFile, stat.Size())
		} else {
			fmt.Printf("SHM file: %s (removed)\n", shmFile)
		}
	}
}
