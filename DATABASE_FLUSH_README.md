# Database Flush Utilities

This directory contains utilities to flush SQLite WAL (Write-Ahead Log) changes to the main database file.

## Why Do You Need This?

SQLite uses WAL mode for better performance. Changes are written to `.db-wal` and `.db-shm` files first, and only periodically synchronized to the main `.db` file. This means:

- **<PERSON><PERSON> doesn't see recent changes** until they're flushed to the main database file
- **Backups might be incomplete** if they only copy the main `.db` file
- **Database appears unchanged** even after making modifications

## Available Tools

### 1. Cross-Platform Go Utility (`checkpoint`)

A standalone Go program that works on all platforms:

```bash
# Build the utility (only needed once)
go build -o checkpoint ./cmd/checkpoint

# Basic usage
./checkpoint

# Specify database file
./checkpoint -db mydb.db

# Verbose output
./checkpoint -v

# Show help
./checkpoint -h
```

### 2. Unix/Linux/macOS Script (`flush-db.sh`)

Convenient bash script that automatically builds and runs the Go utility:

```bash
# Make executable (only needed once)
chmod +x flush-db.sh

# Basic usage
./flush-db.sh

# Specify database file
./flush-db.sh -db mydb.db

# Verbose output
./flush-db.sh -v

# Show help
./flush-db.sh -h
```

### 3. Windows Script (`flush-db.bat`)

Batch script for Windows that automatically builds and runs the Go utility:

```cmd
REM Basic usage
flush-db.bat

REM Specify database file
flush-db.bat -db mydb.db

REM Verbose output
flush-db.bat -v

REM Show help
flush-db.bat -h
```

## Manual Method (No Go Required)

If you have SQLite command-line tool installed:

```bash
# Unix/Linux/macOS
sqlite3 segling.db "PRAGMA wal_checkpoint(FULL);"

# Windows
sqlite3.exe segling.db "PRAGMA wal_checkpoint(FULL);"
```

## When to Use

### Before Git Operations
```bash
# Flush database changes
./flush-db.sh

# Now Git can see the changes
git status
git add segling.db
git commit -m "Updated database"
```

### Before Creating Backups
```bash
# Ensure backup contains all changes
./flush-db.sh
cp segling.db backup/segling_backup.db
```

### After Heavy Database Activity
```bash
# After importing data, updating many records, etc.
./flush-db.sh -v
```

## Output Examples

### When Changes Need Flushing
```
$ ./flush-db.sh -v
Database file: /path/to/segling.db
WAL file exists: segling.db-wal (size: 8272 bytes)
SHM file exists: segling.db-shm (size: 32768 bytes)
Connected to database successfully
Performing WAL checkpoint...
WAL checkpoint completed successfully
Pages checkpointed: 4
Pages remaining in WAL: 0

Checking files after checkpoint:
Main DB: segling.db (size: 421888 bytes)
WAL file: segling.db-wal (removed)
SHM file: segling.db-shm (removed)

Database flush completed!
You can now run 'git status' to see database changes.
```

### When Database Already Synchronized
```
$ ./flush-db.sh
Flushing database: segling.db
Database already synchronized (no WAL files)

Database flush completed!
You can now run 'git status' to see database changes.
```

## Requirements

- **Go 1.16+** (for building the utilities)
- **SQLite3** (included in Go build)

## Files Created

- `cmd/checkpoint/main.go` - Go source code for the checkpoint utility
- `checkpoint` (Unix) / `checkpoint.exe` (Windows) - Compiled binary
- `flush-db.sh` - Unix/Linux/macOS script
- `flush-db.bat` - Windows batch script

## Integration with Application

The sailing application also has built-in checkpoint functionality:

1. **Settings Page**: "Synkronisera databas" button for manual checkpointing
2. **Automatic**: Checkpoints before creating event backups
3. **API Endpoint**: `POST /settings/checkpoint-wal` for programmatic access

These command-line utilities provide an alternative for automation, CI/CD, or when the web application isn't running.
