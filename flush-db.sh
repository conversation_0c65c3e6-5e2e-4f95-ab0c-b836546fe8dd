#!/bin/bash

# SQLite WAL Checkpoint Script for Unix/Linux/macOS
# This script flushes the WAL (Write-Ahead Log) to the main database file

set -e  # Exit on any error

# Default values
DB_PATH="segling.db"
VERBOSE=false
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CHECKPOINT_BINARY="$SCRIPT_DIR/checkpoint"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -db|--database)
            DB_PATH="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            echo "SQLite WAL Checkpoint Script"
            echo ""
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  -db, --database PATH    Database file path (default: segling.db)"
            echo "  -v, --verbose          Verbose output"
            echo "  -h, --help             Show this help"
            echo ""
            echo "Examples:"
            echo "  $0                     # Checkpoint segling.db"
            echo "  $0 -db mydb.db         # Checkpoint specific database"
            echo "  $0 -v                  # Verbose output"
            echo ""
            echo "This script flushes all changes from the SQLite WAL file"
            echo "to the main database file, making changes visible to Git."
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

# Check if checkpoint binary exists, if not try to build it
if [[ ! -f "$CHECKPOINT_BINARY" ]]; then
    echo "Checkpoint utility not found. Building..."
    
    # Check if Go is installed
    if ! command -v go &> /dev/null; then
        echo "Error: Go is not installed. Please install Go or use the manual method:"
        echo "  sqlite3 $DB_PATH \"PRAGMA wal_checkpoint(FULL);\""
        exit 1
    fi
    
    # Build the checkpoint utility
    echo "Building checkpoint utility..."
    cd "$SCRIPT_DIR"
    go build -o checkpoint ./cmd/checkpoint
    
    if [[ ! -f "$CHECKPOINT_BINARY" ]]; then
        echo "Error: Failed to build checkpoint utility"
        exit 1
    fi
    
    echo "✓ Checkpoint utility built successfully"
fi

# Prepare arguments for the checkpoint utility
ARGS=("-db" "$DB_PATH")
if [[ "$VERBOSE" == true ]]; then
    ARGS+=("-v")
fi

# Run the checkpoint utility
echo "Flushing database: $DB_PATH"
"$CHECKPOINT_BINARY" "${ARGS[@]}"

echo ""
echo "Database flush completed!"
echo "You can now run 'git status' to see database changes."
